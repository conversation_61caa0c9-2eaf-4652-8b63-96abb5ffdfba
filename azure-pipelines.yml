
trigger:
  branches:
    include:
      - master

variables: 
  - group: 'selfinvite-mobile'

pool:
  vmImage: 'macos-latest'

steps:

  - checkout: self
    persistCredentials: true
    clean: true

  - task: NodeTool@0
    displayName: 'Install Node'
    inputs:
      versionSpec: '18.14.0' # you can use your desired version here

  - script: yarn install
    displayName: Install Dependencies

  - script: |
      # Disable autocommit on version bump 
      yarn config set version-sign-git-tag false
      yarn config set version-git-tag false
      yarn config set version-commit-hooks false
      # Checkout branch where the build is triggered
      git checkout $(Build.SourceBranchName)
      # Extract existing version of package.json
      oldVer=$(jq -r ".version" package.json)
      # Bump version
      yarn version --patch
      # Add bumped version to staging
      git add *
      # Extract new version of package.json
      newVer=$(jq -r ".version" package.json)
      # Set environment variables
      echo "##vso[task.setvariable variable=OLD_VERSION]$oldVer"
      echo "##vso[task.setvariable variable=NEW_VERSION]$newVer"
    displayName: 'Bump version and set variables'

# https://marketplace.visualstudio.com/items?itemName=vs-publisher-473885.motz-mobile-buildtasks
  - task: ios-bundle-version@1
    displayName: 'Bump iOS version'
    inputs:
      sourcePath: 'ios/MyProject/Info.plist'
      versionCodeOption: 'buildid'
      versionCode: '$(Build.BuildId)'
      versionName: '$(NEW_VERSION)'
      printFile: false

  - task: Gradle@2
    displayName: 'Build APK'
    inputs:
      gradleWrapperFile: 'android/gradlew'
      workingDirectory: 'android/'
      options: '-PversionName=$(NEW_VERSION) -PversionCode=$(Build.BuildId)'
      tasks: 'assembleRelease'
      publishJUnitResults: false
      javaHomeOption: 'JDKVersion'
      jdkVersionOption: '1.8'
      gradleOptions: '-Xmx3072m'
      sonarQubeRunAnalysis: false

  - task: AndroidSigning@3
    displayName: 'Sign APK'
    inputs:
      apkFiles: 'android/app/build/outputs/apk/release/*.apk'
      apksignerKeystoreFile: 'SelfInvite.bak.jks'
      apksignerKeystorePassword: '$(AndroidStorePassword)'
      apksignerKeystoreAlias: '$(AndroidKeyAlias)'
      apksignerKeyPassword: '$(AndroidKeyPassword)'
      zipalign: true

  - task: PublishBuildArtifacts@1
    displayName: 'Publish APK to artifacts'
    inputs:
      PathtoPublish: 'android/app/build/outputs/apk/release'
      ArtifactName: 'android'
      publishLocation: 'Container'

  # https://learn.microsoft.com/en-us/azure/devops/pipelines/ecosystems/android?view=azure-devops
  # https://damienaicheh.github.io/azure/devops/2021/10/25/configure-azure-devops-google-play-en.html
  - task: GooglePlayRelease@4
    inputs:
      authType: 'ServiceEndpoint'
      serviceEndpoint: 'google-play-console'
      apkFile: 'android/app/build/outputs/apk/release/app-release-unsigned.apk'
      track: 'production'
      userFraction: 1.0
      updatePriority: 0
      shouldAttachMetadata: false
      shouldUploadApks: true

  - script: |
      tag="mobile_$(NEW_VERSION)"
      echo "New tag $tag"
      git add *
      git commit -m "Update version from $(OLD_VERSION) to $(NEW_VERSION)"
      git tag $tag
      git pull --rebase origin $(Build.SourceBranchName)
      git push origin $(Build.SourceBranchName)
      git push --tags
    displayName: Bump commit

  - task: InstallAppleCertificate@2
    displayName: Install Apple Certificate
    inputs:
      certSecureFile: 'SigningCertificate.p12'
      certPwd: '$(AppleCertificatePassword)'
      keychain: 'temp'
      deleteCert: true

  - task: InstallAppleProvisioningProfile@1
    displayName: 'Install Apple Provisioning Profile'
    inputs:
      provisioningProfileLocation: 'secureFiles'
      provProfileSecureFile: 'comjautoinvito_AppStore.mobileprovision'
      removeProfile: true

  - task: CocoaPods@0
    displayName: 'Install CocoaPods'
    inputs:
      workingDirectory: 'ios'

  - task: Xcode@5
    displayName: 'Build IPA'
    inputs:
      actions: 'build'
      configuration: 'Release'
      sdk: 'iphoneos'
      xcWorkspacePath: 'ios/selfinvite.xcworkspace'
      scheme: 'selfinvite'
      packageApp: true
      exportPath: 'output'
      signingOption: 'manual'
      signingIdentity: '$(APPLE_CERTIFICATE_SIGNING_IDENTITY)'
      provisioningProfileUuid: '$(APPLE_PROV_PROFILE_UUID)'

  - task: CopyFiles@2
    displayName: 'Copy IPA'
    inputs:
      contents: '**/*.ipa'
      targetFolder: '$(build.artifactStagingDirectory)'
      overWrite: true
      flattenFolders: true

  - task: PublishBuildArtifacts@1
    displayName: 'Publish IPA to artifacts'
    inputs:
      PathtoPublish: '$(build.artifactStagingDirectory)'
      ArtifactName: 'ios'
      publishLocation: 'Container'

# https://marketplace.visualstudio.com/items?itemName=ms-vsclient.app-store
  - task : AppStoreRelease@1
    displayName: 'Upload IPA to App Store'
    inputs:
      authType: 'serviceEndpoint'
      serviceEndpoint: 'app-store-connect'
      ipaPath: '$(build.artifactStagingDirectory)/*.ipa'
      appIdentifier: 'com.jautoinvito'
      appSpecificPassword: '$(AppleCertificatePassword)'
      bundleId: 'com.j.autoinvito'
      releaseTrack: 'TestFlight'
      releaseNotes: 'Test Flight Release'
      shouldSkipSubmission: false
      shouldSkipWaitingForProcessing: false
  #  TASK to move it to Test Flight
  # - task: AppCenterDistribute@3
  #   displayName: 'Upload IPA to AppCenter'
  #   inputs:
  #     serverEndpoint: 'App Center'
  #     appSlug: 'hnadeem/MyProject-iOS'
  #     appFile: 'output/MyProject.ipa'
  #     releaseNotesOption: 'file'
  #     isMandatory: true
  #     destinationType: 'groups'
  #     distributionGroupId: '058a4704-ea24-4877-a2f0-bdfaff9335dc'
  #     isSilent: true