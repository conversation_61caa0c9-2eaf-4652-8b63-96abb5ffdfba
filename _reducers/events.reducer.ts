import { createReducer, createActions } from 'reduxsauce';
import Immutable from 'seamless-immutable';

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  eventRequest: ['eventId'],
  eventEditRequest: ['eventId'],
  eventAllRequest: ['options'],
  eventUpdateRequest: ['event'],
  eventSearchRequest: ['query'],
  eventDeleteRequest: ['eventId'],

  eventSuccess: ['event'],
  eventEditSuccess: ['eventEdit'],
  eventAllSuccess: ['events'],
  eventUpdateSuccess: ['event'],
  eventSearchSuccess: ['events'],
  eventDeleteSuccess: [],

  eventFailure: ['error'],
  eventEditFailure: ['event'],
  eventAllFailure: ['error'],
  eventUpdateFailure: ['error'],
  eventSearchFailure: ['error'],
  eventDeleteFailure: ['error'],
  logoutSuccess: null,
});

export const EventTypes = Types;
export default Creators;
export type EVENT_TYPE = typeof EventTypes;
export type EVENT_STATE_TYPE = typeof INITIAL_STATE;

/* ------------- Initial State ------------- */

export const INITIAL_STATE = Immutable({
  fetchingOne: null,
  fetchingAll: null,
  fetchingEdit: null,
  updating: null,
  searching: null,
  deleting: null,
  event: null,
  eventEdit: null,
  events: [],
  errorOne: null,
  errorAll: null,
  errorEdit: null,
  errorUpdating: null,
  errorSearching: null,
  errorDeleting: null,
});

/* ------------- Reducers ------------- */

// request the data from an api
export const request = state =>
  state.merge({
    fetchingOne: true,
    event: null,
  });

// request the data from an api
export const editRequest = state =>
  state.merge({
    fetchingEdit: true,
    eventEdit: null,
  });

// request the data from an api
export const allRequest = state =>
  state.merge({
    fetchingAll: true,
    events: [],
  });

// request to update from an api
export const updateRequest = state =>
  state.merge({
    updating: true,
  });
// request to delete from an api
export const deleteRequest = state =>
  state.merge({
    deleting: true,
  });

// successful api lookup for single entity
export const success = (state, action) => {
  const { event } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: null,
    event,
  });
};
// successful api lookup for single entity
export const editSuccess = (state, action) => {
  const { eventEdit } = action;
  // console.log('editSuccess eventEdit', eventEdit);
  const typesEs = eventEdit.typeEs.map(a => a.name);
  const typesBs = eventEdit.typeBs.map(a => a.name);
  const typesKs = eventEdit.typeKs.map(a => a.name);
  const typesLs = eventEdit.typeLs.map(a => a.name);
  const intolerances = eventEdit.intolerances.map(a => a.name);

  eventEdit.typeEs = typesEs;
  eventEdit.typeKs = typesKs;
  eventEdit.typeBs = typesBs;
  eventEdit.intolerances = intolerances;
  eventEdit.typeLs = typesLs;
  eventEdit.tags = []; //re calculated on save
  return state.merge({
    fetchingEdit: false,
    errorEdit: null,
    eventEdit,
  });
};

// successful api lookup for all entities
export const allSuccess = (state, action) => {
  const { events } = action;
  return state.merge({
    fetchingAll: false,
    errorAll: null,
    events,
  });
};

// successful api update
export const updateSuccess = (state, action) => {
  const { event } = action;
  return state.merge({
    updating: false,
    errorUpdating: null,
    event,
  });
};
// successful api delete
export const deleteSuccess = state => {
  return state.merge({
    deleting: false,
    errorDeleting: null,
    event: null,
  });
};

// Something went wrong fetching a single entity.
export const failure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: error,
    event: null,
  });
};
// Something went wrong fetching a single entity.
export const editFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingEdit: false,
    errorEdit: error,
    eventEdit: null,
  });
};
// Something went wrong fetching all entities.
export const allFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingAll: false,
    errorAll: error,
    events: [],
  });
};
// Something went wrong updating.
export const updateFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updating: false,
    errorUpdating: error,
    event: state.event,
  });
};
// Something went wrong deleting.
export const deleteFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    deleting: false,
    errorDeleting: error,
    event: state.event,
  });
};

// we've logged out
export const eventReset = state => INITIAL_STATE;

/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.EVENT_REQUEST]: request,
  [Types.EVENT_EDIT_REQUEST]: editRequest,
  [Types.EVENT_ALL_REQUEST]: allRequest,
  [Types.EVENT_UPDATE_REQUEST]: updateRequest,
  [Types.EVENT_DELETE_REQUEST]: deleteRequest,

  [Types.EVENT_SUCCESS]: success,
  [Types.EVENT_EDIT_SUCCESS]: editSuccess,
  [Types.EVENT_ALL_SUCCESS]: allSuccess,
  [Types.EVENT_UPDATE_SUCCESS]: updateSuccess,
  [Types.EVENT_DELETE_SUCCESS]: deleteSuccess,

  [Types.EVENT_FAILURE]: failure,
  [Types.EVENT_EDIT_FAILURE]: editFailure,
  [Types.EVENT_ALL_FAILURE]: allFailure,
  [Types.EVENT_UPDATE_FAILURE]: updateFailure,
  [Types.EVENT_DELETE_FAILURE]: deleteFailure,

  [Types.LOGOUT_SUCCESS]: eventReset,
});
