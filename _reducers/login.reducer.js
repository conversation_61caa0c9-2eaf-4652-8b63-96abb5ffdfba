import { createReducer, createActions } from 'reduxsauce';
import Immutable from 'seamless-immutable';
import { agent } from '@services';
/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  refreshToken: ['credentials'],
  loginSocialRequest: ['credentials', 'authToken'],
  loginSocialSuccess: ['jwt'],
  loginSocialFailure: ['error'],
  logoutRequest: null,
  logoutSuccess: null,
  loginLoadSuccess: [],
});

export const LoginTypes = Types;
export default Creators;

/* ------------- Initial State ------------- */

export const INITIAL_STATE = Immutable({
  authToken: null,
  error: null,
  fetching: false,
  loading: false,
});

/* ------------- Reducers ------------- */

// we're attempting to login
export const request = (state, data) => {
  const {
    credentials: { idToken },
  } = data;
  agent.setAuthToken(idToken);
  console.group('Social Request');
  console.log('social request data', data);
  console.log('social request state', state);
  console.groupEnd();
  return state.merge({ fetching: true, authToken: idToken });
};

// we've successfully logged in
export const success = (state, data) => {
  console.group('Social Request');
  console.log('social success ', data);
  console.groupEnd();
  return state.merge({ fetching: false, error: null });
};

// we've had a problem logging in
export const failure = (state, { error }) => 
  state.merge({ fetching: false, error, authToken: null });

// we need to logout, meaning clear access tokens and account
export const logoutRequest = state => state;

// we've logged out
export const logoutSuccess = state => INITIAL_STATE;

/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.REFRESH_TOKEN]: request,
  [Types.LOGIN_SOCIAL_REQUEST]: request,
  [Types.LOGIN_SOCIAL_SUCCESS]: success,
  [Types.LOGIN_SOCIAL_FAILURE]: failure,
  [Types.LOGOUT_REQUEST]: logoutRequest,
  [Types.LOGOUT_SUCCESS]: logoutSuccess,
});

/* ------------- Selectors ------------- */
