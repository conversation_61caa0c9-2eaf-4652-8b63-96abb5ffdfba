import {
  reducer as paymentReducer,
  PaymentTypes,
  INITIAL_STATE,
} from './payment.reducer'; // Adjust the import path as necessary

describe('Payment Reducer', () => {
  it('should return the initial state', () => {
    expect(paymentReducer(undefined, {})).toEqual(INITIAL_STATE);
  });

  it('should handle PAYMENT_UPDATE_REQUEST', () => {
    const startAction = {
      type: PaymentTypes.PAYMENT_UPDATE_REQUEST,
    };
    // it's empty on purpose because it's just starting to fetch
    expect(paymentReducer({}, startAction)).toEqual(INITIAL_STATE);
  });

  it('should handle PAYMENT_SUCCESS', () => {
    const successAction = {
      type: PaymentTypes.PAYMENT_SUCCESS,
      payload: { id: '123', status: 'success' }, // Mock payload
    };
    expect(paymentReducer({}, successAction)).toEqual({
      id: '123',
      status: 'success',
    });
  });

  it('should handle PAYMENT_FAILURE', () => {
    const failureAction = {
      type: PaymentTypes.PAYMENT_FAILURE,
      payload: { error: 'Failed to process payment' }, // Mock payload
    };
    expect(paymentReducer({}, failureAction)).toEqual({
      error: 'Failed to process payment',
    });
  });

  it('should handle LOGOUT_SUCCESS', () => {
    const logoutAction = {
      type: PaymentTypes.LOGOUT_SUCCESS,
    };
    // Assuming paymentReset clears the payment state
    expect(paymentReducer({}, logoutAction)).toEqual(INITIAL_STATE);
  });

  // Add more tests for other action types...
});
