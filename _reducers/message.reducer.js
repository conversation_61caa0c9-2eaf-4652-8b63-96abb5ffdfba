import { createReducer, createActions } from 'reduxsauce';
import Immutable from 'seamless-immutable';

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  appendConversationMessage: ['message'],
  appendMessageMessages: ['message'],
  messageReadRequest: ['threadId'],
  messageRequest: ['messageId'],
  messageAllRequest: ['options'],
  messageUpdateRequest: ['message'],
  messageDeleteRequest: ['messageId'],
  messageByThreadRequest: ['threadId'],
  appendUnreadMessageDrawer: ['message'],

  messageSuccess: ['message'],
  messageReadSuccess: [],
  messageAllSuccess: ['messages'],
  messageUpdateSuccess: ['message'],
  messageDeleteSuccess: ['message'],
  messageByThreadSuccess: ['conversation'],
  appendUnreadMessageDrawerSuccess: ['message'],

  messageFailure: ['error'],
  messageReadFailure: ['error'],
  messageAllFailure: ['error'],
  messageUpdateFailure: ['error'],
  messageDeleteFailure: ['error'],
  messageByThreadFailure: ['error'],
  logoutSuccess: null,
});

export const MessageTypes = Types;
export default Creators;

/* ------------- Initial State ------------- */

export const INITIAL_STATE = Immutable({
  fetchingThread: null,
  fetchingOne: null,
  fetchingAll: null,
  fetchingRead: null,
  updating: null,
  deleting: null,
  message: null,
  messages: [],
  conversation: [],
  errorOne: null,
  errorAll: null,
  errorUpdating: null,
  errorDeleting: null,
  errorThread: null,
  errorRead: null,
  unreadedMessages: [],
});

/* ------------- Reducers ------------- */

// the message will be added to the current conversation
export const appendMessageInConversation = (state, action) => {
  console.log('appendMessageInConversation conversation: ', state.conversation);
  const { message } = action;
  const { conversation } = state;
  return state.merge({
    conversation: [message, ...conversation],
  });
};

// the message will be added to the current conversation
export const appendMessageInMessages = (state, action) => {
  const { message } = action;
  const { messages } = state;
  //the Message object is aside other possible objects
  // console.log('action>>>: ', action);
  // console.log('message>>>: ', message);
  // console.log('messages>>>: ', messages);
  message.data.badge = true;
  const indexToRemove = messages.findIndex(e => e.thread === message.thread);
  const cleanArray = messages.filter((_, i) => i !== indexToRemove);
  return state.merge({
    messages: [message.data, ...cleanArray],
  });
};

// request the data from an api
export const request = state =>
  state.merge({
    fetchingOne: true,
    message: null,
  });
// request the data from an api
export const readRequest = state =>
  state.merge({
    fetchingRead: true,
  });

// request the data from an api
export const allRequest = state =>
  state.merge({
    fetchingAll: true,
    messages: [],
  });

// request to update from an api
export const updateRequest = state =>
  state.merge({
    updating: true,
  });
// request to delete from an api
export const deleteRequest = state =>
  state.merge({
    deleting: true,
  });
// request to get the conversation from an api
export const byThreadRequest = state =>
  state.merge({
    fetchingThread: true,
    conversation: [],
  });
export const appendUnreadMessageDrawer = (state, action) => {
  const { message } = action;
  const { unreadedMessages } = state;
  console.log('appendUnreadMessageDrawer>>>> action', action);
  console.log(
    'appendUnreadMessageDrawer>>>> unreadedMessages',
    unreadedMessages,
  );
  return state.merge({
    unreadedMessages: [...unreadedMessages, message.data],
  });
};

export const appendUnreadMessageDrawerSuccess = (state, action) => {
  return state.merge({
    unreadedMessages: [],
  });
};

// successful api lookup for single entity
export const success = (state, action) => {
  const { message } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: null,
    message,
  });
};
// successful api lookup for all entities
export const allSuccess = (state, action) => {
  const { messages } = action;

  return state.merge({
    fetchingAll: false,
    errorAll: null,
    messages,
  });
};
// successful api update
// the message will be added to the current conversation
export const updateSuccess = (state, action) => {
  const { message } = action;
  console.log('updateSuccess conversation: ', state.conversation);
  console.log('updateSuccess message: ', message);
  const { conversation } = state;
  return state.merge({
    updating: false,
    errorUpdating: null,
    message,
    conversation: [message, ...conversation],
  });
};

// successful api delete
// the message will be deleted to the current conversation
export const deleteSuccess = (state, action) => {
  const { message } = action;
  const { conversation } = state;
  console.log('deleteSuccess conversation: ', state.conversation);
  const indexToRemove = conversation.findIndex(e => e.id === message.id);

  return state.merge({
    deleting: false,
    errorDeleting: null,
    message: null,
    conversation: conversation.filter((_, i) => i !== indexToRemove),
  });
};
// successful api get conversation
export const byThreadSuccess = (state, action) => {
  console.log('byThreadSuccess conversation: ', action.conversation);
  console.log('byThreadSuccess state: ', state);
  const { conversation } = action;

  return state.merge({
    fetchingThread: false,
    errorThread: null,
    conversation,
  });
};
// successful api read message
export const readSuccess = state => {
  //it marks the last message in messages as read

  return state.merge({
    fetchingRead: true,
    errorRead: null,
  });
};

// Something went wrong fetching a single entity.
export const failure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: error,
    message: null,
  });
};
// Something went wrong fetching all entities.
export const allFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingAll: false,
    errorAll: error,
    messages: [],
  });
};
// Something went wrong updating.
export const updateFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updating: false,
    errorUpdating: error,
    message: state.message,
  });
};
// Something went wrong deleting.
export const deleteFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    deleting: false,
    errorDeleting: error,
    message: state.message,
  });
};
// Something went wrong searching the entities.
export const byThreadFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingThread: false,
    errorThread: error,
    conversation: [],
  });
};

// Something went wrong fetching a single entity.
export const readFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingRead: false,
    errorRead: error,
  });
};

// we've logged out
export const messageReset = state => INITIAL_STATE;

/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.APPEND_CONVERSATION_MESSAGE]: appendMessageInConversation,
  [Types.APPEND_MESSAGE_MESSAGES]: appendMessageInMessages,
  [Types.MESSAGE_REQUEST]: request,
  [Types.MESSAGE_READ_REQUEST]: readRequest,
  [Types.MESSAGE_ALL_REQUEST]: allRequest,
  [Types.MESSAGE_UPDATE_REQUEST]: updateRequest,
  [Types.MESSAGE_DELETE_REQUEST]: deleteRequest,
  [Types.MESSAGE_BY_THREAD_REQUEST]: byThreadRequest,
  [Types.APPEND_UNREAD_MESSAGE_DRAWER]: appendUnreadMessageDrawer,

  [Types.MESSAGE_SUCCESS]: success,
  [Types.MESSAGE_READ_SUCCESS]: readSuccess,
  [Types.MESSAGE_ALL_SUCCESS]: allSuccess,
  [Types.MESSAGE_UPDATE_SUCCESS]: updateSuccess,
  [Types.MESSAGE_DELETE_SUCCESS]: deleteSuccess,
  [Types.MESSAGE_BY_THREAD_SUCCESS]: byThreadSuccess,
  [Types.APPEND_UNREAD_MESSAGE_DRAWER_SUCCESS]:
    appendUnreadMessageDrawerSuccess,

  [Types.MESSAGE_FAILURE]: failure,
  [Types.MESSAGE_READ_FAILURE]: readFailure,
  [Types.MESSAGE_ALL_FAILURE]: allFailure,
  [Types.MESSAGE_UPDATE_FAILURE]: updateFailure,
  [Types.MESSAGE_DELETE_FAILURE]: deleteFailure,
  [Types.MESSAGE_BY_THREAD_FAILURE]: byThreadFailure,

  [Types.LOGOUT_SUCCESS]: messageReset,
});
