import { createReducer, createActions } from 'reduxsauce';
import Immutable from 'seamless-immutable';

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  cleanupPaymentObjects: [],

  paymentTokenRequest: ['eventOfferKvId'],
  paymentRequest: ['paymentId'],
  paymentBankRequest: ['bankAccount'],
  paymentKycUploadRequest: ['documents'],
  paymentKycStatusRequest: ['kycDocument'],
  paymentStatusRequest: ['transactionId'],
  paymentAllRequest: ['options'],
  paymentUpdateRequest: ['payment'],
  paymentDeleteRequest: ['paymentId'],
  paymentKycManagedFlowRequest: null,

  cleanupPaymentSuccess: [],
  paymentTokenSuccess: ['paymentIntent'],
  paymentSuccess: ['payment'],
  paymentBankSuccess: ['bankAccount'],
  paymentKycUploadSuccess: ['kycDocument'],
  paymentKycStatusSuccess: ['userWallet'],
  paymentStatusSuccess: ['paymentStatus'],
  paymentAllSuccess: ['payments'],
  paymentUpdateSuccess: ['payment'],
  paymentDeleteSuccess: [],
  paymentKycManagedFlowSuccess: ['kycManagedFlow'],
  paymentKycManagedFlowCleanup: [],

  paymentTokenFailure: ['error'],
  paymentFailure: ['error'],
  paymentBankFailure: ['error'],
  paymentKycUploadFailure: ['error'],
  paymentKycStatusFailure: ['error'],
  paymentStatusFailure: ['error'],
  paymentAllFailure: ['error'],
  paymentUpdateFailure: ['error'],
  paymentKycManagedFlowFailure: ['error'],
  paymentDeleteFailure: ['error'],
  logoutSuccess: null,
});

export const PaymentTypes = Types;
export default Creators;

/* ------------- Initial State ------------- */

export const INITIAL_STATE = Immutable({
  fetchingToken: null,
  fetchingOne: null,
  fetchingAll: null,
  updatingPayment: null,
  updatingBankAccount: null,
  updatePaymentStatus: null,
  deleting: null,
  paymentIntent: null,
  eventOfferKvId: null,
  payment: null,
  payments: [],
  updatingKyc: null,
  kycDocument: null,
  kycManagedFlowLink: null,
  kycManagedFlowFetching: null,
  userWallet: null,
  bankAccount: null,
  paymentStatus: null,
  errorPaymentStatus: null,
  errorOne: null,
  errorAll: null,
  errorKyc: null,
  errorUpdating: null,
  errorUpdatingBankAccount: null,
  errorDeleting: null,
  errorToken: null,
  kycManagedFlowError: null,
});

/* ------------- Reducers ------------- */
export const cleanupPaymentObjects = state =>
  state.merge({
    paymentIntent: null,
    errorToken: null,
    fetchngToken: false,
    eventOfferKvId: null,
    fetchingToken: false,
    paymentStatus: null,
  });

// request the client token from an api
export const tokenRequest = (state, action) => {
  const { eventOfferKvId } = action;
  console.log('tokenRequest fetchingToken', true);
  return state.merge({
    fetchingToken: true,
    paymentIntent: null,
    eventOfferKvId,
  });
};

// request the upload kyc document
export const uploadKycRequest = state =>
  state.merge({
    updatingKyc: true,
    kycDocument: null,
  });

// request the kyc status
export const kycStatusRequest = state =>
  state.merge({
    updatingKyc: true,
    userWallet: null,
  });

// request the data from an api
export const request = state =>
  state.merge({
    fetchingOne: true,
    payment: null,
  });
// request to upload bank account
export const bankRequest = state =>
  state.merge({
    updatingBankAccount: true,
    bankAccount: null,
  });
// request to get the transaction status
export const statusRequest = state =>
  state.merge({
    updatePaymentStatus: true,
    paymentStatus: null,
  });

// request the data from an api
export const allRequest = state =>
  state.merge({
    fetchingAll: true,
    payments: [],
  });

// request to update from an api
export const updateRequest = state =>
  state.merge({
    updatingPayment: true,
  });

export const kycManagedFlowRequest = state =>
  state.merge({
    kycManagedFlowFetching: true,
  });

// request to delete from an api
export const deleteRequest = state =>
  state.merge({
    deleting: true,
  });

// successful api token retrieve
export const tokenSuccess = (state, action) => {
  console.log('getPaymentToken tokenSuccess', action);
  return state.merge({
    fetchingToken: false,
    errorOne: null,
    errorToken: null,
    paymentIntent: { ...action.paymentIntent },
  });
};

// successful kyc document upload
export const uploadKycSuccess = (state, action) => {
  const { kycDocument } = action;
  console.log('uploadKycSuccess>>', kycDocument);
  return state.merge({
    updatingKyc: false,
    errorKyc: null,
    kycDocument: kycDocument,
  });
};
// successful kyc document status retrieved
export const kycStatusSuccess = (state, action) => {
  const { userWallet } = action;
  return state.merge({
    updatingKyc: false,
    errorKyc: null,
    userWallet: userWallet,
  });
};

// successful api lookup for single entity
export const success = (state, action) => {
  const { payment } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: null,
    payment,
  });
};
// successful api lookup for single entity
export const bankSuccess = (state, action) => {
  const { bankAccount } = action;
  return state.merge({
    updatingBankAccount: false,
    errorUpdatingBankAccount: null,
    bankAccount,
  });
};

// successful transaction lookup for single entity
export const statusSuccess = (state, action) => {
  const { paymentStatus } = action;
  return state.merge({
    updatePaymentStatus: false,
    errorPaymentStatus: null,
    paymentStatus,
  });
};

// successful api lookup for all entities
export const allSuccess = (state, action) => {
  const { payments } = action;
  return state.merge({
    fetchingAll: false,
    errorAll: null,
    payments,
  });
};
// successful api update
export const updateSuccess = (state, action) => {
  const { payment } = action;
  return state.merge({
    updatingPayment: false,
    errorUpdating: null,
    payment,
  });
};

export const KycManagedFlowCleanup = state =>
  state.merge({
    kycManagedFlowLink: null,
    kycManagedFlowFetching: false,
    kycManagedFlowError: null,
  });

export const kycManagedFlowSuccess = (state, action) => {
  const { kycManagedFlow } = action;
  console.log('kycManagedFlow success', kycManagedFlow);
  return state.merge({
    kycManagedFlowFetching: false,
    kycManagedFlowError: null,
    kycManagedFlowLink: kycManagedFlow.url,
  });
};
// successful api delete
export const deleteSuccess = state => {
  return state.merge({
    deleting: false,
    errorDeleting: null,
    payment: null,
  });
};

// Something went wrong fetching a client token.
export const tokenFailure = (state, action) => {
  const { error } = action;
  console.log('tokenFailure fetchingToken', action);
  return state.merge({
    fetchingToken: false,
    errorToken: error,
    paymentIntent: null,
  });
};
// Something went wrong uploading kyc document
export const uploadKycFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updatingKyc: false,
    errorKyc: error,
    kycDocument: null,
  });
};
// Something went wrong requesting kyc document status
export const kycStatusFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updatingKyc: false,
    errorKyc: error,
    userWallet: null,
  });
};

// Something went wrong fetching a single entity.
export const failure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: error,
    payment: null,
  });
};

// Something went wrong fetching the transaction status.
export const statusFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updatePaymentStatus: false,
    errorPaymentStatus: error,
    paymentStatus: null,
  });
};

// Something went wrong updating bank account
export const bankFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updatingBankAccount: false,
    errorUpdatingBankAccount: error,
    bankAccount: null,
  });
};

// Something went wrong fetching all entities.
export const allFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingAll: false,
    errorAll: error,
    payments: [],
  });
};
// Something went wrong updatingPayment.
export const updateFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updatingPayment: false,
    errorUpdating: error,
    payment: state.payment,
  });
};

export const kycManagedFlowFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    kycManagedFlowFetching: false,
    kycManagedFlowError: error,
    kycManagedFlowLink: null,
  });
};
// Something went wrong deleting.
export const deleteFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    deleting: false,
    errorDeleting: error,
    payment: state.payment,
  });
};

// we've logged out
export const paymentReset = state => INITIAL_STATE;
/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.PAYMENT_TOKEN_REQUEST]: tokenRequest,
  [Types.PAYMENT_KYC_UPLOAD_REQUEST]: uploadKycRequest,
  [Types.PAYMENT_KYC_STATUS_REQUEST]: kycStatusRequest,
  [Types.PAYMENT_REQUEST]: request,
  [Types.PAYMENT_BANK_REQUEST]: bankRequest,
  [Types.PAYMENT_STATUS_REQUEST]: statusRequest,
  [Types.PAYMENT_ALL_REQUEST]: allRequest,
  [Types.PAYMENT_UPDATE_REQUEST]: updateRequest,
  [Types.PAYMENT_KYC_MANAGED_FLOW_REQUEST]: kycManagedFlowRequest,

  [Types.CLEANUP_PAYMENT_OBJECTS]: cleanupPaymentObjects,
  [Types.PAYMENT_TOKEN_SUCCESS]: tokenSuccess,
  [Types.PAYMENT_KYC_UPLOAD_SUCCESS]: uploadKycSuccess,
  [Types.PAYMENT_KYC_STATUS_SUCCESS]: kycStatusSuccess,
  [Types.PAYMENT_SUCCESS]: success,
  [Types.PAYMENT_STATUS_SUCCESS]: statusSuccess,
  [Types.PAYMENT_BANK_SUCCESS]: bankSuccess,
  [Types.PAYMENT_ALL_SUCCESS]: allSuccess,
  [Types.PAYMENT_UPDATE_SUCCESS]: updateSuccess,
  [Types.PAYMENT_KYC_MANAGED_FLOW_SUCCESS]: kycManagedFlowSuccess,
  [Types.PAYMENT_KYC_MANAGED_FLOW_CLEANUP]: KycManagedFlowCleanup,

  [Types.PAYMENT_TOKEN_FAILURE]: tokenFailure,
  [Types.PAYMENT_KYC_UPLOAD_FAILURE]: uploadKycFailure,
  [Types.PAYMENT_KYC_STATUS_FAILURE]: kycStatusFailure,
  [Types.PAYMENT_FAILURE]: failure,
  [Types.PAYMENT_STATUS_FAILURE]: statusFailure,
  [Types.PAYMENT_BANK_FAILURE]: bankFailure,
  [Types.PAYMENT_ALL_FAILURE]: allFailure,
  [Types.PAYMENT_UPDATE_FAILURE]: updateFailure,
  [Types.PAYMENT_KYC_MANAGED_FLOW_FAILURE]: kycManagedFlowFailure,

  [Types.LOGOUT_SUCCESS]: paymentReset,
});
