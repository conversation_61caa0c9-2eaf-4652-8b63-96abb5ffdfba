import { createReducer, createActions } from 'reduxsauce';
import Immutable from 'seamless-immutable';
import { EVENT_OFFER_KV_STATE_GUEST_PAID } from '@constants/constants';

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  eventKvRequest: ['eventKvId'],
  eventKvAllByIdRequest: ['eventId'],
  eventKvAllRxRequest: ['options'],
  eventKvAllTxRequest: ['page'],
  eventKvUpdateRequest: ['eventKv'],
  eventKvReportRequest: ['eventKv'],
  eventKvDeleteRequest: ['eventKvId'],

  eventKvSuccess: ['eventKv'],
  eventKvReportSuccess: ['eventKvReport'],
  eventKvAllByIdSuccess: ['eventKvs'],
  eventKvAllRxSuccess: ['eventKvRxRequests'],
  eventKvAllTxSuccess: ['eventKvTxRequests'],
  eventKvUpdateSuccess: ['eventKv'],
  eventKvDeleteSuccess: [],

  eventKvFailure: ['error'],
  eventKvReportFailure: ['error'],
  eventKvAllByIdFailure: ['error'],
  eventKvAllRxFailure: ['error'],
  eventKvAllTxFailure: ['error'],
  eventKvUpdateFailure: ['error'],
  eventKvDeleteFailure: ['error'],
  logoutSuccess: null,
});

export const EventKVTypes = Types;
export default Creators;

/* ------------- Initial State ------------- */

export const INITIAL_STATE = Immutable({
  fetchingOne: null,
  fetchingAll: null,
  fetchingAllRxRequest: null,
  fetchingAllTxRequest: null,
  updating: null,
  report: null,
  deleting: null,
  eventKv: null,
  eventKvReport: null,
  eventKvs: [],
  eventKvRxRequests: [],
  eventKvTxRequests: [],
  errorOne: null,
  errorAll: null,
  errorAllRxRequest: null,
  errorAllTxRequest: null,
  errorUpdating: null,
  errorReporting: null,
  errorDeleting: null,
  page: 1,
});

/* ------------- Reducers ------------- */

// request the data from an api
export const request = state =>
  state.merge({
    fetchingOne: true,
    eventKv: null,
  });

// request the data from an api
export const allEventByIdRequest = state =>
  state.merge({
    fetchingAll: true,
    eventKvs: [],
  });
// request the data from an api
export const allEventRxRequest = state =>
  state.merge({
    fetchingAllRxRequest: true,
    eventKvRxRequests: [],
  });

// request the data from an api
export const allEventTxRequest = (state, action) => {
  const { page = 1 } = action;
  return state.merge({
    fetchingAllTxRequest: true,
    page,
    eventKvTxRequests: page === 0 ? [] : state.eventKvTxRequests,
  });
};

// request to update from an api
export const updateRequest = state =>
  state.merge({
    updating: true,
  });

export const reportRequest = state =>
  state.merge({
    report: true,
  });

// request to delete from an api
export const deleteRequest = state =>
  state.merge({
    deleting: true,
  });

// successful api lookup for single entity
export const success = (state, action) => {
  const { eventKv } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: null,
    eventKv,
  });
};
// successful api lookup for all entities
export const allEventByIdSuccess = (state, action) => {
  const { eventKvs } = action;
  return state.merge({
    fetchingAll: false,
    errorAll: null,
    eventKvs,
  });
};
// successful api lookup for all entities
export const allEventRxSuccess = (state, action) => {
  const { eventKvRxRequests } = action;
  return state.merge({
    fetchingAllRxRequest: false,
    errorAllRxRequest: null,
    eventKvRxRequests,
  });
};
// successful api lookup for all entities
export const allEventTxSuccess = (state, action) => {
  const { eventKvTxRequests } = action;
  return state.merge({
    fetchingAllTxRequest: false,
    errorAllTxRequest: null,
    eventKvTxRequests: [...state.eventKvTxRequests, ...eventKvTxRequests],
  });
};
// successful api update
export const updateSuccess = (state, action) => {
  const { eventKv } = action;
  return state.merge({
    updating: false,
    errorUpdating: null,
    eventKv,
  });
};

// successful api update
export const reportSuccess = (state, action) => {
  const { eventKvReport } = action;
  return state.merge({
    report: false,
    errorReporting: null,
    eventKvReport,
  });
};

// successful api delete
export const deleteSuccess = state => {
  return state.merge({
    deleting: false,
    errorDeleting: null,
    eventKv: null,
  });
};

// Something went wrong fetching a single entity.
export const failure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: error,
    eventKv: null,
  });
};
// Something went wrong fetching all entities.
export const allEventByIdFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingAll: false,
    errorAll: error,
    eventKvs: [],
  });
};
// Something went wrong fetching all entities.
export const allEventRxFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingAllRxRequest: false,
    errorAllRxRequest: error,
    eventKvRxRequests: [],
  });
};
// Something went wrong fetching all entities.
export const allEventTxFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingAllTxRequest: false,
    errorAllTxRequest: error,
    eventKvTxRequests: [],
  });
};
// Something went wrong updating.
export const updateFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updating: false,
    errorUpdating: error,
    eventKv: state.eventKv,
  });
};
// Something went wrong updating.
export const reportFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    report: false,
    errorReporting: error,
    eventKvReport: state.eventKvReport,
  });
};
// Something went wrong deleting.
export const deleteFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    deleting: false,
    errorDeleting: error,
    eventKv: state.eventKv,
  });
};

export const eventKvReset = state => INITIAL_STATE;

/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.EVENT_KV_REQUEST]: request,
  [Types.EVENT_KV_ALL_BY_ID_REQUEST]: allEventByIdRequest,
  [Types.EVENT_KV_ALL_RX_REQUEST]: allEventRxRequest,
  [Types.EVENT_KV_ALL_TX_REQUEST]: allEventTxRequest,
  [Types.EVENT_KV_UPDATE_REQUEST]: updateRequest,
  [Types.EVENT_KV_DELETE_REQUEST]: deleteRequest,

  [Types.EVENT_KV_REPORT_REQUEST]: reportRequest,
  [Types.EVENT_KV_REPORT_SUCCESS]: reportSuccess,
  [Types.EVENT_KV_REPORT_FAILURE]: reportFailure,

  [Types.EVENT_KV_SUCCESS]: success,
  [Types.EVENT_KV_ALL_BY_ID_SUCCESS]: allEventByIdSuccess,
  [Types.EVENT_KV_ALL_RX_SUCCESS]: allEventRxSuccess,
  [Types.EVENT_KV_ALL_TX_SUCCESS]: allEventTxSuccess,
  [Types.EVENT_KV_UPDATE_SUCCESS]: updateSuccess,
  [Types.EVENT_KV_DELETE_SUCCESS]: deleteSuccess,

  [Types.EVENT_KV_FAILURE]: failure,
  [Types.EVENT_KV_ALL_BY_ID_FAILURE]: allEventByIdFailure,
  [Types.EVENT_KV_ALL_RX_FAILURE]: allEventRxFailure,
  [Types.EVENT_KV_ALL_TX_FAILURE]: allEventTxFailure,
  [Types.EVENT_KV_UPDATE_FAILURE]: updateFailure,
  [Types.EVENT_KV_DELETE_FAILURE]: deleteFailure,

  [Types.LOGOUT_SUCCESS]: eventKvReset,
});

/* ------------- Selectors ------------- */
// Is the current user logged in?

export const getTotalParticipantsConfirmed = state => {
  // console.log('getTotalParticipantsConfirmed state', state)
  if (state.eventKvs !== null) {
    const eventKvsConfirmed = state.eventKvs.filter(
      ({ state }) => state === EVENT_OFFER_KV_STATE_GUEST_PAID,
    );
    if (eventKvsConfirmed.length > 0)
      return eventKvsConfirmed.reduce((a, b) => ({
        v: Number(a.v) + Number(b.v),
      })).v;
  }
  return 0;
};

export const getTotalParticipants = state =>
  state.eventKvs !== null && state.eventKvs.length > 0
    ? state.eventKvs.reduce((a, b) => ({ v: Number(a.v) + Number(b.v) })).v
    : 0;
