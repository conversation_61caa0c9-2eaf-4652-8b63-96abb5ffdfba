import { createReducer, createActions } from 'reduxsauce';
import Immutable, { ImmutableObject } from 'seamless-immutable';

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  eventSearchRequest: ['data'],
  eventSearchSuccess: ['response', 'page'],
  eventSearchFailure: ['error', 'page'],
  eventTagSearchRequest: ['tag'],
  eventTagSearchSuccess: ['events'],
  eventTagSearchFailure: ['error'],

  logoutSuccess: null,
});

export const SearchTypes = Types;
export type SEARCH_TYPE = typeof SearchTypes;
export type SEARCH_STATE_TYPE = typeof INITIAL_STATE;
export default Creators;

interface EventSearchFailureAction {
  error: any;
  page: number;
}

/* ------------- Initial State ------------- */

interface SearchState {
  page: number;
  searching: boolean | null;
  searchingPage: boolean | null;
  searchingTag: boolean | null;
  events: any[];
  totalCount: number;
  last: number;
  errorSearching: any;
  eventsTag: any[];
  errorSearchingTag: any;
}

export const INITIAL_STATE: ImmutableObject<SearchState> = Immutable({
  page: 0,
  searching: null,
  searchingPage: null,
  searchingTag: null,
  events: [],
  totalCount: 0,
  last: 0,
  errorSearching: null,
  eventsTag: [],
  errorSearchingTag: null,
});

/* ------------- Reducers ------------- */

// request to search from an api
export const searchRequest = (state: SEARCH_STATE_TYPE, action: SearchState) => {
  const { page } = action;
  const nextState: Partial<SearchState> = { page };
  if (page === 0) nextState.searching = true;
  else nextState.searchingPage = true;
  return state.merge(nextState);
};

// successful api search
export const searchSuccess = (state: SEARCH_STATE_TYPE, action: { response: any }) => {
  const { events } = state;
  const { response } = action;
  const currentPage = Number(response.page);
  const nextState: Partial<SearchState> = {
    errorSearching: null,
    page: currentPage,
    events:
      currentPage === 0 ? response.events : [...events, ...response.events],
    totalCount: Number(response.total),
    last: Number(response.last),
  };
  if (currentPage === 0) nextState.searching = false;
  else nextState.searchingPage = false;

  return state.merge(nextState);
};

// Something went wrong searching the entities.
export const searchFailure = (state: SEARCH_STATE_TYPE, action: EventSearchFailureAction) => {
  const { events } = state;
  const { error, page } = action;
  const currentPage = Number(page);

  let nextState: Partial<SearchState> = {
    errorSearching: error,
    page: currentPage,
    events: currentPage === 0 ? [] : [...events],
  };
  if (currentPage === 0) nextState.searching = false;
  else {
    nextState.searchingPage = false;
    nextState.searching = false;
  }

  return state.merge(nextState);
};

// request to search from an api with tag query
export const searchTagRequest = (state: SEARCH_STATE_TYPE) =>
  state.merge({
    searchingTag: true,
  });

// successful api search with tag query
export const searchTagSuccess = (state: SEARCH_STATE_TYPE, action: { events: any }) => {
  const { events } = action;
  const nextState: Partial<SearchState> = {
    searchingTag: false,
    errorSearchingTag: null,
    eventsTag: events.eventOffers.sort((a, b) => b.date - a.date),
  }
  return state.merge(nextState);
};
// Something went wrong searchingTag the entities with tag query
export const searchTagFailure = (state: SEARCH_STATE_TYPE, action: { error: any }) => {
  const { error } = action;
  return state.merge({
    searchingTag: false,
    errorSearchingTag: error,
    eventsTag: [],
  });
};
// we've logged out
// export const searchReset = state => INITIAL_STATE

/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.EVENT_SEARCH_REQUEST]: searchRequest,
  [Types.EVENT_SEARCH_SUCCESS]: searchSuccess,
  [Types.EVENT_SEARCH_FAILURE]: searchFailure,

  [Types.EVENT_TAG_SEARCH_REQUEST]: searchTagRequest,
  [Types.EVENT_TAG_SEARCH_SUCCESS]: searchTagSuccess,
  [Types.EVENT_TAG_SEARCH_FAILURE]: searchTagFailure,

  // the events are not reset from logout
  // [Types.LOGOUT_SUCCESS]: searchReset
});
