import { createReducer, createActions } from 'reduxsauce';
import Immutable, { ImmutableObject } from 'seamless-immutable';

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  feedbackStatsRequest: ['userId'],
  reviewStatsRequest: ['userId'],
  feedbackRequest: ['feedbackId'],
  feedbacksAllRequest: ['userId'],
  reviewsAllRequest: ['userId'],
  reviewUpdateRequest: ['review'],
  feedbackUpdateRequest: ['feedback'],
  feedbackDeleteRequest: ['feedbackId'],

  feedbackStatsSuccess: ['feedbackStats'],
  reviewStatsSuccess: ['reviewStats'],
  feedbackSuccess: ['feedback'],
  feedbacksAllSuccess: ['feedbacks'],
  reviewsAllSuccess: ['reviews'],
  feedbackUpdateSuccess: ['feedback'],
  reviewUpdateSuccess: ['review'],
  feedbackDeleteSuccess: [],

  feedbackStatsFailure: ['error'],
  reviewStatsFailure: ['error'],
  feedbackFailure: ['error'],
  feedbacksAllFailure: ['error'],
  reviewsAllFailure: ['error'],
  feedbackUpdateFailure: ['error'],
  reviewUpdateFailure: ['error'],
  feedbackDeleteFailure: ['error'],
  logoutSuccess: null,
});

export const FeedbackTypes = Types;
export default Creators;
export type FEEDBACK_TYPE = typeof FeedbackTypes;
export type FEEDBACK_STATE_TYPE = typeof INITIAL_STATE;

interface FeedbackState {
  fetchingOne: null | any;
  fetchingAllFeedbacks: null | any;
  fetchingAllReviews: null | any;
  fetchingStats: null | any;
  fetchingReviewStats: null | any;
  updating: null | any;
  deleting: null | any;
  feedback: null | any;
  review: null | any;
  reviews: any[];
  feedbacks: any[];
  feedbackStats: null | any;
  reviewStats: null | any;
  errorOne: null | any;
  errorAll: null | any;
  errorUpdating: null | any;
  errorDeleting: null | any;
  errorReviewStats: null | any;
  errorFeedbacksStats: null | any;
}
/* ------------- Initial State ------------- */

export const INITIAL_STATE: ImmutableObject<FeedbackState> = Immutable({
  fetchingOne: null,
  fetchingAllFeedbacks: null,
  fetchingAllReviews: null,
  fetchingStats: null,
  fetchingReviewStats: null,
  updating: null,
  deleting: null,
  feedback: null,
  review: null,
  reviews: [],
  feedbacks: [],
  feedbackStats: null,
  reviewStats: null,
  errorOne: null,
  errorAll: null,
  errorUpdating: null,
  errorDeleting: null,
  errorReviewStats: null,
  errorFeedbacksStats: null,
});

/* ------------- Reducers ------------- */

// request the data from an api
export const request = state =>
  state.merge({
    fetchingOne: true,
    feedback: null,
  });

// request the data from an api
export const reviewsAllRequest = state =>
  state.merge({
    fetchingAllReviews: true,
    reviews: [],
  });

// request the data from an api
export const feedbackStatsRequest = state =>
  state.merge({
    fetchingStats: true,
    feedbackStats: null,
  });
// request the data from an api
export const reviewStatsRequest = state =>
  state.merge({
    fetchingReviewStats: true,
    reviewStats: null,
  });

// request the data from an api
export const feedbacksAllRequest = state =>
  state.merge({
    fetchingAllFeedbacks: true,
    feedbacks: [],
  });
// request to update from an api
export const updateRequest = state =>
  state.merge({
    updating: true,
  });
export const reviewUpdateRequest = state =>
  state.merge({
    updating: true,
  });

// request to delete from an api
export const deleteRequest = state =>
  state.merge({
    deleting: true,
  });

// successful api lookup for single entity
export const success = (state, action) => {
  const { feedback } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: null,
    feedback,
  });
};
// successful api lookup for all entities
export const allReviewsSuccess = (state, action) => {
  const { reviews } = action;
  return state.merge({
    fetchingAllReviews: false,
    errorAll: null,
    reviews,
  });
};
// successful api lookup for all entities
export const allFeedbacksSuccess = (state, action) => {
  const { feedbacks } = action;
  return state.merge({
    fetchingAllFeedbacks: false,
    errorAll: null,
    feedbacks,
  });
};
// successful api lookup for all entities
export const feedbackStatsSuccess = (state, action) => {
  const { feedbackStats } = action;
  return state.merge({
    fetchingStats: false,
    errorFeedbacksStats: null,
    feedbackStats,
  });
};
// successful api lookup for all entities
export const reviewStatsSuccess = (state, action) => {
  const { reviewStats } = action;
  return state.merge({
    fetchingReviewStats: false,
    errorReviewStats: null,
    reviewStats,
  });
};

// successful api update
export const updateSuccess = (state, action) => {
  const { feedback } = action;
  return state.merge({
    updating: false,
    errorUpdating: null,
    feedback,
  });
};
export const reviewUpdateSuccess = (state, action) => {
  const { review } = action;
  return state.merge({
    updating: false,
    errorUpdating: null,
    review,
  });
};

// successful api delete
export const deleteSuccess = state => {
  return state.merge({
    deleting: false,
    errorDeleting: null,
    feedback: null,
  });
};

// Something went wrong fetching a single entity.
export const failure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingOne: false,
    errorOne: error,
    feedback: null,
  });
};
// Something went wrong fetching all entities.
export const allFeedbacksFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingAllFeedbacks: false,
    errorAll: error,
    feedbacks: [],
  });
};
// Something went wrong fetching all entities.
export const allReviewsFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingAllReviews: false,
    errorAll: error,
    reviews: [],
  });
};
// Something went wrong fetching all entities.
export const feedbackStatsFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingStats: false,
    errorFeedbacksStats: error,
    feedbackStats: [],
  });
};
// Something went wrong fetching all entities.
export const reviewStatsFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    fetchingReviewStats: false,
    errorReviewStats: error,
    reviewStats: [],
  });
};

// Something went wrong updating.
export const updateFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updating: false,
    errorUpdating: error,
    feedback: state.feedback,
  });
};
export const reviewUpdateFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    updating: false,
    errorUpdating: error,
    review: state.review,
  });
};

// Something went wrong deleting.
export const deleteFailure = (state, action) => {
  const { error } = action;
  return state.merge({
    deleting: false,
    errorDeleting: error,
    feedback: state.feedback,
  });
};

export const feedbacksReset = state => INITIAL_STATE;

/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.FEEDBACK_STATS_REQUEST]: feedbackStatsRequest,
  [Types.REVIEW_STATS_REQUEST]: reviewStatsRequest,
  [Types.FEEDBACK_REQUEST]: request,
  [Types.REVIEWS_ALL_REQUEST]: reviewsAllRequest,
  [Types.FEEDBACKS_ALL_REQUEST]: feedbacksAllRequest,
  [Types.FEEDBACK_UPDATE_REQUEST]: updateRequest,
  [Types.REVIEW_UPDATE_REQUEST]: reviewUpdateRequest,
  [Types.FEEDBACK_DELETE_REQUEST]: deleteRequest,

  [Types.FEEDBACK_STATS_SUCCESS]: feedbackStatsSuccess,
  [Types.REVIEW_STATS_SUCCESS]: reviewStatsSuccess,
  [Types.FEEDBACK_SUCCESS]: success,
  [Types.REVIEWS_ALL_SUCCESS]: allReviewsSuccess,
  [Types.FEEDBACKS_ALL_SUCCESS]: allFeedbacksSuccess,
  [Types.FEEDBACK_UPDATE_SUCCESS]: updateSuccess,
  [Types.REVIEW_UPDATE_SUCCESS]: reviewUpdateSuccess,
  [Types.FEEDBACK_DELETE_SUCCESS]: deleteSuccess,

  [Types.FEEDBACK_STATS_FAILURE]: feedbackStatsFailure,
  [Types.REVIEW_STATS_FAILURE]: reviewStatsFailure,
  [Types.FEEDBACK_FAILURE]: failure,
  [Types.REVIEWS_ALL_FAILURE]: allReviewsFailure,
  [Types.FEEDBACKS_ALL_FAILURE]: allFeedbacksFailure,
  [Types.FEEDBACK_UPDATE_FAILURE]: updateFailure,
  [Types.REVIEW_UPDATE_FAILURE]: reviewUpdateFailure,
  [Types.FEEDBACK_DELETE_FAILURE]: deleteFailure,

  [Types.LOGOUT_SUCCESS]: feedbacksReset,
});
