import { createReducer, createActions } from 'reduxsauce';
import Immutable from 'seamless-immutable';
import { PARAMETER_MAX_GUESTS } from '@constants';

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  updateImageRequest: ['image'],
  updateImageSuccess: ['image'],
  updateImageFailure: ['error'],
  userInfoRequest: ['userInfoId'],
  userInfoSuccess: ['userInfo'],
  userInfoFailure: ['error'],
  setPresentationRequest: ['presentation'],
  setPresentationSuccess: ['presentation'],
  setPresentationFailure: ['error'],
  configurationRequest: [''],
  configurationSuccess: ['configurations'],
  configurationFailure: ['error'],
  setUserInfoRequest: ['userInfoId'],
  setLoadReviewsRequest: ['status'],
  setLoadFeedbacksRequest: ['status'],
  setLoadUserEventsRequest: ['status'],
});

export const CommonTypes = Types;
export default Creators;

/* ------------- Initial State ------------- */

export const INITIAL_STATE = Immutable({
  image: null,
  error: null,
  uploading: false,
  presentation: null,
  userInfo: null,
  fetchUserInfo: false,
  errorUserInfo: null,
  errorPresentation: null,
  presentationUpdate: false,
  fetchingConfiguration: false,
  configurations: null,
  errorConfiguration: null,
  userInfoId: null,
  loadFeedbacks: false,
  loadUserEvents: false,
  loadReviews: false,
});

/* ------------- Reducers ------------- */
// Upload IMAGE
// we're attempting to set device token
export const updateImageRequest = state => state.merge({ uploading: true });
// we've successfully updated the device token
export const updateImageSuccess = (state, data) => {
  const { image } = data;
  return state.merge({ error: null, uploading: false, image });
};
// we've had a problem updating the device token
export const updateImageFailure = (state, { error }) =>
  state.merge({ uploading: false, error });

// getting userInfo
export const userInfoRequest = state => state.merge({ fetchUserInfo: true });
// we've successfully got user info
export const userInfoSuccess = (state, data) => {
  const { userInfo } = data;
  return state.merge({ errorUserInfo: null, fetchUserInfo: false, userInfo });
};

export const setUserInfoRequest = (state, data) => {
  const { userInfoId } = data;
  return state.merge({
    userInfoId,
    loadFeedbacks: true,
    loadReviews: true,
    loadUserEvents: true,
  });
};

export const setLoadReviewsRequest = (state, data) => {
  const { status } = data;
  return state.merge({ loadReviews: status });
};
export const setLoadFeedbacksRequest = (state, data) => {
  const { status } = data;
  return state.merge({ loadFeedbacks: status });
};
export const setLoadUserEventsRequest = (state, data) => {
  const { status } = data;
  return state.merge({ loadUserEvents: status });
};

// we've had a problem updating the device token
export const userInfoFailure = (state, { errorUserInfo }) =>
  state.merge({ fetchUserInfo: false, errorUserInfo });

// getting configuration
// we're attempting to get configuration
export const configurationRequest = state =>
  state.merge({ fetchingConfiguration: true });
// we've successfully got the configuration
export const configurationSuccess = (state, data) => {
  const { configurations } = data;
  return state.merge({
    errorConfiguration: null,
    fetchingConfiguration: false,
    configurations,
  });
};
// we've had a problem getting the configuration
export const configurationFailure = (state, { errorConfiguration }) =>
  state.merge({ fetchingConfiguration: false, errorConfiguration });

// Presentation
// we're attempting to set the presentation status
export const setPresentationRequest = (state, data) => {
  const { presentation } = data;
  return state.merge({ presentationUpdate: true, presentation });
};
// we've successfully updated the device token
export const setPresentationSuccess = (state, data) => {
  const { presentation } = data;
  return state.merge({
    errorPresentation: null,
    presentationUpdate: false,
    presentation,
  });
};
// we've had a problem updating the device token
export const setPresentationFailure = (state, { errorPresentation }) =>
  state.merge({ presentationUpdate: false, errorPresentation });

/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.USER_INFO_REQUEST]: userInfoRequest,
  [Types.USER_INFO_SUCCESS]: userInfoSuccess,
  [Types.USER_INFO_FAILURE]: userInfoFailure,
  [Types.UPDATE_IMAGE_REQUEST]: updateImageRequest,
  [Types.UPDATE_IMAGE_SUCCESS]: updateImageSuccess,
  [Types.UPDATE_IMAGE_FAILURE]: updateImageFailure,
  [Types.CONFIGURATION_REQUEST]: configurationRequest,
  [Types.CONFIGURATION_SUCCESS]: configurationSuccess,
  [Types.CONFIGURATION_FAILURE]: configurationFailure,
  [Types.SET_PRESENTATION_REQUEST]: setPresentationRequest,
  [Types.SET_PRESENTATION_SUCCESS]: setPresentationSuccess,
  [Types.SET_PRESENTATION_FAILURE]: setPresentationFailure,
  [Types.SET_USER_INFO_REQUEST]: setUserInfoRequest,
  [Types.SET_LOAD_REVIEWS_REQUEST]: setLoadReviewsRequest,
  [Types.SET_LOAD_FEEDBACKS_REQUEST]: setLoadFeedbacksRequest,
  [Types.SET_LOAD_USER_EVENTS_REQUEST]: setLoadUserEventsRequest,
});

/* ------------- Selectors ------------- */
// Is presentation enable?
export const isPresentationDisable = state => state.presentation;

export const getMaxGuestRequest = state => {
  if (
    state.configurations !== undefined &&
    state.configurations !== null &&
    state.configurations.length > 0
  ) {
    try {
      return Number(
        state.configurations.filter(e => e.k === PARAMETER_MAX_GUESTS)[0].v,
      );
    } catch (e) {
      console.warn('getMaxGuestRequest', e);
      return 2;
    }
  }
  return 2;
};
