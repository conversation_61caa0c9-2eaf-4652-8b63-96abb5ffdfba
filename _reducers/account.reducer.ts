import { createReducer, createActions } from 'reduxsauce';
import Immutable, { ImmutableObject } from 'seamless-immutable';

/* ------------- Types and Action Creators ------------- */

const { Types, Creators } = createActions({
  accountRequest: [],
  accountSuccess: ['account'], // used for get account and update account
  accountFailure: ['error'], // used for get account and update account
  accountUpdateRequest: ['account'],
  accountPreferencesRequest: [],
  accountPreferencesSuccess: ['preferences'],
  accountPreferencesFailure: ['error'],
  accountPreferencesUpdateRequest: ['preferences'],
  accountPreferencesUpdateSuccess: ['preferences'],
  accountPreferencesUpdateFailure: ['error'],
  addAccountPreferencesRequest: ['preferences'],
  addAccountPreferencesSuccess: ['preferences'],
  addAccountPreferencesFailure: ['error'],
  deviceTokenRequest: ['deviceToken'],
  deviceTokenSuccess: ['deviceToken'],
  deviceTokenFailure: ['error'],
  accountReset: null,
  phoneRequest: ['number'],
  phoneSuccess: ['number'],
  phoneFailure: ['error'],
  usersBlockRequest: ['users'],
  usersBlockSuccess: ['users'],
  usersBlockFailure: ['error'],
  otpRequest: ['otp'],
  otpSuccess: ['otp'],
  otpFailure: ['error'],
  deleteAccountRequest: [''],
  deleteAccountSuccess: [''],
  deleteAccountFailure: ['error'],
});

export const AccountTypes = Types;
export default Creators;
export type ACCOUNT_TYPE = typeof AccountTypes;
export type ACCOUNT_STATE_TYPE = typeof INITIAL_STATE;

interface AccountState {
  deviceToken: string | null;
  account: any | null;
  phoneNumber: string | null;
  otp: string | null;
  preferences: any | null;
  error: any | null;
  fetching: boolean;
  fetchingPhone: boolean;
  fetchingUsersBlock: boolean;
  updating: boolean;
  otpFetching: boolean;
  errorDelete: any | null;
  deleting: boolean;
  usersBlockRequest: boolean;
  deviceTokenRequest: boolean;
  fetchingPreferences: boolean;
}

/* ------------- Initial State ------------- */

export const INITIAL_STATE: ImmutableObject<AccountState> = Immutable({
  deviceToken: null,
  account: null,
  phoneNumber: null,
  otp: null,
  preferences: null,
  error: null,
  fetching: false,
  fetchingPhone: false,
  fetchingUsersBlock: false,
  updating: false,
  otpFetching: false,
  errorDelete: null,
  deleting: false,
  usersBlockRequest: false,
  deviceTokenRequest: false,
  fetchingPreferences: false,
});

/* ------------- Reducers ------------- */
// ACCOUNT

// we're attempting to account
export const request = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ fetching: true });

// we've successfully logged in
export const success = (state: ACCOUNT_STATE_TYPE, data) => {
  const { account } = data;
  return state.merge({ fetching: false, error: null, account });
};

// we've had a problem getting the account
export const failure = (state: ACCOUNT_STATE_TYPE, { error }) =>
  state.merge({ fetching: false, updating: false, account: null, error });

// PHONE
// we're attempting to account
export const phoneRequest = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ fetchingPhone: true });

// we've successfully logged in
export const phoneSuccess = (state: ACCOUNT_STATE_TYPE, data) => {
  const { phoneNumber } = data;
  return state.merge({ fetchingPhone: false, error: null, phoneNumber });
};

// we've had a problem getting the account
export const phoneFailure = (state: ACCOUNT_STATE_TYPE, { error }) =>
  state.merge({ fetchingPhone: false, phoneNumber: null, error });

// Delete account
export const deleteAccountRequest = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ deleting: true });

// we've successfully deleted the account
export const deleteAccountSuccess = (state: ACCOUNT_STATE_TYPE) => {
  return state.merge({ deleting: false, errorDelete: null });
};

// we've had a problem deleting the account
export const deleteAccountFailure = (
  state: ACCOUNT_STATE_TYPE,
  { errorDelete },
) => state.merge({ deleting: false, errorDelete });

// OTP
// we're attempting to account
export const otpRequest = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ otpFetching: true });

// we've successfully logged in
export const otpSuccess = (state: ACCOUNT_STATE_TYPE, data) => {
  const { otp } = data;
  return state.merge({ otpFetching: false, error: null, otp });
};

// we've had a problem getting the account
export const otpFailure = (state: ACCOUNT_STATE_TYPE, { error }) =>
  state.merge({ otpFetching: false, otp: null, error });

// GET PREFERENCES

// we're attempting to account
export const requestPreferences = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ fetchingPreferences: true });

// we've successfully logged in
export const successPreferences = (state: ACCOUNT_STATE_TYPE, data) => {
  const { preferences } = data;
  return state.merge({ fetchingPreferences: false, error: null, preferences });
};

// we've had a problem getting the account
export const failurePreferences = (state: ACCOUNT_STATE_TYPE, { error }) =>
  state.merge({ fetchingPreferences: false, preferences: null, error });

// SAVE PREFERENCES
// we're attempting to account
export const requestPreferencesUpdate = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ fetchingPreferences: true });

// we've successfully logged in
export const successPreferencesUpdate = (state: ACCOUNT_STATE_TYPE, data) => {
  const { preferences } = data;
  return state.merge({ fetchingPreferences: false, error: null, preferences });
};

// we've had a problem getting the account
export const failurePreferencesUpdate = (
  state: ACCOUNT_STATE_TYPE,
  { error },
) => state.merge({ fetchingPreferences: false, preferences: null, error });

//
// we're attempting to updating account settings
export const updateRequest = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ fetching: true });

// DEVICE TOKEN
// we're attempting to set device token
export const tokenRequest = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ deviceTokenRequest: true });
// we've successfully updated the device token
export const tokenSuccess = (state: ACCOUNT_STATE_TYPE, data) => {
  const { deviceToken } = data;
  return state.merge({ error: null, deviceTokenRequest: false, deviceToken });
};
// we've had a problem updating the device token
export const tokenFailure = (state: ACCOUNT_STATE_TYPE, { error }) =>
  state.merge({ deviceTokenRequest: false, error });

export const usersBlockRequest = (state: ACCOUNT_STATE_TYPE) =>
  state.merge({ usersBlockRequest: true });

export const usersBlockSuccess = (state: ACCOUNT_STATE_TYPE, data) => {
  const { account } = state;
  const { users } = data;
  account.userBlocked = users;
  const nextAccount = {
    ...account,
    userBlocked: users,
  };

  return state.merge({
    usersBlockRequest: false,
    error: null,
    account: nextAccount,
  });
};

export const usersBlockFailure = (state: ACCOUNT_STATE_TYPE, { error }) =>
  state.merge({ usersBlockRequest: false, error });

export const accountReset = (state: ACCOUNT_STATE_TYPE) => INITIAL_STATE;

/* ------------- Hookup Reducers To Types ------------- */

export const reducer = createReducer(INITIAL_STATE, {
  [Types.ACCOUNT_REQUEST]: request,
  [Types.ACCOUNT_SUCCESS]: success,
  [Types.ACCOUNT_FAILURE]: failure,
  [Types.ACCOUNT_UPDATE_REQUEST]: updateRequest,
  [Types.ACCOUNT_PREFERENCES_REQUEST]: requestPreferences,
  [Types.ACCOUNT_PREFERENCES_SUCCESS]: successPreferences,
  [Types.ACCOUNT_PREFERENCES_FAILURE]: failurePreferences,
  [Types.ACCOUNT_PREFERENCES_UPDATE_REQUEST]: requestPreferencesUpdate,
  [Types.ACCOUNT_PREFERENCES_UPDATE_SUCCESS]: successPreferencesUpdate,
  [Types.ACCOUNT_PREFERENCES_UPDATE_FAILURE]: failurePreferencesUpdate,
  [Types.ADD_ACCOUNT_PREFERENCES_REQUEST]: requestPreferencesUpdate,
  [Types.ADD_ACCOUNT_PREFERENCES_SUCCESS]: successPreferencesUpdate,
  [Types.ADD_ACCOUNT_PREFERENCES_FAILURE]: failurePreferencesUpdate,
  [Types.DEVICE_TOKEN_REQUEST]: tokenRequest,
  [Types.DEVICE_TOKEN_SUCCESS]: tokenSuccess,
  [Types.DEVICE_TOKEN_FAILURE]: tokenFailure,
  [Types.ACCOUNT_RESET]: accountReset,
  [Types.PHONE_REQUEST]: phoneRequest,
  [Types.PHONE_SUCCESS]: phoneSuccess,
  [Types.PHONE_FAILURE]: phoneFailure,
  [Types.USERS_BLOCK_REQUEST]: usersBlockRequest,
  [Types.USERS_BLOCK_SUCCESS]: usersBlockSuccess,
  [Types.USERS_BLOCK_FAILURE]: usersBlockFailure,
  [Types.OTP_REQUEST]: otpRequest,
  [Types.OTP_SUCCESS]: otpSuccess,
  [Types.OTP_FAILURE]: otpFailure,
  [Types.DELETE_ACCOUNT_REQUEST]: deleteAccountRequest,
  [Types.DELETE_ACCOUNT_SUCCESS]: deleteAccountSuccess,
  [Types.DELETE_ACCOUNT_FAILURE]: deleteAccountFailure,
});

/* ------------- Selectors ------------- */
// Is the current user logged in?
export const isLoggedIn = (state: ACCOUNT_STATE_TYPE) => state.account !== null;

export const getLogin = (state: ACCOUNT_STATE_TYPE) =>
  state.account?.login ?? 'anonymousUser';

export const getNickname = (state: ACCOUNT_STATE_TYPE) => {
  if (state.account !== null) {
    return state.account.nickname;
  }
  return 'anonymousUser';
};
