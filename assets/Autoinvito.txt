------------------------------------------------------------------------
Event
picture, location, name, description, type of kitchen, max number of places, price per person, generic tags

User
name, avatar, age, sex, description, taste, food intolerances
------------------------------------------------------------------------
Left menu : 
(if user is logged in: 
	User avatar,
	My Events,
	Event Offers,
	Event Requests,
	Message, 
	Feedbackm,
	Search

if user is non-logged in:
	Search,
	Login

1. Events Search
	- top: Search bar
	- filter 
	under search bar
	filter option - (dates, guests, city, type of kitchen, drink)
	- events list (2 columns)
	event card view - image, name, dates, show(button)
	click "show" to Event Detail page.

2. Events Detail page
	- event detail information
	image, title, owner avatar, owner name
	- additional information
	guest count, time, position, max number of places
	explanation
	- show 2 buttons on bottom (logged in user)
	send message to host
	participate to the event (if there are place available)
	open dialog(how many people, then pay)

3. Events Offers
	offers that requested to attend in event (current user is guest.)
   - list of event offers

4. Events Requests
	receive request to attend in event (current user is host.)
	- list of event requests

5. My Events
	- list of events
	(Event Detail/Edit event)
	(Create new event)

6. Message
	- list of contacts
	(chat)

7. Feedback
	- list of feedbacks

8. Login/Register/Forgot password

9. User Profile

------------------------------------------------------------------------


