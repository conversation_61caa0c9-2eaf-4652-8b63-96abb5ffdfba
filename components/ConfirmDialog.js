import React, { Component } from 'react';
import { Dialog, Portal, Paragraph, Button } from 'react-native-paper';
import i18n from '@translations/index';
import { colors } from '@constants';

/**
 * Component used to show ConfirmDialog in the app
 */
class ConfirmDialog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: props.visible || false,
    };
  }
  UNSAFE_componentWillReceiveProps(newProps) {
    if (newProps.visible !== this.state.visible) {
      this.setState({
        visible: !!newProps.visible,
      });
    }
  }

  dismissAlertModal = () => {
    this.setState({ visible: false });
    if (this.props.onClose instanceof Function) {
      this.props.onClose();
    }
  };

  render() {
    const { visible } = this.state;
    const { title, description, onOk } = this.props;
    return (
      <Portal>
        <Dialog visible={visible} onDismiss={this.dismissAlertModal}>
          <Dialog.Title>{i18n.t(title)}</Dialog.Title>
          <Dialog.Content>
            <Paragraph>{i18n.t(description)}</Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button
              style={{ borderRadius: 20 }}
              icon="cancel"
              mode="outlined"
              style={{ margin: 5 }}
              onPress={this.dismissAlertModal.bind(this)}>
              {i18n.t('__cancel')}
            </Button>
            <Button
              icon="check"
              mode="contained"
              color={colors.primary}
              onPress={onOk}>
              {i18n.t('__ok')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    );
  }
}

export default ConfirmDialog;
