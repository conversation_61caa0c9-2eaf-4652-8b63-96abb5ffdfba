import React from 'react';
import { View } from 'react-native';
import { Card, Avatar, Paragraph, Caption, Text } from 'react-native-paper';
import StarRating from 'react-native-star-rating';
import { getAvatar, colors } from '@constants';
import { getDateTime } from '@constants/functions';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import useUserNickname from '@hooks/useUserNickname';

const FeedbackEntry = ({ item, showDetails }) => {
  const userNickname = useUserNickname(item.senderId);
  console.log('FeedbackEntry', item);
  return (
    <View style={{ marginBottom: 10, marginStart: 5, marginEnd: 5 }}>
      {
        <Card onPress={showDetails}>
          <Card.Content
            style={{
              marginBottom: -10,
              height: hp('15%'),
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Avatar.Image
                source={{ uri: getAvatar(item.senderId) }}
                size={40}
              />
              <View style={{ flex: 1, marginLeft: 10 }}>
                <Caption
                  style={{
                    fontFamily: 'medium',
                    color: 'black',
                    lineHeight: 16,
                    fontSize: 14,
                    margin: 0,
                  }}>
                  {userNickname}
                </Caption>
                <View style={{ width: 90, paddingVertical: 5 }}>
                  <StarRating
                    starSize={14}
                    disabled={true}
                    maxStars={5}
                    rating={item.point / 100}
                    fullStarColor={colors.fullStar}
                  />
                </View>
              </View>
            </View>

            <Paragraph>{item.description}</Paragraph>
            <Text style={{ textAlign: 'right', color: 'grey' }}>
              {/* TODO: check this */}
              {getDateTime(item.createdDate)}
            </Text>
          </Card.Content>
        </Card>
      }
    </View>
  );
};

export { FeedbackEntry };
