import React, { Component } from 'react';
import { Dialog, Portal, Paragraph, Button } from 'react-native-paper';
import i18n from '@translations/index';
import { View, Image } from 'react-native';
import * as Icon from '@expo/vector-icons';
import {
  colors,
  _ERROR,
  _WARNING,
  _SUCCESS,
  _CONFIRM,
  parseMessage,
  _CANCELLED,
  _DELETE,
  icon_size_xsmall,
} from '../constants';
import { images } from '../constants/images';

export interface ComposedMessage {
  text: string;
  icon?: any;
}

export interface Alert {
  visible: boolean;
  messageType: string;
  message: string | Array<ComposedMessage>;
}

/**
 * Component used to show Alerts in the app
 */
class AlertDialog extends Component<
  {
    alert: Alert;
    // visible: boolean;
    // messageType: string;
    // message: string | Array<ComposedMessage>;
    onClose: Function;
    toShare?: Function | undefined;
    toCancel?: Function | undefined;
  },
  { visible: boolean }
> {
  constructor(props) {
    super(props);
    this.state = {
      visible: props.visible || false,
    };
  }
  UNSAFE_componentWillReceiveProps(newProps) {
    if (newProps.visible !== this.state.visible) {
      this.setState({
        visible: !!newProps.visible,
      });
    }
  }

  dismissAlertModal = (isDismissed: boolean) => {
    this.setState({ visible: false });
    if (this.props.onClose instanceof Function) {
      this.props.onClose(isDismissed);
    }
  };

  showDialogHeader = () => {
    const {
      alert: { messageType },
    } = this.props;
    const iconSize = 64;
    return (
      <View
        style={{
          flexDirection: 'row',
          padding: 20,
        }}>
        {messageType === _WARNING || messageType === _CANCELLED ? (
          <Icon.Entypo size={iconSize} color={colors.warning} name="warning" />
        ) : messageType === _CONFIRM ? (
          <Icon.Octicons
            size={iconSize}
            color={colors.warning}
            name="megaphone"
          />
        ) : messageType === _DELETE ? (
          <Icon.MaterialCommunityIcons
            size={iconSize}
            color={colors.warning}
            name="delete"
          />
        ) : messageType === _ERROR ? (
          <Icon.MaterialIcons
            size={iconSize}
            color={colors.error}
            name="error"
          />
        ) : messageType === _SUCCESS || messageType === _CONFIRM ? (
          <Icon.MaterialCommunityIcons
            size={iconSize}
            color={colors.success}
            name="font-awesome"
          />
        ) : null}
        <Dialog.Title>{`${i18n.t(messageType)}  `}</Dialog.Title>
      </View>
    );
  };

  getBorderColor() {
    const {
      alert: { messageType },
    } = this.props;
    if (
      messageType === _WARNING ||
      messageType === _CANCELLED ||
      messageType === _DELETE
    ) {
      return colors.warning;
    }
    if (messageType === _CONFIRM) {
      return colors.blue;
    }
    if (messageType === _ERROR) {
      return colors.error;
    }
    if (messageType === _SUCCESS) {
      return colors.success;
    }
  }

  showDialogContent = () => {
    const {
      alert: { message },
    } = this.props;
    if (Array.isArray(message) && message.length > 0) {
      const first = message[0];
      const rest = message.slice(1);
      return (
        <Dialog.Content
          style={{
            margin: 0,
            padding: 0,
            minHeight: 120,
          }}>
          <Paragraph
            style={{
              marginLeft: 5,
              marginBottom: -15,
            }}>
            {parseMessage(first.text)}
          </Paragraph>
          {rest.map((prop, key) => (
            <View
              key={key}
              style={{
                flex: 1,
                minHeight: 30,
                marginTop: 5,
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Image
                source={prop.icon ?? images._warning}
                style={{
                  height: icon_size_xsmall,
                  width: icon_size_xsmall,
                }}
              />
              <Paragraph
                style={{
                  marginLeft: 5,
                  minHeight: 20,
                }}>
                {parseMessage(prop.text)}
              </Paragraph>
            </View>
          ))}
        </Dialog.Content>
      );
    } else {
      return (
        <Dialog.Content>
          <Paragraph>{parseMessage(message)}</Paragraph>
        </Dialog.Content>
      );
    }
  };

  render() {
    const { visible } = this.state;
    const {
      toShare,
      toCancel,
      alert: { visible: visibleProp },
    } = this.props;

    return (
      <Portal>
        <Dialog
          visible={visible || visibleProp}
          onDismiss={() => this.dismissAlertModal(true)}
          style={{
            minHeight: 250,
            borderWidth: 2,
            borderColor: this.getBorderColor(),
            justifyContent: 'space-between',
          }}>
          {this.showDialogHeader()}
          {this.showDialogContent()}
          <Dialog.Actions>
            {toShare !== undefined ? (
              <Button
                mode="outlined"
                icon="share-variant"
                style={{ marginEnd: 5 }}
                onPress={() => toShare()}>
                {i18n.t('__share')}
              </Button>
            ) : null}
            {toCancel !== undefined ? (
              <Button
                mode="outlined"
                icon="cancel"
                style={{ marginEnd: 5 }}
                onPress={() => toCancel()}>
                {i18n.t('__no')}
              </Button>
            ) : null}

            <Button
              icon="check"
              mode="contained"
              onPress={this.dismissAlertModal.bind(this)}>
              {i18n.t('__ok')}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    );
  }
}

export default AlertDialog;
