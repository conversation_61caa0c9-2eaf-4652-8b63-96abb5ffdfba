import React, { useEffect, useState } from 'react';
import { Ava<PERSON>, <PERSON>, Button, Badge, Caption, FAB } from 'react-native-paper';
import { View, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import RNRestart from 'react-native-restart';
import Constants from 'expo-constants';
import { images, colors, getIcon } from '@constants';
import { connect, useDispatch, useSelector } from 'react-redux';
import { appConfig } from '@config/app-config';
import i18n from '@translations/index';
import CommonActions from '@reducers/common.reducer';
import LoginActions from '@reducers/login.reducer';
import {
  CREATE_EVENT_ROUTE,
  DRAWER_MY_EVENTS_ROUTE,
  DRAWER_PROFILE_ROUTE,
  DRAWER_EVENTS_REQUEST_ROUTE,
  DRAWER_SEARCH_ROUTE,
  DRAWER_INCOMING_EVENT_REQUEST_ROUTE,
  DRAWER_LOGIN_ROUTE,
  PAYMENT_ROUTE,
} from '@routes/route_constants';
import { DrawerActions, useNavigation } from '@react-navigation/native';
import { useAuth0 } from 'react-native-auth0';
import { useStripe } from '@stripe/stripe-react-native';

const MainMenu = props => {
  const navigation = useNavigation();
  const { resetPaymentSheetCustomer } = useStripe();

  const dispatch = useDispatch();
  const { authorize, clearSession, user, getCredentials, error, isLoading } =
    useAuth0();
  const [active, setActive] = useState(DRAWER_SEARCH_ROUTE);
  const [imageHash, setImageHash] = useState(Date.now());
  const account = useSelector(state => state.account);
  const nickname = useSelector(state => {
    return state.account.account?.nickname || state.account.account?.login;
  });
  const loggedIn = useSelector(
    state => state.authorization?.authToken !== null ?? false,
  );
  const scopes = 'offline_access openid profile email';

  const links = [
    { key: DRAWER_SEARCH_ROUTE, label: '__home' },
    { key: DRAWER_INCOMING_EVENT_REQUEST_ROUTE, label: '__incoming_requests' },
    { key: DRAWER_EVENTS_REQUEST_ROUTE, label: '__outgoing_requests' },
    { key: DRAWER_MY_EVENTS_ROUTE, label: '__my_events' },
    // { key: PAYMENT_ROUTE, label: '__payment' },
  ];

  const changeHandler = (key: string, screen = null, params = null) => {
    console.log('changeHandler account login', account);
    console.log('changeHandler account key', key, 'screen', screen);

    if (account !== undefined && account !== null) {
      props.setUserInfoRequest(account.login);
    }

    setActive(key);

    navigation.navigate(key, { screen });
  };

  useEffect(() => {
    // check to update image
    if (account.fetching && !account.fetching) {
      if (account.error == null) {
        setImageHash(Date.now());
      }
    }
  }, [account.fetching, account.error]);

  useEffect(() => {
    const retrieveCredentials = async () => {
      const credentials = await getCredentials(scopes);
      console.log('retrieve credentials >>', credentials);
      console.log('retrieve credentials exp at', credentials?.expiresAt);
      if (credentials !== undefined) {
        dispatch(LoginActions.refreshToken(credentials));
        dispatch(LoginActions.loginSocialRequest(credentials));
      } else {
        console.log('safely clean up the content');
        dispatch(LoginActions.logoutRequest());
      }
    };
    retrieveCredentials();
  }, [dispatch, getCredentials]);

  const setPresentationAndRestart = async () => {
    const ret = await props.enablePresentation();
    RNRestart.Restart();
  };

  const onLogout = async () => {
    await clearSession({}, {});
    dispatch(LoginActions.logoutRequest());
    changeHandler(DRAWER_SEARCH_ROUTE);
    resetPaymentSheetCustomer();
    navigation.dispatch(DrawerActions.closeDrawer());
  };

  const onLogin = async () => {
    await authorize(
      {
        scope: scopes,
        audience: 'https://auth.selfinvite.eu/api/v2/',
      },
      {},
    );
    const credentials = await getCredentials(scopes);
    console.log('>> onLogin credentials', credentials);
    if (credentials) {
      // Alert.alert('AccessToken: ' + credentials?.accessToken);
      dispatch(LoginActions.loginSocialRequest(credentials));

      navigation.navigate(DRAWER_SEARCH_ROUTE);
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          padding: 20,
          paddingBottom: 10,
          paddingTop: Constants.statusBarHeight + 20,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.background,
        }}>
        {account.account && account.account.imageUrl ? (
          <Avatar.Image
            // key={account.account.imageUrl}
            source={{
              uri: `${account.account.imageUrl}?${imageHash}`,
            }}
            size={100}
            style={{ backgroundColor: colors.whiteBackground }}
          />
        ) : (
          <Avatar.Image
            style={{ backgroundColor: colors.info }}
            source={images._profile}
            size={80}
          />
        )}
        {loggedIn ? (
          <View>
            <Button
              onPress={() => navigation.navigate(DRAWER_PROFILE_ROUTE)}
              uppercase={false}
              style={{
                textAlign: 'center',
                marginTop: 5,
                elevation: 3,
                backgroundColor: colors.whiteBackground,
                borderWidth: 1,
                borderColor: 'red',
              }}>
              {'@' + nickname}
            </Button>
          </View>
        ) : (
          <View />
        )}
      </View>
      <ScrollView style={{}}>
        <List.Section title={i18n.t('__menu')}>
          {!loggedIn ? (
            <List.Item
              left={props => <List.Icon {...props} icon="home" />}
              style={{
                padding: 0,
                margin: 5,
                marginHorizontal: 10,
                borderRadius: 5,
                overflow: 'hidden',
                backgroundColor:
                  active === DRAWER_SEARCH_ROUTE
                    ? 'rgba(242, 73, 88, 0.1)'
                    : 'transparent',
              }}
              onPress={() => changeHandler(DRAWER_SEARCH_ROUTE)}
              titleStyle={{
                color:
                  active === DRAWER_SEARCH_ROUTE ? colors.primary : colors.text,
              }}
              title={i18n.t('__home')}
              key={DRAWER_SEARCH_ROUTE}
            />
          ) : (
            links.map(({ key, label, badge }) => (
              <List.Item
                left={props => <List.Icon {...props} icon={getIcon(label)} />}
                style={{
                  padding: 0,
                  margin: 5,
                  marginHorizontal: 10,
                  borderRadius: 5,
                  overflow: 'hidden',
                  backgroundColor:
                    active === key ? 'rgba(242, 73, 88, 0.1)' : 'transparent',
                }}
                onPress={() => changeHandler(key)}
                titleStyle={{
                  color: active === key ? colors.primary : colors.text,
                }}
                title={i18n.t(label)}
                key={key}
                right={props =>
                  badge && badge() > 0 ? (
                    <Badge {...props} style={{ margin: 10 }}>
                      {badge()}
                    </Badge>
                  ) : null
                }
              />
            ))
          )}
        </List.Section>

        {!loggedIn ? (
          <List.Section title={i18n.t('__authentication')}>
            <List.Item
              left={props => (
                <List.Icon {...props} icon={getIcon('__authentication')} />
              )}
              title={i18n.t('__login')}
              style={{
                padding: 0,
                margin: 5,
                marginHorizontal: 10,
                borderRadius: 5,
                overflow: 'hidden',
                backgroundColor:
                  active === DRAWER_LOGIN_ROUTE
                    ? 'rgba(242, 73, 88, 0.1)'
                    : 'transparent',
              }}
              onPress={onLogin}
            />
          </List.Section>
        ) : (
          <List.Section title={i18n.t('__authentication')}>
            <List.Item
              left={props => (
                <List.Icon {...props} icon={getIcon('__logout')} />
              )}
              title={i18n.t('__logout')}
              style={{
                padding: 0,
                margin: 5,
                marginHorizontal: 10,
                borderRadius: 5,
                overflow: 'hidden',
                backgroundColor:
                  active === 'RegisterStack'
                    ? 'rgba(242, 73, 88, 0.1)'
                    : 'transparent',
              }}
              onPress={onLogout}
            />
          </List.Section>
        )}
        <TouchableOpacity
          style={{ marginLeft: 20, marginBottom: 20 }}
          onPress={setPresentationAndRestart}>
          <Caption style={styles.caption}>
            {i18n.t('__show_presentation')}
          </Caption>
        </TouchableOpacity>
        <View style={{ marginLeft: 20 }}>
          <Caption style={styles.caption}>
            {i18n.t('__version') + ': ' + appConfig.APP_VERSION}
          </Caption>
        </View>
        {loggedIn ? (
          <View style={{ marginBottom: 20 }}>
            <FAB
              style={styles.fab}
              icon={getIcon('add')}
              onPress={() => changeHandler(CREATE_EVENT_ROUTE, {})}
            />
          </View>
        ) : (
          <View />
        )}
      </ScrollView>
    </View>
  );
};

const mapStateToProps = appState => {
  return {
    authorization: appState.authorization,
    // loggedIn: isLoggedIn(appState.account),
    // nickname: getNickname(appState.account),
    eventKv: appState.eventkv,
    account: appState.account,
    events: appState.events,
    messages: appState.messages,
    common: appState.common,
  };
};

const mapDispatchToProps = dispatch => {
  return {
    // logout: () => dispatch(LoginActions.logoutRequest()),
    enablePresentation: () =>
      dispatch(CommonActions.setPresentationRequest(true)),
    setUserInfoRequest: userInfoId =>
      dispatch(CommonActions.setUserInfoRequest(userInfoId)),
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(MainMenu);

const styles = StyleSheet.create({
  drawerContent: {
    flex: 1,
  },
  userInfoSection: {
    paddingLeft: 20,
  },
  title: {
    marginTop: 20,
    fontWeight: 'bold',
  },
  caption: {
    fontSize: 14,
    lineHeight: 14,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 20,
    marginRight: 20,
  },
  section: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 15,
  },
  paragraph: {
    fontWeight: 'bold',
    marginRight: 3,
  },
  drawerSection: {
    marginTop: 15,
  },
  preference: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  fab: {
    position: 'absolute',
    backgroundColor: colors.primary,
    color: colors.white,
    zIndex: 100,
    elevation: 50,
    margin: 20,
    right: 0,
    bottom: 0,
  },
});
