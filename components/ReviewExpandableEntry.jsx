import React, { useState } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { Card, Avatar, Paragraph, Caption, Text } from 'react-native-paper';
import StarRating from 'react-native-star-rating';
import { getAvatar, colors } from '@constants';
import { getDateTime } from '@constants/functions';
import * as Icon from '@expo/vector-icons';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import ReviewRatings from '@screens/ReviewRatings';
import useUserNickname from '@hooks/useUserNickname';

const ReviewExpandableEntry = ({ item, showDetails }) => {
  const [collapsed, setCollapsed] = useState(true);
  const userNickname = useUserNickname(item.senderId);

  const defaultHeight = 15 + item.description.length * 0.06;
  const plusHeight = defaultHeight + 15;

  return (
    <View style={{ marginBottom: 10, marginStart: 5, marginEnd: 5 }}>
      {
        <Card onPress={showDetails}>
          <Card.Content
            style={{
              marginBottom: -10,
              height: collapsed
                ? hp(defaultHeight + '%')
                : hp(plusHeight + '%'),
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Avatar.Image
                source={{ uri: getAvatar(item.senderId) }}
                size={40}
              />
              <View style={{ flex: 1, marginLeft: 10 }}>
                <Caption
                  style={{
                    fontFamily: 'medium',
                    color: 'black',
                    lineHeight: 16,
                    fontSize: 14,
                    margin: 0,
                  }}>
                  {userNickname}
                </Caption>
                <View style={{ width: 90, paddingVertical: 5 }}>
                  <StarRating
                    starSize={14}
                    disabled={true}
                    maxStars={5}
                    rating={item.point / 100}
                    fullStarColor={colors.fullStar}
                  />
                </View>
              </View>
              <TouchableOpacity onPress={() => setCollapsed(!collapsed)}>
                <Icon.Entypo
                  name={collapsed ? 'triangle-down' : 'triangle-up'}
                  color={colors.gray}
                  size={30}
                />
              </TouchableOpacity>
            </View>

            <Paragraph>{item.description}</Paragraph>
            <Text style={{ textAlign: 'right' }}>
              {getDateTime(item.createdDate)}
            </Text>
            {!collapsed ? <ReviewRatings ratings={item} /> : <></>}
          </Card.Content>
        </Card>
      }
    </View>
  );
};

export { ReviewExpandableEntry };
