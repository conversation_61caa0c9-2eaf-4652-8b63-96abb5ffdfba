import React, { Component } from 'react';
import { Portal } from 'react-native-paper';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment from 'moment';
import { colors } from '@constants/colors';

/**
 * Component used to show DateDialog in the app
 */
class DateDialog extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: props.visible || false,
    };
  }

  render() {
    const { showDate, onCancel, onConfirm } = this.props;
    return (
      <Portal>
        <DateTimePickerModal
          mode="date"
          style={{ tintColor: colors.primary }}
          isVisible={showDate}
          maximumDate={moment(new Date()).utc().subtract(18, 'years').toDate()}
          onConfirm={onConfirm}
          onCancel={onCancel}
        />
      </Portal>
    );
  }
}

export default DateDialog;
