import React, { FC } from 'react';
import { Image, ImageSourcePropType } from 'react-native';
import { getIconFromArray, icon_size_small } from '@constants';
import { typeEvents } from '@constants/typeE';

interface EventIconProps {
  size?: number;
  type: string; // Adjust the type according to your actual data type
}

const EventIcon: FC<EventIconProps> = props => {
  const size = props.size ? props.size : icon_size_small;
  const iconSource = getIconFromArray(
    typeEvents,
    props.type,
  ) as ImageSourcePropType;

  return (
    <Image
      style={{
        height: size,
        width: size,
      }}
      source={iconSource}
    />
  );
};
export default EventIcon;
