import React, { FC } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Ionicons from 'react-native-vector-icons/Ionicons';

interface IconWithBadgeProps {
  name: string;
  badgeCount: number;
  color: string;
  size: number;
}

const IconWithBadge: FC<IconWithBadgeProps> = ({
  name,
  badgeCount,
  color,
  size,
}) => {
  return (
    <View style={styles.container}>
      <Ionicons name={name} size={size} color={color} />
      {badgeCount > 0 && (
        <View style={styles.badge}>
          <Text style={styles.text}>{badgeCount}</Text>
        </View>
      )}
    </View>
  );
};
export default IconWithBadge;

const styles = StyleSheet.create({
  container: {
    width: 24,
    height: 24,
    margin: 5,
  },
  badge: {
    position: 'absolute',
    right: -6,
    top: -3,
    backgroundColor: 'red',
    borderRadius: 6,
    width: 12,
    height: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});
