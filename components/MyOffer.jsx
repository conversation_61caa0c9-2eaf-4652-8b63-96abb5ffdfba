import React, { useEffect, useState } from 'react';
import { View, Linking, Platform } from 'react-native';
import { List, IconButton, Avatar, Chip, Paragraph } from 'react-native-paper';
import * as Icon from '@expo/vector-icons';
import {
  colors,
  getAvatarOrDummy,
  getCached<PERSON>ogin,
  EVENT_OFFER_KV_STATE_GUEST_PAID,
  EVENT_OFFER_KV_STATE_GUEST_REQUEST,
  EVENT_OFFER_KV_STATE_HOST_CONFIRM,
  EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL,
  EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL,
  EVENT_OFFER_KV_STATE_HOST_CANCEL,
  EVENT_OFFER_KV_STATE_GUEST_CANCEL,
  getDateTime,
  EVENT_STATE_CLOSE,
  getIcon,
  EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT,
  EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT,
  EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN,
  EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE,
  EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND,
  EVENT_OFFER_KV_STATE_NO_REFUND,
  EVENT_STATE_FEEDBACK_CLOSE,
  EVENT_STATE_CLOSING,
  EVENT_STATE_OPEN,
  EVENT_OFFER_KV_HOST_CONFIRMS_REFUND,
  EVENT_OFFER_KV_HOST_DENIES_REFUND,
  EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT,
  EVENT_OFFER_KV_STATE_DELETED,
  _DELETE,
  EVENT_STATE_DELETED,
  EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST,
  EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST_REFUNDED_DONE,
} from '@constants';
import i18n from '@translations/index';
import StarRating from 'react-native-star-rating';
import { EVENT_DETAILS_ROUTE, PAYMENT_ROUTE } from '@routes/route_constants';
import {
  EVENT_OFFER_KV_GUEST_CANCELLED_PAID_REQUEST_BEFORE_EVENT_CLOSE,
  EVENT_STATE_FEEDBACK_OPEN_AND_MANAGE_DISPUTE,
} from '@constants/constants';
import { getEventStatus } from '@constants/functions';
import useUserNickname from '@hooks/useUserNickname';

const LeftComponent = ({ loggedIn, user_id }) => (
  <View>
    <Avatar.Image
      source={getAvatarOrDummy(loggedIn, user_id)}
      size={50}
      style={{
        marginTop: 15,
        backgroundColor: colors.whiteBackground,
      }}
    />
  </View>
);
const DescriptionComponent = ({ item, userNickname }) => (
  <View>
    <Paragraph
      style={{
        marginLeft: 0,
        color: userNickname ? colors.black : '#FF0000',
        fontFamily: 'medium',
      }}>
      {userNickname ? `@${userNickname}` : `${i18n.t('__user_deleted')}`}
    </Paragraph>
    <Paragraph>
      <Icon.Feather name="calendar" />
      {getDateTime(item.last_modified_date)}
    </Paragraph>
    <Paragraph>
      <StarRating
        starSize={15}
        disabled={true}
        maxStars={5}
        rating={item.avg_review_rx}
        fullStarColor={colors.fullStar}
      />
    </Paragraph>
    {getEventStatus(item.eventofferstate)}
  </View>
);

const MyOffer = ({
  name,
  user_id,
  item,
  selected,
  loggedIn,
  cancelHandler,
  feedback,
  deleteDialog,
  refund,
  id,
  navigation,
  disablePayButton,
}) => {
  const userNickname = useUserNickname(item.user_id);
  const element = {
    flexDirection: 'row',
    justifyContent: 'center',
  };
  const flatlistEntry = {
    padding: 5,
    borderRadius: 15,
    marginBottom: 5,
    paddingVertical: 10,
    borderColor: colors.gray,
    borderWidth: 1,
    backgroundColor: colors.whiteBackground,
  };
  const flatlistElement = {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  };
  const flatlistColumnElement = {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-around',
  };

  const navigateToEventDetails = () => {
    // const { id, item } = this.props;
    navigation.push(EVENT_DETAILS_ROUTE, {
      id,
      item,
      showFab: false,
    });
  };

  const rightComponent = () =>
    // {
    //   // cancelHandler,
    //   // feedback,
    //   // deleteDialog,
    //   // refund,
    //   // item,
    //   // // id,
    //   // onPaymentStatus,
    //   // state,
    //   // eventofferstate,
    //   // disablePayButton,
    // },
    {
      const refundBtn = (
        <IconButton
          onPress={() => refund(id)}
          size={24}
          color={colors.declined}
          icon={({ size, color }) => (
            <Icon.MaterialCommunityIcons
              name={getIcon('__refund')}
              size={size}
              color={color}
            />
          )}
        />
      );
      const feedbackBtn = (
        <IconButton
          onPress={() => feedback(id)}
          size={24}
          color={colors.declined}
          icon={({ size, color }) => (
            <Icon.MaterialIcons name="rate-review" size={size} color={color} />
          )}
        />
      );
      const cancelBtn = (
        <IconButton
          onPress={() => cancelHandler(id, EVENT_OFFER_KV_STATE_GUEST_CANCEL)}
          size={24}
          color={colors.declined}
          icon={({ size, color }) => (
            <Icon.MaterialCommunityIcons
              name="cancel"
              size={size}
              color={color}
            />
          )}
        />
      );

      const showLocationOnMap = (
        <IconButton
          onPress={() => {
            const scheme = Platform.select({
              ios: 'maps:0,0?q=',
              android: 'geo:0,0?q=',
            });
            const latLng = `${item.lat},${item.lon}`;
            const label = item.name;
            const url = Platform.select({
              ios: `${scheme}${label}@${latLng}`,
              android: `${scheme}${latLng}(${label})`,
            });
            Linking.openURL(url);
          }}
          size={24}
          color={colors.declined}
          icon={({ size, color }) => (
            <Icon.MaterialCommunityIcons
              name="map-check"
              size={size}
              color={color}
            />
          )}
        />
      );

      const cancelAfterPayBtn = (
        <IconButton
          onPress={() =>
            cancelHandler(id, EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL)
          }
          size={24}
          color={colors.declined}
          icon={({ size, color }) => (
            <Icon.MaterialCommunityIcons
              name="cancel"
              size={size}
              color={color}
            />
          )}
        />
      );

      const payBtn = (
        <IconButton
          // TODO: enable this
          disabled={disablePayButton}
          size={24}
          color={colors.declined}
          icon={({ size, color }) => (
            <Icon.Entypo name="credit-card" size={size} color={color} />
          )}
          onPress={() =>
            navigation.push(PAYMENT_ROUTE, {
              item,
              // onPaymentStatus,
            })
          }
        />
      );

      const deleteBtn = (
        <IconButton
          icon={({ size, color }) => (
            <Icon.MaterialCommunityIcons
              name="delete"
              size={size}
              color={color}
            />
          )}
          size={20}
          color={colors.declined}
          style={{ margin: 0, marginLeft: 10 }}
          onPress={() =>
            deleteDialog(_DELETE, item, EVENT_OFFER_KV_STATE_DELETED)
          }
        />
      );

      const getRefundStateForGuest = currentItem => {
        switch (currentItem.refund_state) {
          case EVENT_OFFER_KV_STATE_NO_REFUND:
            if (
              currentItem.eventofferstate ===
              EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN
            ) {
              return refundBtn;
            }
            break;

          case EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND:
            return refundChip;

          case EVENT_OFFER_KV_GUEST_CANCELLED_PAID_REQUEST_BEFORE_EVENT_CLOSE:
          case EVENT_OFFER_KV_HOST_CONFIRMS_REFUND:
          case EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST:
            return refundAcceptedChip;

          case EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST_REFUNDED_DONE:
            return refundDoneChip;

          case EVENT_OFFER_KV_HOST_DENIES_REFUND:
            return refundDeniedChip;

          default:
            return null;
        }
      };

      const finishedChip = (
        <Chip
          style={{ backgroundColor: colors.success }}
          icon={getIcon('__finished')}>
          {i18n.t('__finished')}
        </Chip>
      );
      const acceptedChip = (
        <Chip icon={getIcon('__accepted')}>{i18n.t('__accepted')}</Chip>
      );
      const expiredChip = (
        <Chip icon={getIcon('__expired')}>{i18n.t('__expired')}</Chip>
      );
      const paidChip = (
        <Chip
          style={{ backgroundColor: colors.success }}
          icon={getIcon('__paid')}>
          {i18n.t('__paid')}
        </Chip>
      );
      const waitingConfirmation = (
        <Chip icon={getIcon('__pending')}>{i18n.t('__pending')}</Chip>
      );
      const refundChip = (
        <Chip icon={getIcon('__refund')}>{i18n.t('__refund_requested')}</Chip>
      );
      const guestCancelledChip = (
        <Chip icon={getIcon('__cancelled')}>{i18n.t('__cancelled')}</Chip>
      );
      const hostCancelledChip = (
        <Chip icon={getIcon('__cancelled')}>{i18n.t('__host_cancelled')}</Chip>
      );
      const refundAcceptedChip = (
        <Chip style={{ backgroundColor: colors.success }} icon="cash">
          {i18n.t('__refund_accepted')}
        </Chip>
      );
      const refundDoneChip = (
        <Chip style={{ backgroundColor: colors.success }} icon="cash-check">
          {i18n.t('__refund_done')}
        </Chip>
      );
      const refundDeniedChip = (
        <Chip style={{ backgroundColor: colors.warning }} icon="cash-remove">
          {i18n.t('__refund_denied')}
        </Chip>
      );
      const unknownChip = (
        <Chip icon={getIcon('__unknown')}>{i18n.t('__unknown')}</Chip>
      );

      // console.log(
      //   'state',
      //   state,
      //   'eventofferstate',
      //   eventofferstate,
      //   'id',
      //   id,
      //   'item name',
      //   item.name,
      //   'item id',
      //   item.id,
      //   'refund_state ',
      //   item.refund_state,
      // );
      // console.log('item is', item);

      /**
       * new state machine
       */

      let refundElement = null;
      let eokvElement = unknownChip;
      switch (item.eventofferstate) {
        case EVENT_STATE_CLOSING: //2
        case EVENT_STATE_OPEN: //3
          refundElement = getRefundStateForGuest(item);
          // console.log(
          //   'eventofferstate EVENT_STATE_CLOSING EVENT_STATE_OPEN',
          //   item.eventofferstate,
          // );
          switch (item.state) {
            case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
              eokvElement = (
                <View>
                  {acceptedChip}
                  <View style={element}>
                    {cancelBtn}
                    {payBtn}
                  </View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
              eokvElement = (
                <View>
                  {waitingConfirmation}
                  <View style={element}>{cancelBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_GUEST_PAID:
              eokvElement = (
                <View>
                  {paidChip}
                  <View style={element}>{showLocationOnMap}</View>
                  <View style={element}>{cancelAfterPayBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
            case EVENT_OFFER_KV_STATE_HOST_CANCEL:
              eokvElement = (
                <View>
                  {hostCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_GUEST_CANCEL:
            case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
              eokvElement = (
                <View>
                  {guestCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;
          }
          break;

        case EVENT_STATE_CLOSE: //1
          refundElement = getRefundStateForGuest(item);
          // console.log(
          //   'eventofferstate EVENT_STATE_CLOSE',
          //   item.eventofferstate,
          // );
          switch (item.state) {
            case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
            case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
              eokvElement = (
                <View>
                  {expiredChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
            case EVENT_OFFER_KV_STATE_HOST_CANCEL:
              eokvElement = (
                <View>
                  {hostCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_GUEST_CANCEL:
            case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
              eokvElement = (
                <View>
                  {guestCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;
            case EVENT_OFFER_KV_STATE_GUEST_PAID:
              eokvElement = (
                <View>
                  {paidChip}
                  <View style={element}>{showLocationOnMap}</View>
                </View>
              );
              break;
          }
          break;

        case EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN: //4
          refundElement = getRefundStateForGuest(item);
          console.log(
            'eventofferstate EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN',
            item.eventofferstate,
          );
          switch (item.state) {
            case EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT:
            case EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT:
              eokvElement = <View style={element}>{finishedChip}</View>;
              break;

            case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
              eokvElement = <View style={element}>{hostCancelledChip}</View>;
              break;

            case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
              eokvElement = (
                <View>
                  {guestCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
            case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
              eokvElement = (
                <View>
                  {expiredChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              refundElement = null;
              break;

            case EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT:
            case EVENT_OFFER_KV_STATE_GUEST_PAID:
              eokvElement = <View style={element}>{feedbackBtn}</View>;
              break;
          }
          break;

        case EVENT_STATE_FEEDBACK_OPEN_AND_MANAGE_DISPUTE: //5
        case EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE: //6
          refundElement = getRefundStateForGuest(item);
          console.log(
            'eventofferstate EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE',
            item.eventofferstate,
          );
          switch (item.state) {
            case EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT:
            case EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT:
              eokvElement = (
                <View>
                  {finishedChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
              eokvElement = (
                <View>
                  {hostCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
              eokvElement = (
                <View>
                  {guestCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT:
            case EVENT_OFFER_KV_STATE_GUEST_PAID:
              eokvElement = <View style={element}>{feedbackBtn}</View>;
              break;

            case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
            case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
              eokvElement = (
                <View>
                  {expiredChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              refundElement = null;
          }
          break;

        case EVENT_STATE_DELETED: //-1
        case EVENT_STATE_FEEDBACK_CLOSE: //7
          refundElement = getRefundStateForGuest(item);
          console.log(
            'eventofferstate EVENT_STATE_FEEDBACK_CLOSE',
            item.eventofferstate,
          );
          switch (item.state) {
            case EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT:
            case EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT:
            case EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT:
              eokvElement = (
                <View>
                  {finishedChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_HOST_CANCEL:
              eokvElement = (
                <View>
                  {hostCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
              eokvElement = (
                <View>
                  {hostCancelledChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
              break;

            case EVENT_OFFER_KV_STATE_GUEST_PAID:
            case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
            case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
              eokvElement = (
                <View>
                  {expiredChip}
                  <View style={element}>{deleteBtn}</View>
                </View>
              );
          }
          break;

        default:
          console.log('eventofferstate default', item.eventofferstate);
          eokvElement = unknownChip;
      }

      return refundElement ? (
        <View style={flatlistColumnElement}>
          <View style={flatlistElement}>{eokvElement}</View>
          <View style={flatlistElement}>{refundElement}</View>
        </View>
      ) : (
        <View style={flatlistColumnElement}>
          <View style={flatlistElement}>{eokvElement}</View>
        </View>
      );
    };

  const leftComponent = () => (
    <LeftComponent loggedIn={loggedIn} user_id={item.user_id} />
  );
  const descriptionComponent = () => (
    <DescriptionComponent item={item} userNickname={userNickname} />
  );

  return (
    <List.Item
      onPress={navigateToEventDetails}
      style={[
        flatlistEntry,
        selected === item.id
          ? { backgroundColor: colors.gray }
          : { backgroundColor: colors.whiteBackground },
      ]}
      left={leftComponent}
      title={name}
      titleStyle={{ fontWeight: 'bold' }}
      description={descriptionComponent}
      right={rightComponent}
    />
  );
};
// );

export { MyOffer };
