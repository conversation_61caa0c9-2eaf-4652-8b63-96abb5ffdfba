import React from 'react';
import { Chip } from 'react-native-paper';
import i18n from '@translations/index';
import { getIcon } from '@constants/functions';
import { colors } from '@constants/colors';

export const getRefundChip = () => (
  <Chip icon={getIcon('__refund')}>{i18n.t('__refund_requested')}</Chip>
);

export const getRefundAcceptedChip = () => (
  <Chip style={{ backgroundColor: colors.success }} icon="cash">
    {i18n.t('__refund_accepted')}
  </Chip>
);

export const getRefundDoneChip = () => (
  <Chip style={{ backgroundColor: colors.success }} icon="cash-check">
    {i18n.t('__refund_done')}
  </Chip>
);
export const getRefundDeniedChip = () => (
  <Chip style={{ backgroundColor: colors.warning }} icon="cash-remove">
    {i18n.t('__refund_denied')}
  </Chip>
);

export const getFinishedChip = () => (
  <Chip
    style={{ backgroundColor: colors.success }}
    icon={getIcon('__finished')}>
    {i18n.t('__finished')}
  </Chip>
);

export const getAcceptedChip = () => (
  <Chip icon={getIcon('__accepted')}>{i18n.t('__accepted')}</Chip>
);

export const getExpiredChip = () => (
  <Chip icon={getIcon('__expired')}>{i18n.t('__expired')}</Chip>
);

export const getPaidChip = () => (
  <Chip style={{ backgroundColor: colors.success }} icon={getIcon('__paid')}>
    {i18n.t('__paid')}
  </Chip>
);

export const getWaitingConfirmationChip = () => (
  <Chip icon={getIcon('__pending')}>{i18n.t('__pending')}</Chip>
);

export const getGuestCancelledChip = () => (
  <Chip icon={getIcon('__cancelled')}>{i18n.t('__cancelled')}</Chip>
);

export const getHostCancelledChip = () => (
  <Chip icon={getIcon('__cancelled')}>{i18n.t('__host_cancelled')}</Chip>
);

export const getUnknownChip = () => (
  <Chip icon={getIcon('__unknown')}>{i18n.t('__unknown')}</Chip>
);

export const getWaitingPaymentChip = () => (
  <Chip
    style={{ backgroundColor: colors.warning }}
    icon={getIcon('__waitingForPayment')}>
    {i18n.t('__waitingForPayment')}
  </Chip>
);

export const getCancelledChip = () => (
  <Chip icon={getIcon('__cancelled')}>{i18n.t('__cancelled')}</Chip>
);
