import React, { FC } from 'react';
import { Image, ImageSourcePropType } from 'react-native';
import {
  getIconFromArray,
  icon_size_small,
  images,
  typeBeverage,
} from '@constants';

interface BeverageIconProps {
  size?: number;
  type: string; // Adjust the type according to your actual data type
}

const BeverageIcon: FC<BeverageIconProps> = props => {
  const size = props.size ? props.size : icon_size_small;

  return (
    <Image
      style={{
        height: size,
        width: size,
      }}
      source={
        getIconFromArray(
          typeBeverage,
          props.type,
          images._drink_default,
        ) as ImageSourcePropType
      }
    />
  );
};

export default BeverageIcon;
