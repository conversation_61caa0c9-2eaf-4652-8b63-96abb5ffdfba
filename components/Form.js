import React, { Component } from 'react';
import { Input } from './Input';

class Form extends Component {
  generateForm = (form, change, state) => {
    const fields = Object.keys(form).map(key => ({
      ...form[key],
      key,
    }));

    return fields.map(field => (
      <Input {...field} _key={field.key} change={change} />
    ));
  };

  render() {
    const { form, change } = this.props;

    return this.generateForm(form, change);
  }
}

export { Form };
