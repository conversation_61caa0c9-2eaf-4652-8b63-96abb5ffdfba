import * as React from 'react';
import {
  ProgressBar,
  Colors,
  Portal,
  Dialog,
  Paragraph,
} from 'react-native-paper';
import { colors } from '@constants';

const ProgressDialog = props => (
  <Portal>
    <Dialog
      visible={props.visible}
      onDismiss={() => (props.onDismiss ? props.onDismiss() : null)}
      style={{ borderWidth: 2, borderColor: colors.warning }}>
      <Dialog.Content>
        <Paragraph>{`uploading... ${props.progress} %`}</Paragraph>
        <ProgressBar progress={props.progress} color={Colors.red800} />
      </Dialog.Content>
    </Dialog>
  </Portal>
);

export default ProgressDialog;
