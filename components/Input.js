import React, { Component, Fragment } from 'react';
import {
  Text,
  TextInput,
  StyleSheet,
  Image,
  View,
  TouchableOpacity,
  Alert,
  ImageBackground,
  PermissionsAndroid,
  Platform,
} from 'react-native';
import { Surface, Button, Switch } from 'react-native-paper';
import * as Icon from '@expo/vector-icons';
import RNPickerSelect from 'react-native-picker-select';
import SectionedMultiSelect from 'react-native-sectioned-multi-select';
import moment from 'moment';
import UIStepper from 'react-native-ui-stepper';
import DummyEvent from '@assets/images/dummy-event.png';
import { Camera } from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';

import {
  colors,
  defaultDateFormat,
  icon_size,
  icon_size_small,
  INPUT_TYPE_DATE,
  INPUT_TYPE_IMAGE,
  INPUT_TYPE_MULTIIMAGE,
  INPUT_TYPE_MULTIPLE,
  INPUT_TYPE_PICKER,
  INPUT_TYPE_PICKER_COUNTRY,
  INPUT_TYPE_PICKER_COUNTRY_PHONE,
  INPUT_TYPE_SWITCHBISTATE,
  INPUT_TYPE_TAGTEXT,
  INPUT_TYPE_TAGTEXTMULTILINE,
  INPUT_TYPE_TEXTAREA,
  INPUT_TYPE_UISTEPPER,
} from '@constants';
import CountryPicker from 'react-native-country-picker-modal';
import i18n from '@translations/index';
import TwitterTextView from 'react-native-twitter-textview';
import { BottomDialog } from '@screens/BottomDialog';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { INPUT_TYPE_MULTIIMAGE_PICKER } from '@constants/constants';

class Input extends Component {
  state = {
    imagePickerResult: null,
    showInfo: false,
  };

  /**
   * Check Camera Roll permissions on ios
   * if its a image picker
   */
  async componentDidMount() {
    if (this.props.image && Platform.OS === 'íos') {
      const { status } = await Camera.requestCameraPermissionsAsync();
      //  Permissions.askAsync(
      //   Permissions.CAMERA,
      //   Permissions.MEDIA_LIBRARY,
      // );
      if (status !== 'granted') {
        Alert.alert(i18n.t('__camera_roll_permissions'));
      }
      const { status2 } = await MediaLibrary.requestPermissionsAsync();
      if (status2 !== 'granted') {
        Alert.alert(i18n.t('__camera_roll_permissions'));
      }
    }
  }

  async requestCameraPermission() {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: i18n.t('__camera_permission'),
            message: i18n.t('__camera_roll_permissions'),
            buttonNegative: i18n.t('__cancel'),
            buttonPositive: i18n.t('__yes'),
          },
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Camera permission given');
        } else {
          console.log('Camera permission denied');
        }
      } catch (err) {
        console.warn(err);
      }
    }
  }

  /**
   * On Camera pick photo
   */
  async onCameraPickerChange() {
    try {
      if (this.props.pickImageHandler instanceof Function) {
        this.props.pickImageHandler();
      } else {
        const { imageNumber = 1, replace = false } = this.props;
        const value = this.props.value === null ? [] : this.props.value || [];
        await this.requestCameraPermission();
        const pickerResponse = await launchCamera({
          mediaType: 'photo',
          maxWidth: this.props.maxWidth,
          maxHeight: this.props.maxHeight,
          quality: 0.7,
          selectionLimit:
            imageNumber - value.length > 0 ? imageNumber - value.length : 1,
        });

        if (pickerResponse.didCancel) {
          return;
        }
        if (pickerResponse.errorCode) {
          throw pickerResponse.errorCode;
        }

        const finalImageResult = [];
        if (!replace && value) {
          if (Array.isArray(value)) {
            finalImageResult.push(...value);
          } else {
            finalImageResult.push(value);
          }
        }

        pickerResponse.assets.forEach(obj => {
          // console.log('obj>>>', obj);
          finalImageResult.push(obj.uri);
        });
        // finalImageResult.push(croppedImage.path);
        if (this.props.change instanceof Function) {
          // console.log('onCameraPickerChange >> ', finalImageResult);
          this.props.change(finalImageResult, this.props._key);
        }
      }
    } catch (e) {
      Alert.alert('Error ' + e);
    }
  }

  /**
   * On Image Change
   */
  async onImagePickerChange() {
    if (this.props.pickImageHandler instanceof Function) {
      this.props.pickImageHandler();
    } else {
      try {
        const { value = [], imageNumber = 1, replace = false } = this.props;
        const pickerResponse = await launchImageLibrary({
          mediaType: 'photo',
          maxWidth: this.props.maxWidth,
          maxHeight: this.props.maxHeight,
          quality: 0.7,

          selectionLimit:
            imageNumber - value.length > 0 ? imageNumber - value.length : 1,
        });

        const finalImageResult = [];
        if (!replace && value) {
          if (Array.isArray(value)) {
            finalImageResult.push(...value);
          } else {
            finalImageResult.push(value);
          }
        }

        pickerResponse.assets.forEach(obj => {
          finalImageResult.push(obj.uri);
        });

        if (this.props.change instanceof Function) {
          this.props.change(finalImageResult, this.props._key);
        }
      } catch (e) {
        // Alert.alert(i18n.t('__error_pic_path'));
        console.log('pickerResponse error', e);
      }
    }
  }

  onSelectedItemsChange = selectedItems => {
    this.setState({ selectedItems });
  };

  checkKeyboard = (number, phone) => {
    if (number === true || phone === true) {
      return 'numeric';
    } else {
      return 'default';
    }
  };

  translateArray = items => {
    const copy = JSON.parse(JSON.stringify(items));
    copy.forEach((e, i, a) => {
      let element = {};
      if (e?.children !== undefined) {
        element.children = this.translateArray(e.children);
      }
      element.id = e.id;
      element.name =
        e.label.substring(0, 2) === '__' ? i18n.t(e.label) : e.label;
      element.value = e.value;
      element.icon = e.icon;
      a[i] = element;
    });
    return copy;
  };

  // custom icon renderer passed to iconRenderer prop
  // see the switch for possible icon name
  // values
  icon = ({ name, size = icon_size, style }) => {
    // flatten the styles
    const flat = StyleSheet.flatten(style);
    // remove out the keys that aren't accepted on View
    const { color, fontSize, ...styles } = flat;

    let iconComponent;
    // the colour in the url on this site has to be a hex w/o hash
    const iconColor =
      color && color.substr(0, 1) === '#' ? `${color.substr(1)}/` : '';

    const Search = (
      <Image
        source={{ uri: `https://png.icons8.com/search/${iconColor}ios/` }}
        style={{ width: size, height: size }}
      />
    );
    const Down = (
      <Image
        source={{ uri: `https://png.icons8.com/arrow-down/${iconColor}ios/` }}
        style={{ width: size, height: size }}
      />
    );
    const Up = (
      <Image
        source={{ uri: `https://png.icons8.com/arrow-up/${iconColor}ios/` }}
        style={{ width: size, height: size }}
      />
    );
    const Close = (
      <Image
        source={{ uri: `https://png.icons8.com/close-button/${iconColor}ios/` }}
        style={{ width: size, height: size }}
      />
    );

    const Check = (
      <Image
        source={{ uri: `https://png.icons8.com/check-mark/${iconColor}ios/` }}
        style={{ width: size, height: size }}
      />
    );

    const Cancel = (
      <Image
        source={{ uri: `https://png.icons8.com/cancel/${iconColor}ios/` }}
        style={{ width: size, height: size }}
      />
    );

    switch (name) {
      case 'search':
        iconComponent = Search;
        break;
      case 'keyboard-arrow-up':
        iconComponent = Up;
        break;
      case 'keyboard-arrow-down':
        iconComponent = Down;
        break;
      case 'close':
        iconComponent = Close;
        break;
      case 'check':
        iconComponent = Check;
        break;
      case 'cancel':
        iconComponent = Cancel;
        break;
      default:
        iconComponent = null;
        break;
    }
    console.log('icon name', name);
    return <View style={styles}>{iconComponent}</View>;
  };

  removeItem = (element, array) => {
    const cleanedPickerResult = array?.filter(e => e !== element);
    this.props.change(cleanedPickerResult, this.props._key);
  };

  render() {
    const {
      type,
      picker,
      format,
      disabled,
      multiSelect,
      label,
      change,
      optional,
      image,
      date,
      showDateHandler,
      pickImageHandler,
      pickImageHandlerSuccess,
      pickImageHandlerError,
      value,
      textarea,
      multiple,
      maxLength,
      minLength,
      items,
      selectedItems,
      _key,
      number,
      phone,
      uiStepper,
      minStep,
      maxStep,
      step,
      subHead,
      singleChoice,
      hideSearch,
      showCancelButton,
      countries,
      tagText,
      tagTextMultiline,
      switchBiState,
      explanationText,
      explanationIcon,
      multiImage,
      imageNumber = 3,
      ...rest
    } = this.props;
    const { imagePickerResult } = this.state;
    const {
      inpStyle,
      inpStyleBigger,
      formGroupLabelStyle,
      formGroupContainerStyle,
      textareaStyle,
    } = styles;
    const otherStyle = textarea ? textareaStyle : {};
    let inp;

    switch (type) {
      case INPUT_TYPE_TAGTEXT:
        inp = (
          <TextInput
            editable={!disabled}
            onChangeText={text => change(text, _key)}
            style={{
              ...inpStyle,
              ...otherStyle,
            }}
            placeholderTextColor={colors.placeholderText}
            multiline={false}
            maxLength={maxLength}
            keyboardType={this.checkKeyboard(number, phone)}
            {...rest}>
            <TwitterTextView>{value}</TwitterTextView>
          </TextInput>
        );
        break;
      case INPUT_TYPE_TAGTEXTMULTILINE:
        inp = (
          <TextInput
            editable={!disabled}
            onChangeText={text => change(text, _key)}
            style={{
              ...inpStyle,
              ...otherStyle,
              height: hp('20%'),
            }}
            numberOfLines={6}
            placeholderTextColor={colors.placeholderText}
            multiline={true}
            maxLength={maxLength}
            keyboardType={this.checkKeyboard(number, phone)}
            {...rest}>
            <TwitterTextView>{value}</TwitterTextView>
          </TextInput>
        );
        break;
      case INPUT_TYPE_UISTEPPER:
        inp = (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingRight: 20,
              marginTop: 10,
              paddingLeft: 30,
            }}>
            <Text style={inpStyle}>{subHead ? i18n.t(subHead) : ''}</Text>
            <UIStepper
              disabled={disabled}
              height={40}
              width={120}
              displayValue
              minimumValue={minStep}
              maximumValue={maxStep}
              steps={step}
              value={value}
              initialValue={value}
              borderColor={colors.primary}
              textColor={colors.primary}
              tintColor={colors.primary}
              onValueChange={next => {
                change(next, _key);
              }}
            />
          </View>
        );
        break;
      case INPUT_TYPE_MULTIPLE:
        const translatedItems2 = this.translateArray(items);
        // console.log('translatedItems items', items)
        // console.log('translatedItems translatedItems2', translatedItems2)
        inp = (
          <SectionedMultiSelect
            styles={{
              container: { maxHeight: '80%', marginTop: 100 },
              selectToggle: { padding: 5 },
              selectToggleText: { marginLeft: 30 },
              itemIconStyle: { height: 25, width: 25 },
            }}
            disabled={disabled}
            items={translatedItems2}
            single={singleChoice}
            hideSearch={hideSearch}
            showCancelButton={showCancelButton}
            uniqueKey="id"
            subKey="children"
            iconKey="icon"
            colors={{ primary: colors.primary }}
            confirmText={i18n.t('__confirm')}
            selectText={rest.placeholder}
            showDropDowns={true}
            expandDropDowns={true}
            onSelectedItemsChange={text => change(text, _key)}
            selectedItems={Array.isArray(value) ? value : []}
            IconRenderer={Icon.MaterialIcons}
          />
        );
        break;

      case INPUT_TYPE_DATE:
        inp = (
          <View>
            {explanationText && explanationIcon ? (
              <TouchableOpacity
                onPress={() => {
                  this.setState({ showInfo: true });
                  //to reset trigger for future time
                  setTimeout(() => this.setState({ showInfo: false }), 1000);
                }}>
                <Icon.Feather size={20} name="info" />
              </TouchableOpacity>
            ) : null}
            <Button
              disabled={disabled}
              icon={({ size, color }) => (
                <Icon.Feather name="calendar" color={color} size={size} />
              )}
              uppercase={false}
              onPress={showDateHandler}>
              {value
                ? moment(value).format(format || defaultDateFormat)
                : i18n.t('__select_date')}
            </Button>
            {explanationText && explanationIcon ? (
              <BottomDialog
                visible={this.state.showInfo}
                message={i18n.t(explanationText)}
                icon={explanationIcon}
              />
            ) : null}
          </View>
        );
        break;
      case INPUT_TYPE_SWITCHBISTATE:
        return (
          <View>
            <Text style={inpStyle}>
              {label ? i18n.t(label) + ' ' : ''}
              {explanationText && explanationIcon ? (
                <TouchableOpacity
                  onPress={() => {
                    this.setState({ showInfo: true });
                    //to reset trigger for future time
                    setTimeout(() => this.setState({ showInfo: false }), 1000);
                  }}>
                  <Icon.Feather size={20} name="info" />
                </TouchableOpacity>
              ) : null}
            </Text>
            <View
              style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
              <Text style={inpStyleBigger}>
                {value ? (
                  <Image
                    source={explanationIcon}
                    style={{
                      width: icon_size_small,
                      height: icon_size_small,
                      marginBottom: 10,
                      marginRight: 5,
                      marginLeft: 5,
                    }}
                  />
                ) : null}
                {subHead ? ' ' + i18n.t(subHead) : ''}
              </Text>
              <Switch
                value={value}
                color={colors.primary}
                onValueChange={text => change(text, _key)}
              />
              {explanationText && explanationIcon ? (
                <BottomDialog
                  visible={this.state.showInfo}
                  message={i18n.t(explanationText)}
                  icon={explanationIcon}
                />
              ) : null}
            </View>
          </View>
        );
      case INPUT_TYPE_MULTIIMAGE_PICKER:
        return (
          <View style={{ flex: 1, flexDirection: 'row', marginTop: 10 }}>
            <TouchableOpacity
              style={styles.button}
              disabled={value && value.length >= imageNumber}
              onPress={this.onImagePickerChange.bind(this)}>
              <Icon.Feather
                name="image"
                size={50}
                color={
                  value && value.length >= imageNumber
                    ? colors.gray
                    : colors.black
                }
              />
            </TouchableOpacity>

            {Array.isArray(value) ? (
              <View
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                }}>
                {value.map(element => (
                  <View style={{ marginLeft: 10 }} key={element}>
                    <ImageBackground
                      source={{ uri: element }}
                      style={styles.thumbnail}>
                      <TouchableOpacity
                        style={styles.button}
                        onPress={() => this.removeItem(element, value)}>
                        <Text
                          style={{
                            fontSize: 50,
                            color: colors.white,
                            lineHeight: 42,
                            paddingTop: 10,
                            paddingLeft: 10,
                          }}>
                          ×
                        </Text>
                      </TouchableOpacity>
                    </ImageBackground>
                  </View>
                ))}
              </View>
            ) : (
              <></>
            )}
          </View>
        );
      // break;
      case INPUT_TYPE_MULTIIMAGE:
        inp = (
          <Fragment>
            {Array.isArray(value) ? (
              value.map(element => (
                <View style={{ flex: 1 }} key={element}>
                  <ImageBackground
                    source={{ uri: element }}
                    style={styles.image}>
                    <TouchableOpacity
                      style={styles.button}
                      onPress={() => this.removeItem(element, value)}>
                      <Text
                        style={{
                          fontSize: 50,
                          color: colors.white,
                          lineHeight: 42,
                          paddingTop: 10,
                          paddingLeft: 10,
                        }}>
                        ×
                      </Text>
                    </TouchableOpacity>
                  </ImageBackground>
                </View>
              ))
            ) : (
              <Image source={DummyEvent} style={styles.image} />
            )}
            <View style={{ flex: 1, flexDirection: 'row' }}>
              <Button
                disabled={disabled}
                icon={({ size, color }) => (
                  <Icon.Feather name="image" color={color} size={size} />
                )}
                uppercase={false}
                onPress={this.onImagePickerChange.bind(this)}>
                {i18n.t('__pick_image')}
              </Button>
              <Button
                disabled={disabled}
                icon={({ size, color }) => (
                  <Icon.Feather name="camera" color={color} size={size} />
                )}
                uppercase={false}
                onPress={this.onCameraPickerChange.bind(this)}>
                {i18n.t('__take_image')}
              </Button>
            </View>
          </Fragment>
        );
        break;
      case INPUT_TYPE_IMAGE:
        inp = (
          <Fragment>
            {imagePickerResult ? (
              <Image
                source={{ uri: `${imagePickerResult}` }}
                style={styles.image}
              />
            ) : typeof value === 'string' || value instanceof String ? (
              <Image
                source={{ uri: `${value}?${new Date().toISOString()}` }}
                style={styles.image}
              />
            ) : value !== '' ? (
              <Image source={value} style={styles.image} />
            ) : (
              <Image source={DummyEvent} style={styles.image} />
            )}
            <View style={{ flex: 1, flexDirection: 'row' }}>
              <Button
                disabled={disabled}
                icon={({ size, color }) => (
                  <Icon.Feather name="image" color={color} size={size} />
                )}
                uppercase={false}
                onPress={this.onImagePickerChange.bind(this)}>
                {i18n.t('__pick_image')}
              </Button>
              <Button
                disabled={disabled}
                icon={({ size, color }) => (
                  <Icon.Feather name="camera" color={color} size={size} />
                )}
                uppercase={false}
                onPress={this.onCameraPickerChange.bind(this)}>
                {i18n.t('__take_image')}
              </Button>
            </View>
          </Fragment>
        );
        break;
      case INPUT_TYPE_PICKER:
        const translatedItems = JSON.parse(JSON.stringify(items));
        translatedItems.forEach(
          (e, i, a) =>
            (a[i] = {
              label:
                e.label.substring(0, 2) == '__' ? i18n.t(e.label) : e.label,
              value: e.value,
            }),
        );
        // console.log('translatedItems weww', translatedItems)

        inp = (
          <RNPickerSelect
            disabled={disabled}
            itemKey={value} //bug from component that triggers too many requests
            value={value}
            onValueChange={text => change(text, _key)}
            items={translatedItems}
            style={inpStyle}
            pickerProps={rest}
            placeholder={{ label: rest.placeholder || '-' }}
            placeholderTextColor={colors.placeholderText}
            textInputProps={{
              style: {
                color: '#595959',
                fontFamily: 'medium',
                paddingVertical: 2,
              },
            }}
            useNativeAndroidPickerStyle={false}
          />
        );
        break;

      case INPUT_TYPE_TEXTAREA:
        inp = (
          <TextInput
            editable={!disabled}
            onChangeText={text => change(text, _key)}
            style={{
              ...inpStyle,
              ...otherStyle,
              height: hp('20%'),
            }}
            placeholderTextColor={colors.placeholderText}
            value={
              typeof value === 'object' || value === undefined ? '' : `${value}`
            }
            multiline={textarea}
            maxLength={maxLength}
            keyboardType={this.checkKeyboard(number, phone)}
            {...rest}
          />
        );
        break;
      case INPUT_TYPE_PICKER_COUNTRY_PHONE:
        inp = (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingRight: 20,
              marginTop: 10,
              paddingLeft: 30,
            }}>
            <Text style={inpStyle}>{value}</Text>
            <CountryPicker
              withFilter
              withFlag
              withCallingCode
              withCountryNameButton
              // countryCode
              onSelect={next => {
                console.log('CountryPicker ', next);
                console.log('CountryPicker this.state', next);
                change(next, _key);
              }}
              visible={true}
            />
          </View>
        );
        break;
      case INPUT_TYPE_PICKER_COUNTRY:
        inp = (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingRight: 20,
              marginTop: 10,
              paddingLeft: 30,
            }}>
            <Text style={inpStyle}>{value}</Text>
            <CountryPicker
              withFilter
              withFlag
              withCallingCode
              withCountryNameButton
              // countryCode
              onSelect={next => {
                console.log('CountryPicker ', next);
                change(next, _key);
              }}
              // visible={true}
            />
          </View>
        );
        break;

      default:
        inp = (
          <TextInput
            editable={!disabled}
            onChangeText={text => change(text, _key)}
            style={{
              ...inpStyle,
              ...otherStyle,
            }}
            placeholderTextColor={colors.placeholderText}
            value={
              typeof value === 'object' || value === undefined ? '' : `${value}`
            }
            multiline={textarea}
            maxLength={maxLength}
            keyboardType={this.checkKeyboard(number, phone)}
            {...rest}
          />
        );
        break;
    }

    return (
      <Surface style={formGroupContainerStyle}>
        <Text style={formGroupLabelStyle}>
          {i18n.t(label) && i18n.t(label).toUpperCase()} {!optional && '*'}
        </Text>
        {inp}
      </Surface>
    );
  }
}

export { Input };

const styles = StyleSheet.create({
  inpStyle: {
    color: '#595959',
    fontFamily: 'medium',
    height: 30,
    padding: 0,
  },
  inpStyleBigger: {
    color: '#595959',
    fontFamily: 'bold',
    fontSize: 18,
    height: 45,
    padding: 0,
  },
  formGroupContainerStyle: {
    borderColor: '#EAEAEA',
    backgroundColor: colors.background,
    borderRadius: 5,
    borderWidth: 2,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginBottom: 20,
  },
  textareaStyle: {
    justifyContent: 'flex-start',
    height: 'auto',
    maxHeight: 100,
  },
  formGroupLabelStyle: {
    color: '#A4A4A4',
    fontFamily: 'bold',
    fontSize: 10,
  },
  thumbnail: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    borderColor: '#d00',
    borderWidth: 2,
    backgroundColor: '#aaa',
  },
  image: {
    width: '100%',
    height: 220,
    borderRadius: 5,
    resizeMode: 'contain',
    marginVertical: 10,
  },
  hashtagStyle: {
    color: 'blue',
  },
  mentionStyle: {
    color: 'green',
  },
  linkStyle: {
    color: 'blue',
  },
  emailStyle: {
    color: 'blue',
  },
});
