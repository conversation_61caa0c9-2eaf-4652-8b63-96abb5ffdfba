/* @flow */

import * as React from 'react';
import { Platform, View } from 'react-native';
import { RadioButton, withTheme, Theme } from 'react-native-paper';
import { Image } from 'react-native';
// import RadioButtonGroup, { RadioButtonContext } from 'react-native-paper/lib/typescript/src/components/RadioButton/RadioButtonGroup';
// import RadioButtonAndroid from 'react-native-paper/lib/typescript/src/components/RadioButton/RadioButtonAndroid';
// import RadioButtonIOS from 'react-native-paper/lib/typescript/src/components/RadioButton/RadioButtonIOS';
// import { withTheme } from 'react-native-paper/src/core/theming';
// import { Theme } from 'react-native-paper';

type Props = {
  /**
   * Value of the radio button
   */
  value: string,
  /**
   * Status of radio button.
   */
  status?: 'checked' | 'unchecked',

  /**
   * icon associated with
   */
  icon?: string,
  size?: number,
  /**
   * Whether radio is disabled.
   */
  disabled?: boolean,
  /**
   * Function to execute on press.
   */
  onPress?: () => mixed,
  /**
   * Custom color for unchecked radio.
   */
  uncheckedColor?: string,
  /**
   * Custom color for radio.
   */
  color?: string,
  /**
   * @optional
   */
  theme: Theme,
};

/**
 * Radio buttons allow the selection a single option from a set.
 *
 * <div class="screenshots">
 *   <figure>
 *     <img src="screenshots/radio-enabled.android.png" />
 *     <figcaption>Android (enabled)</figcaption>
 *   </figure>
 *   <figure>
 *     <img src="screenshots/radio-disabled.android.png" />
 *     <figcaption>Android (disabled)</figcaption>
 *   </figure>
 *   <figure>
 *     <img src="screenshots/radio-enabled.ios.png" />
 *     <figcaption>iOS (enabled)</figcaption>
 *   </figure>
 *   <figure>
 *     <img src="screenshots/radio-disabled.ios.png" />
 *     <figcaption>iOS (disabled)</figcaption>
 *   </figure>
 * </div>
 *
 * ## Usage
 * ```js
 * import * as React from 'react';
 * import { View } from 'react-native';
 * import { RadioButton } from 'react-native-paper';
 *
 * export default class MyComponent extends React.Component {
 *   state = {
 *     checked: 'first',
 *   };
 *
 *   render() {
 *     const { checked } = this.state;
 *
 *     return (
 *       <View>
 *         <RadioButton
 *           value="first"
 *           status={checked === 'first' ? 'checked' : 'unchecked'}
 *           onPress={() => { this.setState({ checked: 'first' }); }}
 *         />
 *         <RadioButton
 *           value="second"
 *           status={checked === 'second' ? 'checked' : 'unchecked'}
 *           onPress={() => { this.setState({ checked: 'second' }); }}
 *         />
 *       </View>
 *     );
 *   }
 * }
 * ```
 */
class SIRadioButton extends React.Component<Props> {
  // @component ./RadioButtonGroup.js
  static Group = RadioButton.Group;

  // @component ./RadioButtonAndroid.js
  static Android = RadioButton.Android;

  // @component ./RadioButtonIOS.js
  static IOS = RadioButton.IOS;

  handlePress = context => {
    const { onPress } = this.props;
    const { onValueChange } = context || {};

    onPress ? onPress() : onValueChange(this.props.value);
  };

  isChecked = context =>
    context.value === this.props.value ? 'checked' : 'unchecked';

  render() {
    const Button = Platform.select({
      default: RadioButton.Android,
      ios: RadioButton.IOS,
    });

    const context = {};
    return this.props.icon ? (
      <View style={{ flexDirection: 'row', padding: 5, paddingRight: 10 }}>
        <Button
          {...this.props}
          status={this.props.status || (context && this.isChecked(context))}
          onPress={() => {
            return this.handlePress(context);
          }}
        />
        <Image
          style={{
            height: this.props.size ?? 40,
            width: this.props.size ?? 40,
          }}
          source={this.props.icon}
        />
      </View>
    ) : (
      <Button
        {...this.props}
        status={this.props.status || (context && this.isChecked(context))}
        onPress={() => {
          return this.handlePress(context);
        }}
      />
    );
  }
}

export default withTheme(SIRadioButton);
