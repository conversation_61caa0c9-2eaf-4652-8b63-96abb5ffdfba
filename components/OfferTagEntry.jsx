import React, { useEffect, useState } from 'react';
import { View } from 'react-native';
import {
  IconButton,
  Avatar,
  Paragraph,
  Card,
  Caption,
} from 'react-native-paper';
import * as Icon from '@expo/vector-icons';
import { colors, getCachedLogin, getDateTime, getFirstImage } from '@constants';
import { EVENT_DETAILS_ROUTE } from '@routes/route_constants';
import HighlightText from '@sanar/react-native-highlight-text';
import { useSelector } from 'react-redux';
import { isLoggedIn } from '@reducers/account.reducer';
import { useNavigation } from '@react-navigation/native';

const OfferTagEntry = ({ item, tag }) => {
  const navigation = useNavigation();
  const loggedIn = useSelector(appState => isLoggedIn(appState.account));
  const [userNickname, setUserNickname] = useState(null);

  useEffect(() => {
    const getUserNickName = async () => {
      const login = await getCachedLogin(item.userId);
      setUserNickname(login);
    };
    getUserNickName();
  }, [item]);

  return (
    <View style={{ marginBottom: 5, marginStart: 5, marginEnd: 5 }}>
      <Card>
        <Card.Content>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <View style={{ flexDirection: 'column', alignItems: 'center' }}>
              <Avatar.Image
                source={getFirstImage(item)}
                size={50}
                style={{ backgroundColor: colors.whiteBackground }}
              />

              <IconButton
                onPress={() =>
                  navigation.push(EVENT_DETAILS_ROUTE, {
                    id: item.id,
                    userId: item.userId,
                  })
                }
                color={colors.blue}
                size={24}
                icon={({ size, color }) => (
                  <Icon.MaterialCommunityIcons
                    name="eye"
                    color={color}
                    size={size}
                  />
                )}
              />
            </View>
            <View style={{ flex: 1, marginLeft: 10 }}>
              <Caption
                style={{
                  fontFamily: 'medium',
                  color: 'black',
                  lineHeight: 16,
                  fontSize: 14,
                  margin: 0,
                }}>
                <HighlightText
                  highlightStyle={{ backgroundColor: 'yellow' }}
                  searchWords={[tag]}
                  textToHighlight={item.name}
                />
              </Caption>
              <Paragraph>
                <HighlightText
                  highlightStyle={{ backgroundColor: 'yellow' }}
                  searchWords={[tag]}
                  textToHighlight={item.presentation}
                />
              </Paragraph>
              <Paragraph>
                <Icon.Feather name="calendar" />
                {getDateTime(item.date)}
              </Paragraph>
              <Paragraph
                style={{
                  marginLeft: 0,
                  color: '#333',
                  fontFamily: 'medium',
                }}>
                {loggedIn ? `@${userNickname}` : ''}
              </Paragraph>
            </View>
          </View>
        </Card.Content>
      </Card>
    </View>
  );
};

export default OfferTagEntry;
