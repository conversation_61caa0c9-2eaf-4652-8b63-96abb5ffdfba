import React, { useEffect, useState, useCallback } from 'react';
import { Appbar, Avatar } from 'react-native-paper';
import i18n from '@translations/index';
import { TouchableOpacity } from 'react-native';
import { useSelector } from 'react-redux';
import { colors } from '@constants/index';
import { MESSAGES_ROUTE } from '@routes/route_constants';
import { isLoggedIn } from '@reducers/account.reducer';
import { useNavigation } from '@react-navigation/native';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import useUserNickname from '@hooks/useUserNickname';

const { Header, Content, Action, BackAction } = Appbar;

const MainHeader = ({
  title,
  subtitle,
  hasBack,
}: {
  title: string;
  subtitle?: string;
  hasBack?: boolean;
}) => {
  const [imageHash, setImageHash] = useState(Date.now());
  const navigation = useNavigation();
  const messages = useSelector(appState => appState.messages);
  const account = useSelector(appState => appState.account);
  const loggedIn = useSelector(appState => isLoggedIn(appState.account));
  const tryRenderNickName = useUserNickname(title || '');

  useEffect(() => {
    if (account.fetching) {
      setImageHash(Date.now());
    }
  }, [account.fetching]);

  const showMessageButton = useCallback(() => {
    const avoidScreens = [
      '__my_messages',
      '__login',
      '__register',
      '__reset_password',
      '__payment',
      '__blocked_users',
      '__reviews',
      '__feedbacks',
      '__phone_number',
      '__delete_profile',
      '__edit_profile',
      '__create_event',
    ];
    const containsAtOrHash = (str: string) => /[@|#]/.test(str);
    if (loggedIn && !avoidScreens.includes(title) && !containsAtOrHash(title)) {
      const icon =
        messages.unreadedMessages?.length > 0
          ? 'message-badge-outline'
          : 'message-outline';
      return (
        <Action
          icon={icon}
          onPress={() => navigation.navigate(MESSAGES_ROUTE)}
        />
      );
    }
    return null;
  }, [loggedIn, title, messages.unreadedMessages?.length, navigation]);

  const renderSubTitle = useCallback(() => {
    return subtitle?.startsWith('__') ? i18n.t(subtitle) : subtitle;
  }, [subtitle]);

  const renderTitle = () => {
    let renderedTitle = title;
    if (title.includes('|')) {
      renderedTitle = tryRenderNickName ?? '';
    } else if (title.startsWith('__')) {
      renderedTitle = i18n.t(title);
    }
    return renderedTitle;
  };

  const renderAvatar = () => {
    return account.account !== null &&
      account.account.imageUrl !== null &&
      account.account.imageUrl !== '' ? (
      <TouchableOpacity
        style={{ marginLeft: 10 }}
        testID="avatarButton"
        onPress={navigation.openDrawer}>
        <Avatar.Image
          size={35}
          source={{
            uri: `${account.account.imageUrl}?${imageHash}`,
          }}
          style={{ backgroundColor: colors.whiteBackground }}
        />
      </TouchableOpacity>
    ) : (
      <Action icon="menu" testID="menuButton" onPress={navigation.openDrawer} />
    );
  };

  return (
    <Header style={{ elevation: 0, marginTop: 0, height: hp('5%') }}>
      {hasBack ? <BackAction onPress={navigation.goBack} /> : renderAvatar()}

      <Content title={renderTitle()} subtitle={renderSubTitle()} />
      {showMessageButton()}
    </Header>
  );
};

export { MainHeader };
