import TwitterTextView from 'react-native-twitter-textview';
import React from 'react';
import { HASHTAG_ROUTE, USER_INFO_ROUTE } from '@routes/route_constants';
import { useNavigation } from '@react-navigation/native';

const TagText = props => {
  const navigation = useNavigation();
  // Declare a new state variable, which we'll call "count"
  // const [policies, setPolicies] = useState(new Set())

  const onHashTagPressed = (e, hashTag) => {
    navigation.navigate(HASHTAG_ROUTE, { tag: hashTag.trim() });
  };
  const onPressMention = (e, mentionTag) => {
    navigation.navigate(USER_INFO_ROUTE, {
      userInfoId: mentionTag.replace('@', '').trim(),
    });
  };
  return (
    <TwitterTextView
      onPressHashtag={onHashTagPressed}
      onPressMention={onPressMention}>
      {props.value}
    </TwitterTextView>
  );
};

export default TagText;
