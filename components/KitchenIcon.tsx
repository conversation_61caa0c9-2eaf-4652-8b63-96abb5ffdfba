import React, { FC } from 'react';
import { Image, ImageSourcePropType } from 'react-native';
import {
  getIconFromArray,
  icon_size_small,
  images,
  typeKitchen,
} from '@constants';

interface KitchenIconProps {
  size?: number;
  type: string;
}

const KitchenIcon: FC<KitchenIconProps> = props => {
  const size = props.size ? props.size : icon_size_small;

  return (
    <Image
      style={{
        height: size,
        width: size,
      }}
      source={
        getIconFromArray(
          typeKitchen,
          props.type,
          images._kitchen_default,
        ) as ImageSourcePropType
      }
    />
  );
};
export default KitchenIcon;
