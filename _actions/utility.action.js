export const UPLOAD_EVENT_FILE_REQUEST = 'UPLOAD_EVENT_FILE_REQUEST';
export const UPLOAD_FILE_REQUEST = 'UPLOAD_FILE_REQUEST';
import { agent } from '@services';
import { Platform } from 'react-native';
import mime from 'mime';

function getFileFormData(file) {
  const formdata = new FormData();
  formdata.append('file', {
    uri: Platform.OS === 'android' ? file : file.replace('file:/', ''),
    type: mime.getType(file),
    name: file.split('/').pop() ?? 'image.png',
  });
  return formdata;
}

export const uploadEventFile = async (file, onUploadProgress) => {
  const formdata = new FormData();
  console.log('uploadEventFile before ___ ', file);

  formdata.append('file', {
    uri: Platform.OS === 'android' ? file.uri : file.uri.replace('file:/', ''),
    type: mime.getType(file),
    name: file.split('/').pop() ?? 'image.png',
  });
  const responseData = await agent
    .uploadImageEvent(formdata, onUploadProgress)
    .then(response => response.json())
    .catch(e => console.log('uploadImageEvent error', e));

  console.log('uploadEventFile response ', responseData);
  return {
    type: UPLOAD_EVENT_FILE_REQUEST,
    payload: responseData,
  };
};

export const uploadGenericFile = async (file, authToken, onUploadProgress) => {
  console.log('uploadGenericFile before ___ ', file);
  const responseData = await agent
    .uploadImageGeneric(getFileFormData(file), onUploadProgress)
    .then(response => response)
    .catch(e => console.log('uploadImageGeneric error', e));

  return {
    type: UPLOAD_FILE_REQUEST,
    payload: responseData,
  };
};

export const uploadFile = async file => {
  console.log('uploadFile before ___ ', file);
  const responseData = await agent
    .uploadImageProfile(getFileFormData(file))
    .then(response => response)
    .catch(e => console.log('uploadImageProfile error', e));

  return {
    type: UPLOAD_FILE_REQUEST,
    payload: responseData,
  };
};
