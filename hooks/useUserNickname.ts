import { useEffect, useState } from 'react';
import { getCachedLogin } from '@constants/functions';
import i18n from '@translations/index';
import { ellipsis } from '@constants/functions';

const useUserNickname = (
  userId: string | null,
  isLoggedIn: boolean = true,
  ellipsisValue: number = 50,
) => {
  const [userNickname, setUserNickname] = useState<string | null>(userId);

  useEffect(() => {
    const getUserNickName = async () => {
      if (isLoggedIn === false) {
        setUserNickname(i18n.t('__hidden_user'));
      } else if (userId && userId.includes('|')) {
        const login = await getCachedLogin(userId);

        setUserNickname(login !== null ? ellipsis(login, ellipsisValue) : null);
      }
    };
    getUserNickName();
  }, [userId, isLoggedIn, ellipsisValue]);

  return userNickname;
};
export default useUserNickname;
