import { useEffect, useState } from 'react';
import { getAvatar } from '@constants';

const useUserAvatar = (userId?: string) => {
  const [userAvatar, setUserAvatar] = useState(userId);
  useEffect(() => {
    const getUserAvatar = () => {
      if (userId) {
        const avatar = getAvatar(userId);
        setUserAvatar(avatar);
      }
    };
    getUserAvatar();
  }, [userId]);

  return userAvatar;
};

export default useUserAvatar;
