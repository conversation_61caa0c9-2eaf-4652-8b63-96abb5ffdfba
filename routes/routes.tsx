import React from 'react';
import MainMenu from '@components/MainMenu';
import { Feedback } from '@screens/Feedback';
import { EditAvatar } from '@screens/EditAvatar';
import { AddPhoneNumber } from '@screens/AddPhoneNumber';
import Profile from '@screens/Profile';
import SearchScreen from '@screens/Search';
import IncomingEventRequests from '@screens/IncomingEventRequests';
import Messages from '@screens/Messages';
import Chat from '@screens/Chat';
import MyEvents from '@screens/MyEvents';
import Map from '@screens/Map';
import * as Icon from '@expo/vector-icons';

// **************************************************************
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// **************************************************************

const headerOptions = {
  headerShown: true,
};
const noHeaderOptions = {
  headerTransparent: true,
  headerBackTitle: '',
  title: '',
  headerBackTitleVisible: false,
};
const chatHeaderOptions = {
  headerTintColor: 'white',
  headerStyle: {
    backgroundColor: colors.primary,
  },
};
// Drawer stack definition

const SearchNavigator = createStackNavigator();

const buildHeaderSearch = (props: any) => (
  <MainHeader title="__search_desc" subtitle="__search_sub_desc" {...props} />
);
const MessageStackHeader = props => (
  <MainHeader title="__my_messages" {...props} />
);
const UserInfoHeader = props => (
  <MainHeader title="" subtitle="__user_info_sub" {...props} hasBack={true} />
);
const EventRequestStackHeader = props => (
  <MainHeader
    title="__outgoing_requests"
    subtitle="__your_requests_sub"
    {...props}
  />
);
const PaymentHeader = props => (
  <MainHeader title="__payment" {...props} hasBack={true} />
);
const MyEventsHeader = props => (
  <MainHeader title="__my_events" subtitle="__my_events_sub" {...props} />
);

const MyEventsHeaderComplex = props => {
  console.log('MyEventsHeaderComplex', props.route.params);
  // TODO: check here after converting the UserInfo as FC
  const nickname = useUserNickname(props.route.params?.login);
  if (props.route.params?.readOnly) {
    console.log('navigationOptions nickname', nickname);
    return (
      <MainHeader
        title={nickname}
        subtitle="__user_past_created"
        hasBack={true}
        {...props}
      />
    );
  } else {
    return MyEventsHeader(props);
  }
};

const MainHeaderComponent = props => (
  <MainHeader
    title="__incoming_requests"
    subtitle="__event_offers_sub"
    {...props}
  />
);
const ProfileHeader = props => <MainHeader title="__profile" {...props} />;
const DeleteProfileHeader = props => (
  <MainHeader title="__delete_profile" {...props} hasBack={true} />
);
const BlockedUsersHeader = props => (
  <MainHeader title="__blocked_users" {...props} hasBack={true} />
);
const EditProfileHeader = (props: any) => (
  <MainHeader title="__edit_profile" {...props} hasBack={true} />
);
const EditAvatarHeader = (props: any) => (
  <MainHeader title="__edit_avatar" {...props} hasBack={true} />
);
const PhoneNumberHeader = (props: any) => (
  <MainHeader title="__phone_number" {...props} hasBack={true} />
);
const ReviewHeader = (props: any) => (
  <MainHeader title="__reviews" {...props} hasBack={true} />
);
const FeedbackHeader = (props: any) => (
  <MainHeader title="__feedbacks" {...props} hasBack={true} />
);
const TabBarIconReviewHeader = ({ color, size }) => {
  return <Icon.MaterialIcons name="rate-review" size={size} color={color} />;
};
const TabBarIconFeedbackHeader = ({ color, size }) => {
  return <Icon.MaterialIcons name="feedback" size={size} color={color} />;
};

function SearchStack() {
  return (
    <SearchNavigator.Navigator>
      <SearchNavigator.Screen
        name="searchstack"
        component={SearchScreen}
        options={{
          ...headerOptions,
          header: buildHeaderSearch,
        }}
      />
      <SearchNavigator.Screen
        name="map"
        options={noHeaderOptions}
        component={Map}
      />
      <SearchNavigator.Screen
        name="createeventstack"
        component={CreateEvent}
        options={({ route }) => ({
          ...headerOptions,
          header: props => <MainHeader title={
          route.params && route.params.event === undefined
            ? i18n.t('__create_event')
            : route.params && route.params.clone
            ? i18n.t('__clone_event')
            : i18n.t('__edit_event')} {...props} hasBack={true} />,
        })}
      />
      <SearchNavigator.Screen
        name="hashtagstack"
        component={Hashtags}
        options={({ route }) => ({
          ...headerOptions,
          header: props => (
            <MainHeader title={route.params.tag} {...props} hasBack={true} />
          ),
        })}
      />
      <SearchNavigator.Screen
        key="eventdetailsstack"
        name="eventdetailsstack"
        component={EventDetails}
        options={noHeaderOptions}
      />
      <SearchNavigator.Screen
        name={REVIEW_HOST_LIST_ROUTE}
        component={ReviewListStack}
        options={noHeaderOptions}
      />

      <SearchNavigator.Screen
        name="userinfostack"
        component={UserInfo}
        options={({ route }) => ({
          header: props => {
            console.log('userinfostack header', route.params);
            return (
              <MainHeader
                title={route.params.userInfoId}
                {...props}
                subtitle="__user_info_sub"
                hasBack={true}
              />
            );
          },
        })}
      />

      <SearchNavigator.Screen
        name="messagestack"
        component={Messages}
        options={{
          ...headerOptions,
          header: MessageStackHeader,
        }}
      />
      <SearchNavigator.Screen
        name="chatstack"
        component={Chat}
        options={({ route }) => ({
          ...chatHeaderOptions,
          header: props => {
            console.log('chatstack', route.params);
            return (
              <MainHeader
                title={
                  route.params.login !== route.params.item.userTx
                    ? route.params.item.userTx
                    : route.params.item.userRx
                }
                {...props}
                hasBack={true}
              />
            );
          },
        })}
      />

      <SearchNavigator.Screen
        name="myeventsstack"
        component={MyEvents}
        options={props => ({
          header: MyEventsHeaderComplex(props),
        })}
      />

      <SearchNavigator.Screen
        name="feedbackreviewtab"
        component={FeedbackReviewTab}
        options={{
          headerShown: false,
        }}
      />

      <SearchNavigator.Screen
        name="eventrequeststack"
        component={YourRequests}
        options={{
          ...headerOptions,
          header: EventRequestStackHeader,
        }}
      />

      <SearchNavigator.Screen
        name="incomingeventrequestsstack"
        component={IncomingEventRequests}
        options={{
          ...headerOptions,
          header: MainHeaderComponent,
        }}
      />

      <SearchNavigator.Screen
        name="paymentstack"
        component={Payment}
        options={{
          ...headerOptions,
          header: PaymentHeader,
        }}
      />
    </SearchNavigator.Navigator>
  );
}

const EventDetailsNavigator = createStackNavigator();
function EventDetailsStack(): React.JSX.Element {
  return (
    <EventDetailsNavigator.Navigator>
      <EventDetailsNavigator.Screen
        name="eventdetailsstack"
        component={EventDetails}
        options={{
          // ...headerOptions,
          // headerBackTitle: '',
          headerTransparent: true,
          headerShown: false,
          // header: props => <MainHeader {...props} />,
        }}
      />

      <EventDetailsNavigator.Screen
        name="userinfostack"
        component={UserInfo}
        options={{
          ...headerOptions,
          header: UserInfoHeader,
        }}
      />
      <EventDetailsNavigator.Screen
        name="hashtagstack"
        component={Hashtags}
        options={({ route }) => ({
          ...headerOptions,
          header: props => (
            <MainHeader
              title={route.params?.tag ?? '__tags'}
              {...props}
              hasBack={true}
            />
          ),
        })}
      />
    </EventDetailsNavigator.Navigator>
  );
}

const EventRequestNavigator = createStackNavigator();
function EventRequestStack() {
  return (
    <EventRequestNavigator.Navigator>
      <EventRequestNavigator.Screen
        name="eventrequeststack"
        component={YourRequests}
        options={{
          ...headerOptions,
          header: EventRequestStackHeader,
        }}
      />

      <EventRequestNavigator.Screen
        name="reviewstack"
        component={Review}
        options={({ route }) => ({
          ...headerOptions,
          header: props => (
            <MainHeader title={route.params.type} {...props} hasBack={true} />
          ),
        })}
      />

      <EventRequestNavigator.Screen
        name="eventdetailsstack"
        component={EventDetails}
        options={noHeaderOptions}
      />

      <EventRequestNavigator.Screen
        name="paymentstack"
        component={Payment}
        options={{
          ...headerOptions,
          header: PaymentHeader,
        }}
      />
    </EventRequestNavigator.Navigator>
  );
}

const MyEventsNavigator = createStackNavigator();
function MyEventsStack() {
  return (
    <MyEventsNavigator.Navigator>
      <MyEventsNavigator.Screen
        name="myeventsstack"
        component={MyEvents}
        options={{
          ...headerOptions,
          header: MyEventsHeader,
        }}
      />

      <MyEventsNavigator.Screen
        name="eventdetailsstack"
        component={EventDetails}
        options={noHeaderOptions}
      />
    </MyEventsNavigator.Navigator>
  );
}

const IncomingEventRequestsNavigator = createStackNavigator();
function IncomingEventRequestsStack() {
  return (
    <IncomingEventRequestsNavigator.Navigator>
      <IncomingEventRequestsNavigator.Screen
        name="incomingeventrequestsstack"
        component={IncomingEventRequests}
        options={{
          ...headerOptions,
          header: MainHeaderComponent,
        }}
      />

      <IncomingEventRequestsNavigator.Screen
        name="eventdetailsstack"
        component={EventDetails}
        options={noHeaderOptions}
      />
      <IncomingEventRequestsNavigator.Screen
        name="feedbackstack"
        component={Feedback}
        options={({ route }) => ({
          ...headerOptions,
          header: props => (
            <MainHeader title={route.params.type} {...props} hasBack={true} />
          ),
        })}
      />
      <IncomingEventRequestsNavigator.Screen
        name="userinfostack"
        component={UserInfo}
        options={({ route }) => ({
          header: props => {
            return (
              <MainHeader
                title={route.params.userInfoId}
                {...props}
                subtitle="__user_info_sub"
                hasBack={true}
              />
            );
          },
        })}
      />
    </IncomingEventRequestsNavigator.Navigator>
  );
}

const ProfileNavigator = createStackNavigator();
function ProfileStack() {
  return (
    <ProfileNavigator.Navigator>
      <ProfileNavigator.Screen
        name="profilestack"
        component={Profile}
        options={{
          ...headerOptions,
          header: ProfileHeader,
        }}
      />
      <ProfileNavigator.Screen
        name="deleteprofilestack"
        component={DeleteProfile}
        options={{
          ...headerOptions,
          header: DeleteProfileHeader,
        }}
      />
      <ProfileNavigator.Screen
        name="usersblockedstack"
        component={UsersBlocked}
        options={{
          ...headerOptions,
          header: BlockedUsersHeader,
        }}
      />
      <ProfileNavigator.Screen
        name="editprofilestack"
        component={EditProfile}
        options={{
          ...headerOptions,
          header: EditProfileHeader,
        }}
      />
      <ProfileNavigator.Screen
        name="editavatarstack"
        component={EditAvatar}
        options={{
          ...headerOptions,
          header: EditAvatarHeader,
        }}
      />
      <ProfileNavigator.Screen
        name="addphonenumberstack"
        component={AddPhoneNumber}
        options={{
          ...headerOptions,
          header: PhoneNumberHeader,
        }}
      />
      <ProfileNavigator.Screen
        name="feedbackreviewstack"
        component={FeedbackReviewTab}
        options={{
          headerShown: false,
        }}
      />
    </ProfileNavigator.Navigator>
  );
}

const ReviewListNavigator = createStackNavigator();
function ReviewListStack() {
  return (
    <ReviewListNavigator.Navigator screenOptions={{ headerShown: false }}>
      <ReviewListNavigator.Screen
        name="reviewsliststack"
        component={ReviewList}
      />
      {/* <ReviewListNavigator.Screen name="reviewslists" component={ReviewList} /> */}
    </ReviewListNavigator.Navigator>
  );
}
const FeedbackListNavigator = createStackNavigator();
function FeedbackListStack() {
  return (
    <FeedbackListNavigator.Navigator screenOptions={{ headerShown: false }}>
      <FeedbackListNavigator.Screen
        name="feedbacksliststack"
        // name="feedbackslist"
        component={FeedbackList}
      />
    </FeedbackListNavigator.Navigator>
  );
}

const FeedbackReviewStackTabNavigator = createBottomTabNavigator();
const FeedbackReviewTab = () => {
  return (
    <FeedbackReviewStackTabNavigator.Navigator {...{ backBehavior: 'none' }}>
      <FeedbackReviewStackTabNavigator.Screen
        name={i18n.t('__reviews')}
        key={'reviewsliststack'}
        component={ReviewList}
        options={{
          ...headerOptions,
          header: ReviewHeader,
          tabBarIcon: TabBarIconReviewHeader,
        }}
      />
      <FeedbackReviewStackTabNavigator.Screen
        name={i18n.t('__feedbacks')}
        key={'feedbacksliststack'}
        component={FeedbackList}
        options={{
          ...headerOptions,
          header: FeedbackHeader,
          tabBarIcon: TabBarIconFeedbackHeader,
        }}
      />
    </FeedbackReviewStackTabNavigator.Navigator>
  );
};

import { createDrawerNavigator } from '@react-navigation/drawer';
import { MainHeader } from '@components/MainHeader';
import YourRequests from '@screens/YourRequests';
import DeleteProfile from '@screens/DeleteProfile';
import UsersBlocked from '@screens/UsersBlocked';
import EditProfile from '@screens/EditProfile';
import CreateEvent from '@screens/CreateEvent';
import ReviewList from '@screens/ReviewList';
import FeedbackList from '@screens/FeedbackList';
import EventDetails from '@screens/EventDetails';
import {
  CHAT_ROUTE,
  HASHTAG_ROUTE,
  MESSAGES_ROUTE,
  REVIEW_HOST_LIST_ROUTE,
  USER_INFO_ROUTE,
} from './route_constants';
import UserInfo from '@screens/UserInfo';
import * as Linking from 'expo-linking';
import Payment from '@screens/Payment';
import Hashtags from '@screens/Hashtags';
import { Review } from '@screens/Review';
import { colors } from '@constants/colors';
import useUserNickname from '@hooks/useUserNickname';
import i18n from '@translations/index';

const Drawer = createDrawerNavigator();
function Routes() {
  return (
    <Drawer.Navigator
      initialRouteName="search"
      screenOptions={{ drawerStyle: { width: 320 }, headerShown: false }}
      drawerContent={props => <MainMenu {...props} />}>
      <Drawer.Screen name="search" component={SearchStack} />
      <Drawer.Screen name="eventrequest" component={EventRequestStack} />
      <Drawer.Screen name="myevents" component={MyEventsStack} />
      <Drawer.Screen
        name="incomingeventrequests"
        component={IncomingEventRequestsStack}
      />
      <Drawer.Screen name="profile" component={ProfileStack} />
      <Drawer.Screen name="eventdetailsstack" component={EventDetailsStack} />
    </Drawer.Navigator>
  );
}

// useReduxDevToolsExtension(navigationRef);

const pathConfig = {
  screens: {
    EventDetails: {
      path: 'eventdetails/:eventId',
    },
    Hashtags: {
      path: `${HASHTAG_ROUTE}/:paramName/:tagId`,
    },
    Chat: {
      path: CHAT_ROUTE,
    },
    Message: {
      path: MESSAGES_ROUTE,
    },
    UserInfo: {
      path: `${USER_INFO_ROUTE}/:paramName/:id`,
    },
    ReviewListStack: {
      path: 'reviewslist/:paramName/:id',
    },
    ReviewList: {
      path: 'reviewsliststack/:paramName/:id',
    },
    FeedbackList: {
      path: 'feedbacksliststack/:paramName/:id',
    },
    // ===========================================
    Profile: {
      path: 'user/:id/:section',
      parse: {
        id: (id: string) => `user-${id}`,
      },
      stringify: {
        id: (id: string) => id.replace(/^user-/, ''),
      },
    },
  },
};
// // to deeplinking
const linkingConfig = {
  prefixes: [Linking.createURL('/'), 'https://selfinvite.eu', 'selfinvite://'],
  config: pathConfig,
};

export default { Routes, linkingConfig };
