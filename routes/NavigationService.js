// NavigationService.js

// import { NavigationActions } from 'react-navigation'
import { useNavigation } from '@react-navigation/native';

const navigation = useNavigation();
let _navigator;

function setTopLevelNavigator(navigatorRef) {
  console.log('setTopLevelNavigator MOUNTED ');
  _navigator = navigatorRef;
}

function getCurrentNavigationRoute() {
  console.log('getCurrentNavigationRoute ', _navigator);
  if (_navigator && _navigator.state.nav.routes.length > 0) {
    return _navigator.state.nav.routes[_navigator.state.nav.index].routeName;
  }
  return 'EMPTY_ROUTE';
}

function isMounted() {
  return _navigator !== undefined;
}

async function navigate(routeName, params = undefined) {
  if (_navigator !== undefined) {
    console.log('_navigator navigate ', routeName, params);
    _navigator.dispatch(
      NavigationActions.navigate({
        routeName,
        params,
      }),
    );
  } else {
    console.warn('_navigator navigate 2', _navigator);
    const res = NavigationActions.init();
    console.log('_navigator undefined navigate ', res);
    this.timeoutHandle = setTimeout(() => {
      const res2 = navigation.navigate({ routeName, params });
      // console.log('_navigator undefined navigate res2 ', res2)
      // NavigationService.navigate(path)
    }, 1000);
    //   // const res = NavigationActions.init()
    //   // console.log('_navigator undefined navigate ', res)
    //   // const res2 = NavigationActions.navigate({ routeName, params })
    //   // console.log('_navigator undefined navigate res2 ', res2)
  }
}

// add other navigation functions that you need and export them

export default {
  navigate,
  setTopLevelNavigator,
  isMounted,
  getCurrentNavigationRoute,
};
