export const USER_INFO_ROUTE = 'userinfostack';
export const EVENT_DETAILS_ROUTE = 'eventdetailsstack';
export const REVIEW_HOST_LIST_ROUTE = 'reviewhostliststack';
export const MY_EVENT_ROUTE = 'myeventsstack';
export const INCOMING_EVENT_REQUESTS_ROUTE = 'incomingeventrequestsstack';
export const OUTGOING_EVENT_REQUESTS_ROUTE = 'eventrequeststack';
export const PROFILE_ROUTE = 'profilestack';
export const LOGIN_ROUTE = 'loginstack';
export const SEARCH_ROUTE = 'searchstack';
export const FEEDBACK_LIST_ROUTE = 'feedbackliststack';
export const REVIEWS_LIST_ROUTE = 'reviewsliststack';
export const MESSAGES_ROUTE = 'messagestack';
export const CREATE_EVENT_ROUTE = 'createeventstack';
export const EDIT_AVATAR_ROUTE = 'editavatarstack';
export const ADD_PHONE_ROUTE = 'addphonenumberstack';
export const EDIT_PROFILE_ROUTE = 'editprofilestack';
export const FEEDBACK_REVIEWS_ROUTE = 'feedbackreviewstack';
export const FEEDBACK_ROUTE = 'feedbackstack';
export const REVIEW_ROUTE = 'reviewstack';
export const CHAT_ROUTE = 'chatstack';
export const MAP_ROUTE = 'map'; // differ from the other because of the text printed
export const PAYMENT_ROUTE = 'paymentstack';
export const HASHTAG_ROUTE = 'hashtagstack';

// Drawer routes
export const DRAWER_PROFILE_ROUTE = 'profile';
export const DRAWER_SEARCH_ROUTE = 'searchstack';
export const DRAWER_INCOMING_EVENT_REQUEST_ROUTE = 'incomingeventrequests';
export const DRAWER_LOGIN_ROUTE = 'login';
export const DRAWER_LOGIN_ROUTE_SCREEN = { screen: 'loginstack' };
export const DRAWER_MESSAGE_ROUTE = 'message';
export const DRAWER_MY_EVENTS_ROUTE = 'myevents';
export const DRAWER_EVENTS_REQUEST_ROUTE = 'eventrequest';
export const DRAWER_DELETE_PROFILE_ROUTE = 'deleteprofilestack';
export const DRAWER_USERS_BLOCKED_ROUTE = 'usersblockedstack';

// test
export const DRAWER_PAYMENT_ROUTE = 'paymentstack';
