describe('Home Screen', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should have Search screen', async () => {
    await expect(element(by.id('Searchbar'))).toBeVisible();
    await expect(element(by.id('SearchDialog'))).toBeNotVisible();
    await expect(element(by.id('SearchFlatList'))).toBeVisible();
    await expect(element(by.id('SearchbarButton'))).toBeVisible();
    await element(by.id('SearchbarButton')).tap();
    await expect(element(by.id('SearchDialog'))).toBeVisible();

    await expect(element(by.id('SearchDialogPrice'))).toBeVisible();
    await expect(element(by.id('SearchDialogLocation'))).toBeVisible();
    await expect(element(by.id('SearchDialogPriceSlider'))).toBeNotVisible();
    await expect(element(by.id('SearchDialogLocationElements'))).toBeNotVisible();

    

    await element(by.id('SearchDialogButtonClose')).tap();
    await expect(element(by.id('SearchDialog'))).toBeNotVisible();
  });

  // afterAll(async () => {
  //   await device.terminateApp();
  // });
});
