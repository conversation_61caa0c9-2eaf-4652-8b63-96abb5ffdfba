// Simple React Native specific changes
export const BASE_AI_URL = 'services/ai/';
import Config from 'react-native-config';

interface AppConfig {
  uaaBaseUrl: string;
  apiUrl: string;
  shareBaseUrl: string;
  nomunatumUrl: string;
  appUrlScheme: string;
  appName: string;
  appBrandName: string;
  APP_VERSION: string;
  notificationDefaultChannelId: string;
  notificationDefaultChannelName: string;
  conditionPolicyUrl: string;
  appleSignInRedirectUri: string;
  appleSignInCliendId: string;
  baseAssetUrl: string;
  auth0Domain: string;
  auth0ClientId: string;
  stripePubKey: string;
  googlePayTestEnv: boolean;
  algoliaAppId: string;
  algoliaSearchKey: string;
  algoliaSearchIndex: string;
}

export const appConfig: AppConfig = {
  appBrandName: 'Selfinvite',
  uaaBaseUrl: 'services/uaa/',
  apiUrl: Config.CLOUD_API ?? 'https://selfinvite.eu/',
  shareBaseUrl: Config.URL_EVENT_SHARE ?? 'https://selfinvite.eu/event?q=',
  nomunatumUrl:
    'https://nominatim.openstreetmap.org/?format=json&addressdetails=1&q=',
  appUrlScheme: 'selfinvite',
  appName: 'selfinvite',
  APP_VERSION: Config.VERSION_NAME ?? '3.0.0',
  notificationDefaultChannelId: 'selfinvite-default',
  notificationDefaultChannelName: 'selfinvite default',
  conditionPolicyUrl: 'https://selfinvite.eu/terms-conditions/',
  appleSignInRedirectUri: 'https://selfinvite.eu/uaa/api/return/apple',
  appleSignInCliendId: 'eu.selfinvite',
  baseAssetUrl: Config.BASE_ASSET_URL ?? '',
  auth0Domain: Config.REACT_APP_AUTH0_DOMAIN ?? 'dev-7f99-jj4.eu.auth0.com',
  auth0ClientId:
    Config.REACT_APP_AUTH0_CLIENT_ID ?? 'nB922uauIvYGhcG2b5I0WUDei7FpBKHh',
  stripePubKey: Config.STRIPE_PUB_KEY ?? 'pk_test_pA0GVzkinxHbxZtSr7LgwxcC',
  googlePayTestEnv: Config.GOOGLE_PAY_TEST_ENV === 'true' ?? true,
  algoliaAppId: Config.ALGOLIA_APP_ID ?? 'W4HOC52B2O',
  algoliaSearchKey: Config.ALGOLIA_SEARCH_KEY ?? 'NONE',
  algoliaSearchIndex: Config.ALGOLIA_SEARCH_INDEX ?? 'event_offer_dev',
};
