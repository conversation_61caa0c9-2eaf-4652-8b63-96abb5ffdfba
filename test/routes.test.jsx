import * as React from 'react';
import { screen, render, fireEvent } from '@testing-library/react-native';
import { NavigationContainer } from '@react-navigation/native';
import routes from '@routes/routes';

test('shows profile screen when View Profile is pressed', () => {
  render(
    <NavigationContainer>
      <routes.Routes />
    </NavigationContainer>,
  );

  fireEvent.press(screen.getByText('View Profile'));

  expect(screen.getByText('My Profile')).toBeOnTheScreen();
});
