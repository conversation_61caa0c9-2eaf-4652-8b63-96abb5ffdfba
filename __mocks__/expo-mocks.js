// In your __mocks__/expo-localization.js file or in a setup file for Jest
jest.mock('expo-localization', () => {
  return {
    locale: 'en', // Provide a default locale
    locales: ['en'], // And an array of locales if your application uses it
    // Mock other properties and methods as needed
  };
});
jest.mock('expo-file-system', () => {
  return {};
});
// In your __mocks__/expo-localization.js file or in a setup file for Jest
jest.mock('expo-location', () => {
  return {};
});
// In your __mocks__/expo-localization.js file or in a setup file for Jest
jest.mock('expo-constants', () => {
  return {};
});

