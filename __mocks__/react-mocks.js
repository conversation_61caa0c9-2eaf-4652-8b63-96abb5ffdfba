jest.mock('react-native-calendar-picker', () => {
  return {};
});
jest.mock('react-native-country-picker-modal', () => {
  return {};
});
jest.mock('react-native-image-picker', () => {
  return {};
});
jest.mock('react-native-picker-select', () => {
  return {};
});
jest.mock('react-native-sectioned-multi-select', () => {
  return {};
});
jest.mock('react-native-raw-bottom-sheet', () => {
  return {};
});
jest.mock('react-native-ui-stepper', () => {
  return {};
});
jest.mock('react-native-star-rating', () => {
  return {};
});
jest.mock('react-native-modal-datetime-picker', () => {
  return {};
});
jest.mock('@ptomasroos/react-native-multi-slider', () => {
  return {
    DefaultMaker: jest.fn(() => null),
  };
});
jest.mock('react-native-iphone-x-helper', () => {
  return {
    // Mock the functions or properties you use from this module
    getStatusBarHeight: jest.fn().mockReturnValue(0),
    // Add other mocks as needed
    getBottomSpace: jest.fn().mockReturnValue(0),
  };
});
