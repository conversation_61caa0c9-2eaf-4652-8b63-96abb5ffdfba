# stages: #used to define stages that can be used by jobs and is defined globally
#   - deployiOS
#   - deployAndroid
# variables: #Set up environment values
#   LC_ALL: 'en_US.UTF-8'
#   LANG: 'en_US.UTF-8'
# before_script: #Override a set of commands that are executed before job.
#   - bundle config set path 'vendor/bundle'
#   - bundle install #install bundle in gemfile
#   - whoami
#   - pwd
#   - echo ${PATH}
#   - which npm
#   - ls -lh $(which npm)
#   - ln -sf $(which npm) /usr/local/bin
#   - ls -lh $(which yarn)
#   - ln -sf $(which yarn) /usr/local/bin
#   - yarn install #install node_modules for RN project
# deployiOS:
#   dependencies: []
#   stage: deployiOS
#   script: #scrip when work with current state
#     - cd ios/
#     - pod install
#     - cd ..
#     - fastlane ios release --env prod --verbose
#   tags: #tag registered with gitlab-runner
#     - ios
#   only: #defines the names of branches and tags for which the job will run
#     - develop
# deployAndroid:
#   dependencies: []
#   stage: deployAndroid
#   script: #scrip when work with current state
#     - fastlane android release --env prod --verbose
#   tags: #tag registered with gitlab-runner
#     - android
#   only: #defines the names of branches and tags for which the job will run
#     - develop


stages:
  - build-ios
  - build-android

variables:
  SECURE_FILES_DOWNLOAD_PATH: '/builds/${GITLAB_USER_LOGIN}/selfinvite/'
  LC_ALL: 'en_US.UTF-8'
  LANG: 'en_US.UTF-8'

default:
  interruptible: true
  before_script:
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | base64 --decode | tr -d '\r' > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - ssh-keyscan -H gitlab.com >> ~/.ssh/known_hosts
    - curl --silent "https://gitlab.com/gitlab-org/incubation-engineering/mobile-devops/load-secure-files/-/raw/main/installer" | bash
    - ls -lah $SECURE_FILES_DOWNLOAD_PATH
    - cp Selfinvite.bak.jks android/app/Selfinvite.bak.jks
    - export FASTLANE_PASSWORD="$(cat apple_password)"
    - git config --global user.email $GITLAB_USER_EMAIL
    - git config --global user.name $GITLAB_USER_NAME
    - yarn install --frozen-lockfile
    - gem install bundler
    - bundle config set path 'vendor/bundle'
    - bundle install
    - gem install fastlane -NV
    - bundle exec fastlane match --readonly

cache:
  key:
    files:
      - yarn.lock
      - Gemfile.lock
  paths:
    - node_modules
    - vendor/bundle

build-ios:
  stage: build-ios
  image: reactnativecommunity/react-native-android
  only:
    - master
  script:
    - export # show all the env vars
    - npx pod-install --repo-update
    - fastlane ios release --env prod --verbose
  artifacts:
    paths:
      - ios/build/

build-android:
  stage: build-android
  image: reactnativecommunity/react-native-android
  only:
    - master
  script:
    - export # show all the env vars
    - fastlane android release --env prod --verbose
  artifacts:
    paths:
      - ./android/app/build/outputs/


