{"__euro_sign": "€", "__home": "Home", "__filters": "Filters", "__search": "Search", "__search_desc": "Search For Events", "__search_sub_desc": "Precise your search with the filter options", "__search_placeholder": "event, location, #tag", "__menu": "<PERSON><PERSON>", "__events": "Events", "__total": "Total", "__price": "Price", "__location": "Location", "__authentication": "Authentication", "__login": "Conecte-se", "__login_fb": "Entrar com o Facebook", "__timeout_exception": "Timeout Facebook connection", "__facebook_validation_error": "Facebook does not validate your request", "__register": "Register", "__logout": "Logout", "__version": "Version", "__event": "Event", "__participate": "Participate", "__select": "Select", "__user": "User", "__continue": "Continue", "__participant": "Participant", "__show_presentation": "Showcase", "__presentation": "Presentation", "__street": "Street", "__park": "Park", "__square": "Square", "__club": "Club", "__beach": "Beach", "__camping": "Camping", "__sport_facilities": "Sports facility", "__infused": "Infused - tea / coffee", "__veg_milk": "Vegetable milk", "__milk": "Milk", "__neighborhood": "Neighborhood", "__office": "Office", "__rooftop": "Rooftop", "__state": "State", "__country": "Country", "__postal_code": "Postal Code", "__code": "Code", "__pic": "Picture", "__pricepp": "Price per person", "__person": "Person", "__incoming_requests": "Incoming requests", "__outgoing_requests": "Outgoing requests", "__received_requests": "Received requests", "__event_offers": "Event offers", "__event_offers_sub": "Offers made by users on your events", "__your_requests": "Your requests", "__your_requests_sub": "Event you have requested for", "__my_events": "My Events", "__user_events": "'s Events", "__my_events_sub": "List of events you have created", "__user_past_created": "List of past events", "__my_messages": "Messages", "__feedback_guest": "<PERSON><PERSON><PERSON>\n(as Guest)", "__feedback_host": "Reviews\n(as Host)", "__feedbacks": "Feedbacks", "__feedbacks_sub_desc": "Feedback you received as guest", "__no_reviews_yet": "No reviews yet", "__save": "Save", "__reviews": "Reviews", "__review": "Review", "__see_reviews": "See <PERSON>'s reviews", "__no_reviews": "No reviews for this user", "__no_feedbacks": "No feedbacks for this user", "__no_feedbacks_yet": "No feedback yet", "__no_tags_yet": "No events with the tag", "__no_event_data": "No event data available", "__your_comment": "Your Comment", "__your_comment_placeholder_host": "Tell the host what you think about this event", "__your_comment_placeholder_guest": "Tell the guest what you think about this event", "__reviews_sub": "Reviews you received as host", "__sending": "Sending...", "__send": "Send", "__ok": "Ok", "__typeEvent": "Event type", "__event_public": "Public", "__event_private": "Private", "__event_gte18_explanation": "The registration, due to certain type of events and consumptions is only possible to adult people", "__event_private_explanation": "Private events:\n\nPrivate events are secret, they do not appears in the search results, participation is only true sharing link", "__public_private": "Private or Public", "__calendar_event_added_desc": "Calendar event added successfully", "__calendar_event_added_error_desc": "Error adding event to calendar", "__email_or_username": "Email or Username", "__password": "Password", "__or": "Or", "__active": "Active", "__gte_18_years": "You need to be at least 18 years old", "__insert_age": "Please insert your age!", "__first_name": "First Name", "__name": "Name", "__nickname": "Nickname", "__theme": "Theme", "__last_name": "Last Name", "__language": "Language", "__username": "Username", "__email": "Email", "__password_confirm": "Confirm Password", "__dob": "Date of birth", "__warning": "Warning", "__success": "Success", "__error": "Error", "__error_dateinvalid": "Date inserted is too early", "__hidden_user": "hidden user", "__check_connection": "Check your connection", "__check_email": "Please check your email to confirm it. Check the spam folder too.", "__user_not_logged_in": "You are not logged in.\nPlease logged in or register an account!", "__server_error_registration": "Server Error while registration, please try again", "__server_error_del": "Server error while deleting this event. Please try again", "__server_error_get_events": "Server error while getting the events. Please try again", "__server_error_get_reviews": "Server error while getting the reviews. Please try again", "__server_error_create_review": "Server error while creating the review. Please try again", "__server_error_create_feedback": "Server error while creating the feedback. Please try again", "__server_error_get_feedbacks": "Server error while getting the feedbacks. Please try again", "__server_error_save_user_data": "Server error while saving user data. Please try again", "__server_error_save_image": "Server error while uploading the image. Please try again", "__server_error_get_data": "Server error while retrieving data. Please try again", "__wrongstate": "Error wrong object state!", "__create_event": "Create event", "__clone_event": "Clone event", "__edit_event": "Edit event", "__wrong_data": "Wrong data", "__analizing": "Analizing...", "__profile": "Profile", "__reset_password": "Reset Password", "__sending_link": "Sending Link...", "__edit_avatar": "Edit avatar", "__avatar": "Avatar", "__delete_profile": "Delete profile", "__special_guest": "Special guest", "__edit_profile": "Edit profile", "__bank_onboard": "Bank", "__kyc_onboard": "KYC", "__document_unverified_other": "Document invalid", "__kyc_waiting_response": "KYC: waiting for response", "__kyc_to_be_verified": "KYC: to be verified", "__kyc_not_compliant": "Kyc waiting for response or not compliant", "__kyc_verified": "KYC: verified", "__kyc_rejected": "KYC: rejected", "__phone_number": "Phone number", "__otp": "OTP", "__duplicateentry": "You have already requested participation", "__description": "Description", "__badges": "Badges", "__food_intol": "Food Intolerances", "__allergens": "Allergens", "__no_allergens": "No allergens declared", "__no_food_intol": "No intolerances declared", "__dates": "Dates", "__guests": "Guests", "__city": "City", "__kitchen": "Kitchen", "__drink": "Drink", "__date": "Date", "__duration_short": "Duration", "__duration_unit": "Minutes", "__duration": "Duration (Minutes)", "__sure_?": "Are you sure?", "__confirm_del": "You want to delete your event", "__confirm_ed": "You want to update this event?", "__confirm_clone": "The event is close, do you want to clone it?", "__show": "Show", "__next": "Next", "__user_not_position": "User location not detected", "__user_location_permission_denied": "You haven't granted permission to detect your location.", "__participation_fee": "*in accordance with the terms of use, Selfinvite provides the possibility of inviting at most another person other than the User", "__participation_fee_desc": "Selfinvite fee", "__previous": "Previous", "__confirm_1": "Do you really want to ", "__pay_confirm": "Checkout", "__1_offer": " this offer?", "__offer": "Offer", "__close": "Close", "__closed": "Closed", "__wrongeokv": "Error while processing this request: wrong request number", "__error_server": "Error while processing this request", "__verify_host_needs": "You must verify to receive money for your event:", "__verify_host_no_iban": "You must add your bank account to receive money for your event!", "__verify_user_needs": "You must verify your phone number to pay for event!", "__cancel_success_event_offer": "Event offer cancelled successfully", "__accept_success_event_offer": "Event offer accepted successfully", "__delete_success_event_offer": "Event offer removed successfully", "__cancel_error_event_offer": "Error while cancelling this event offer", "__accept_error_event_offer": "Error while accepting this event offer", "__feedback_created_ok": "Feed<PERSON> successfully created!", "__review_created_ok": "Review successfully created!", "__cancel_success_event_request": "Event request cancelled successfully", "__accept_success_event_request": "Event request accepted successfully", "__delete_delete_event_offer": "Do you want to remove this event request?", "__accept_confirm_event_offer": "Do you want to confirm this event request?", "__cancel_confirm_event_offer": "Do you want to cancel this event request?", "__refund_confirm_event_offer": "Do you want to refund the guest for the event attendance?", "__cancel_error_event_request": "Error while cancelling this event request", "__accept_error_event_request": "Error while accepting this event request", "__delete_success_event_request": "Event request deleted successfully", "__delete_error_event_request": "Error while deleting this event request", "__payment_success_event_request": "Payment Successful", "__payment_error_event_request": "Error while processing the payment", "__waitingForPayment": "Waiting payment", "__payment": "Payment", "__expired": "Expired", "__back": "Back", "__sort": "Sort", "__sort_asc": "Asc", "__sort_desc": "Desc", "__offers_pending": "Offers Pending", "__yes": "Yes", "__no": "No", "__upload": "Upload", "__cancel": "Cancel", "__alert": "<PERSON><PERSON>", "__error_dup_partic": "You can't send the request twice.\nYou can see your request in the Your requests menu.", "__error_partic": "Error sending your request", "__error_partic_no_place": "No more place available", "__req_partic": "Will you join with a Special Guest* ?", "__req_partic_ok": "Congratulations!\nYour request has been sent, wait for the host to confirm your request and pay it.\nYou can see your request in the Your requests menu.", "__req_partic_nok": "Your request has not been accepted", "__event_save_ok": "Event successfully saved", "__login_needed": "Você deve estar logado para ver o.", "__male": "Male", "__female": "Female", "__gender": "Gender", "__saving": "Saving...", "__mandatory_values": "Every field with (*) are required", "__your_address": "Your address", "__your_address_tip": "Type in your address to search in", "__event_alert_1": "Event date should be after today", "__max_partic": "Max Participants", "__number_people": "People", "__verify": "Verify", "__verifying": "Verifying...", "__pick_address": "Pick address", "__use_local_address": "Use current address", "__phone_verified": "Your mobile number has been verified!", "__iban_inserted": "Your Bank account has been inserted!", "__phone_save_error": "Server error while saving the phone number. Please try again", "__phone_verified_error": "Server error while checking the otp.\nPlease verify your otp code and try again", "__valid_email": "Please enter a valid email", "__enter_email": "Please enter your email", "__error_email": "No such email exists in our system", "__email_instruction": "Enter you email address in the field below to reset your password", "__invalid_cred": "Invalid Credentials", "__email_exist": "Email exists, try to reset the password using your email", "__connecting": "Connecting...", "__accepted": "Accepted", "__cancelled": "Cancelled", "__guest": "Guest", "__host": "Host", "__use_condition": "Conditions", "__link": "Link", "__paid": "Paid", "__camera_roll_permissions": "Sorry, we need camera roll permissions to make this work!", "__select_date": "Select a date", "__take_image": "Take a pic", "__camera_unavailable": "Camera not available", "__camera_permission": "App Camera Permission", "__permission": "Camera permission Denied", "__pick_image": "Pick an image", "__change_image": "Change image", "__pic_not_blank": "You need an image", "__accept": "Accept", "__pay": "Pay", "__feedback": "<PERSON><PERSON><PERSON>", "__delete": "Delete", "__refund": "Refund", "__refund_denied": "Refund denied", "__unknown": "Unknown", "__refund_accepted": "Refund accepted", "__refund_done": "Refunded", "__refund_check": "Refund verify", "__refund_requested": "Refund requested", "__finished": "Finished", "__all": "All", "__completed": "Completed", "__review_list": "Reviews list", "__feedback_list": "Feedbacks list", "__pending": "Pending", "__delete_conf": "Do you really want to delete this request?", "__cancel_conf": "Do you really want to cancel this request?", "__dispute_conf": "Do you really want to open a refund for this request?\nThe payment to the host will be paused and the host will review your request", "__apply": "Apply", "__requested_for": "requested for", "__seats_in": "seats in", "__server_error_bad_id": "Server Error Id not found", "__events_requests": "Events requests", "__past_events": "Past events", "__requests": "Requests", "__no_events": "No events", "__no_requests": "No requests", "__no_messages": "No Messages", "__host_cancelled": "Host cancelled", "__type_message": "Type a message...", "__message_disabled": "Message disabled until payment!", "__info": "Info", "__user_info": "User info", "__user_info_sub": "user details", "__server_error_get_user_info": "Server error to get user info. Please try again", "__user_since": "User since", "__error_length_gt_2_lt_50": "The length must be within 2 and 50 chars", "__error_length_gt_4_lt_50": "The length must be within 4 and 50 chars", "__error_length_gt_2_lt_200": "The length must be within 2 and 200 chars", "__error_length_lt_50": "The length must be smaller than 50 chars", "__userexists": "Login exists already", "__is_missing": "Missing value", "__address": "Address", "__fb_err_1": "<PERSON><PERSON> failed with error: ", "__fb_err_2": "Login was cancelled", "__fb_err_3": "<PERSON><PERSON> was successful with permissions: ", "__fb_logout": "User logged out", "__fb_err_4": "Cancel: Unmanaged error", "__fb_err_5": "Unmanaged error", "__fb_err_6": "<PERSON><PERSON> not shared", "__fb_err_7": "Name not shared", "__fb_warn_1": "Email exists but it is not linked to Facebook\nWanna merge it?", "__error_validation": "Validation error", "__give_feedback": "<PERSON>", "__rating": "Rating", "__event_requests": "Events Requests", "__feedback_expired": "This feedback has been expired", "__rate_event": "Rate the Event", "__rate_guest": "Rate the Guest", "__rate_host": "Rate the Host", "__create_message": "New Message", "__create": "Send", "__selfinvite": "<PERSON><PERSON><PERSON><PERSON>", "__participants": "Participants", "__confirmed": "confirmed", "__requested": "requested", "__available": "available", "__when": "When", "__place_available": "available seats", "__share": "Share", "__date_time": "Date/Time", "__server_error_generic": "Server error! Please try again", "__successfull_request": "Request correctly executed", "__share_intro": "Ehi! See my event:", "__share_intro_participation": "<PERSON><PERSON>! I'll join this event:", "__message_title": "Title", "__message_receiver": "Receiver", "__message_body": "Your Message", "__message_required": "Please enter ", "__message_user_not_found": "User not found with this username", "__message_success": "Message sent successfully", "__message_error": "Error while sending the message", "__confirm": "Confirm", "__dairy": "Dairy", "__sulphites": "Sulphites", "__lupins": "<PERSON><PERSON><PERSON>", "__lupin": "<PERSON><PERSON><PERSON>", "__clams": "Clams", "__celery": "Celery", "__mustard": "Mustard", "__sesame": "Sesame", "__eggs": "Eggs", "__peanuts": "Peanuts", "__soy": "Soy", "__gluten": "Gluten", "__nuts": "Nuts", "__legumes": "Legumes", "__shellfish": "Shellfish", "__dinner": "Dinner", "__lunch": "Lunch", "__brunch": "Brunch", "__breakfast": "Breakfast", "__picnic": "Picnic", "__yoga": "Yoga lovers", "__bbq": "BBQ", "__aperitif": "Aperitif", "__alcoholic": "Alcoholic", "__analcoholic": "Analcoholic", "__wine": "Wine", "__cocktail": "Cocktail", "__liquor": "Liquor", "__beer": "Beer", "__terrace": "Terrace", "__garden": "Garden", "__canteen": "Canteen", "__other": "Other", "__restaurant": "Restaurant", "__hotel": "Hotel", "__bnb": "BnB", "__ethnic": "Ethnic", "__fusion": "Fusion", "__mediterrean": "Mediterran", "__asian": "Asian", "__chinese": "Chinese", "__japanese": "Japanese", "__turkish": "Turkish", "__turkey": "Turkish", "__greek": "Greek", "__italian": "Italian", "__pastry": "Pastry", "__packed_food": "Packed food", "__food_delivery": "Food delivery", "__pizza": "Pizza", "__baking": "Baking", "__african": "African", "__vegeterian": "<PERSON><PERSON><PERSON><PERSON>", "__vegan": "Vegan", "__street_food": "Street food", "__fish": "Fish", "__dry_meat": "Dry aged meat", "__junk_food": "Junk food", "__thai": "Thai", "__mexican": "Mexican", "__argentina": "Argentinian", "__brazilian": "Brazilian", "__gourmet_deli": "Gourmet", "__lang_it": "it", "__lang_en": "en", "__lang_fr": "fr", "__lang_es": "es", "__lang_de": "de", "__lang_pt": "pt", "__it": "IT (+39)", "__uk": "UK (+44)", "__us": "US (+1)", "__gr": "GR (+30)", "__nl": "NL (+31)", "__be": "BE (+32)", "__fr": "FR (+33)", "__lu": "LUX (+352)", "__ir": "IR (+353)", "__ic": "IC (+354)", "__fi": "FI (+358)", "__ch": "CH (+41)", "__at": "AT (+43)", "__es": "ES (+34)", "__de": "DE (+49)", "__dk": "DK (+45)", "__se": "SE (+46)", "__nor": "NO (+47)", "__pl": "PL (+48)", "__pt": "PT (+351)", "__event_is_used": "Impossible to delete!\nEvent with attendees", "__folk_dance": "Folk dance", "__pool_party": "Pool party", "__dj_set": "Dj set", "__halloween_party": "Halloween party", "__graduation_party": "Graduation party", "__wedding_party": "Wedding party", "__after_party": "After party", "__new_year_eve_party": "New year's eve party", "__birthday_party": "Birthday", "__bachelor_ette_party": "Bachelor / Bachelorette party", "__christmas_party": "Christmas party", "__card": "Card", "__cards": "Cards", "__iban": "<PERSON><PERSON>", "__bank_account": "Bank account", "__id": "Id", "__front_id": "+ Front Id", "__same_pic_inserted": "You have inserted the same pic twice", "__back_id": "+ Back Id", "__card_game_tournament": "Card game tournament", "__game_board_tournament": "Game board tournament", "__tv_sports": "Tv sports", "__sports_events": "Sport events", "__tennis": "Tennis", "__padel": "Pa<PERSON>", "__climbing": "Climbing", "__ski": "Ski", "__beach_party": "Beach party", "__children_party": "Children party", "__running": "Running", "__football": "Fu<PERSON>al", "__skateboarding": "Skateboarding", "__surfboarding": "Surf", "__fishing": "Fishing", "__tv_series": "Tv Series", "__naturism_party": "Naturism party", "__home_growers": "Home growers", "__movie_lovers": "Movie lovers", "__live_tv": "Live tv", "__rpg_cosplaying_party": "RPG Cosplaying party", "__playground_3vs3": "Playground 3 vs 3", "__tasting": "Tasting", "__bike_lovers": "Bike lovers", "__karaoke": "Karaoke", "__music_events": "Music events", "__ballroom_dance": "Ballroom dance", "__hiking": "Hiking", "__book_club": "Book club", "__live_music": "Live music", "__bbq_lovers": "BBQ lovers", "__workout_fitness_course": "Workout fitness lovers", "__homebrewers": "Homebrewers", "__smokers": "Smokers", "__special_events": "Special events", "__feedbackexist": "Feedback exists already", "__reviewexist": "Review exists already", "__open": "Open", "__closing": "Closing", "__policies": "Policies", "__user_policy": "User policy", "__general_policy": "General policy", "__terms_policy": "Terms", "__privacy_policy": "Privacy", "__payment_policy": "Payments", "__cookie_policy": "Cookie policy", "__selfinviter": "Selfinviter", "__strunz": "<PERSON><PERSON><PERSON>", "__best_chef": "Best chef", "__delete_message": "Delete Message", "__delete_user_irreversibile": "Delete the user is Irreversibile, all your data will be removed!", "__delete_user_irreversibile_final": "Are you sure to Delete your account?\nAll your data will be removed!\nWe're sorry to see you leave!", "__fresh_juice": "Fresh juice", "__sandwiches": "Sandwiches", "__salad": "Salad", "__chicken": "Chicken", "__pork": "Pork", "__poultry_meat": "Poultry meat", "__cake": "Cake", "__icecream": "Ice cream", "__cheese": "Cheese", "__meat": "Meat", "__sweets_candies": "Sweets / Candies", "__bacon": "<PERSON>", "__chips_popcorn": "Chips / pop corn", "__singles_party": "Singles party", "__swingers_party": "Swingers party", "__speed_date": "Speed date", "__lgbtq_party": "LGBTQ party", "__speak_easy": "Speak easy", "__dark_kitchen": "Dark kitchen", "__water": "Water", "__details": "Details", "__smoothie": "<PERSON><PERSON><PERSON><PERSON>", "__self_drink": "Self-produced drink", "__season_fruit": "Seasonable fruit", "__season_veg": "Seasonable vegetables", "__breads": "Bread / Crackers / Breadsticks", "__jam": "Fruit jam / Marmelade", "__butter": "Butter / Peanut butter", "__evo": "Olive oil", "__oil": "Seed oil", "__burgers": "Burgers", "__pasta_risotto": "Pasta / Risotto", "__rice": "Rice", "__lamb": "<PERSON> / Sheep", "__duck": "<PERSON> / <PERSON>", "__rabbit": "Rabbit", "__cereals": "Cereals", "__pate": "Paté / Meat or Fish Pie", "__seafood": "Seafood", "__salami": "Ham / cured Ham / Salami / Pastrami / Sausages", "__mushrooms": "Mushrooms / Truffles", "__raw": "Raw", "__bio": "Organic (Bio)", "__frozen": "Frozen food", "__canned": "Canned goods", "__homemade": "Self-produced / Homemade", "__bonfire": "Bonfire", "__guest_bath": "Guest bathroom", "__pet_friendly": "Pet friendly", "__private_room": "Private bedrooms", "__wifi": "Wifi", "__swim_pool": "Swimming pool", "__playground": "Playground", "__airco": "Air condition", "__wood_oven": "Wood oven", "__indoor": "Indoor", "__outdoor": "Outdoor", "__sending_30s": "Sending... (wait 30s)", "__nationality": "Nationality", "__residency": "Residency", "__prefix": "Prefix", "__yacht": "Yacht", "__sail_boat": "Sail boat", "__wheelchair_access": "Wheelchair accessible", "__km0": "Local", "__no_policy_selected": "No policy selected", "__all_policy_selected": "All policy selected", "__not_all_policy_selected": "Not all policy selected", "__risky_delete": "Risky part", "__invalid_iban": "The provided IBAN is not correct", "__not_deleteable": "The event can not be delete within 24 hours of the due date, try again later", "__gallery": "Gallery", "__document_unreadable": "Documento non leggibile", "__document_not_accepted": "Documento non accettato", "__document_has_expired": "Documento scaduto", "__document_incomplete": "Documento incompleto", "__document_does_not_match_user_data": "Documento: i dati non corrispondono", "__kyc_rejected_explanation": "Kyc document not accepted, you have to upload it correctly", "__slide_1_title": "Welcome Selfinviter!", "__slide_1_text": "Selfinvite is the Social Eating Application that allows you to meet people all over Europe to Eat and have Fun Together thanks to a Network based on Collaborative Consumption.", "__slide_2_title": "Be a GUEST", "__slide_2_text": "Choose what to eat and with whom, look for the event where food, drink, location, and situation are to your liking, people with your same passions ready to welcome you, eat, and have fun together.", "__slide_3_title": "Or be a HOST", "__slide_3_text": "Create Your Event, express yourself, arranges food, drinks and price per person, include your new self-invited friends in activities that define you", "__slide_4_title": "Let's Meet Up!", "__slide_4_text": "#BeSocialforReal", "__hour_sign": "h", "__nsfw": "Content explicit", "__general": "General", "__block_user": "Block user", "__enable_blocked_users": "Enable users", "__blocked_users": "Blocked users", "__no_blocked_users": "No blocked users", "__no_actions": "No actions", "__report_event": "Report event", "__add_calendar_event": "Add event to calendar", "__report_event_spam": "It's suspicious or spam", "__report_event_abusive": "It's abusive or harmful", "__report_error_idexists": "Report already inserted", "__actions": "Actions", "__error_pic_path": "Error pics path, try a different photo path", "__state_invalid": "Error: Invalid state"}