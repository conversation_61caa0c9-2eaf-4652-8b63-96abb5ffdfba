import { I18n } from 'i18n-js';
import * as Localization from 'expo-localization';

import fr from '@translations/global_fr.json';
import it from '@translations/global_it.json';
import de from '@translations/global_de.json';
import en from '@translations/global_en.json';
import es from '@translations/global_es.json';
import pt from '@translations/global_pt.json';

/**
 * localization
 */
// Set the key-value pairs for the different languages you want to support.
// I18n.translations = globalTranslations;
// I18n.translations['en'] = en;
// I18n.translations['de'] = de;
// I18n.translations['fr'] = fr;
// I18n.translations['es'] = es;
// I18n.translations['it'] = it;
// I18n.translations['pt'] = pt;

// I18n.locale = Localization.locale;
// // When a value is missing from a language it'll fallback to another language with the key present.
// I18n.fallbacks = true;

const i18n = new I18n(
  { en: en, de: de, fr: fr, es: es, it: it, pt: pt },
  { enableFallback: true, locale: Localization.locale },
);

export default i18n;
