# to view expo cred manager
expo credentials:manager

# to list the commands
gradle tasks

# to clean the cache
gradle clean
gradle cleanBuildCache

# to build dep
gradle buildDependents


https://stackoverflow.com/questions/35935060/how-can-i-generate-an-apk-that-can-run-without-server-with-react-native



react native cookie build.gradle
android {
    compileSdkVersion 28
    buildToolsVersion "28.0.3"

    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 28
        versionCode 1
        versionName "1.0"
    }
    lintOptions {
        abortOnError false
    }
}
