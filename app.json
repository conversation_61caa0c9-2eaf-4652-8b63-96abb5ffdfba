{"name": "selfinvite", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "expo": {"sdkVersion": "50.0.0", "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON>", "privacy": "unlisted", "platforms": ["ios", "android"], "scheme": "selfinvite", "version": "3.0.0", "orientation": "portrait", "icon": "./assets/ic_launcher.png", "splash": {"image": "./assets/splash/splash.png", "resizeMode": "contain", "backgroundColor": "#F24958"}, "updates": {"fallbackToCacheTimeout": 0}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.j.autoinvito", "buildNumber": "300", "googleServicesFile": "./ios/GoogleService-Info.plist"}, "android": {"package": "com.j.autoinvito", "versionCode": 300, "googleServicesFile": "./android/app/google-services.json"}, "description": "Selfinvite app", "plugins": ["expo-localization", "@react-native-firebase/app", "@react-native-firebase/crashlytics", ["@stripe/stripe-react-native", {"merchantIdentifier": "merchant.eu.selfinvite", "enableGooglePay": true}], "expo-secure-store"]}, "facebookScheme": "fb345992691296635", "facebookAppId": "345992691296635", "facebookDisplayName": "selfinvite"}