#!/bin/bash

SRC=./regenerated_app
DST_IOS=./selfinvite/ios
DST_ANDROID=./selfinvite/android

FILE=ic_launcher.png
FILE_LAUNCH=shell_launch_background_image.png

echo copying from $SRC to $DST_ANDROID files $FILE $FILE_LAUNCH

##android
cp $SRC/android/app/src/main/res/drawable-xxxhdpi/$FILE_LAUNCH $DST_ANDROID/app/src/main/res/drawable-xxxhdpi/

cp $SRC/android/app/src/main/res/mipmap-hdpi/$FILE $DST_ANDROID/app/src/main/res/mipmap-hdpi/
cp $SRC/android/app/src/main/res/mipmap-mdpi/$FILE $DST_ANDROID/app/src/main/res/mipmap-mdpi/
cp $SRC/android/app/src/main/res/mipmap-xhdpi/$FILE $DST_ANDROID/app/src/main/res/mipmap-xhdpi/
cp $SRC/android/app/src/main/res/mipmap-xxhdpi/$FILE $DST_ANDROID/app/src/main/res/mipmap-xxhdpi/
cp $SRC/android/app/src/main/res/mipmap-xxxhdpi/$FILE $DST_ANDROID/app/src/main/res/mipmap-xxxhdpi/

echo copying from $SRC to $DST_IOS files
##ios
cp $SRC/ios/selfinvite/Assets.xcassets/AppIcon.appiconset/*.png $DST_IOS/selfinvite/Assets.xcassets/

cp $SRC/ios/selfinvite/Supporting/*.png $DST_IOS/selfinvite/Supporting/




####
detox recorder iphone id id=2F4A9AA5-45C8-4DCC-B860-0CCE1E53C980