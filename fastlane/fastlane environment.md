<details><summary>✅ fastlane environment ✅</summary>

### Stack

| Key                         | Value                                                   |
| --------------------------- | ------------------------------------------------------- |
| OS                          | 10.15.7                                                 |
| Ruby                        | 2.7.2                                                   |
| Bundler?                    | false                                                   |
| Git                         | git version 2.24.1                                      |
| Installation Source         | /usr/local/Cellar/fastlane/2.170.0/libexec/bin/fastlane |
| Host                        | Mac OS X 10.15.7 (19H114)                               |
| Ruby Lib Dir                | /usr/local/Cellar/ruby/2.7.2/lib                        |
| OpenSSL Version             | OpenSSL 1.1.1g  21 Apr 2020                             |
| Is contained                | false                                                   |
| Is homebrew                 | true                                                    |
| Is installed via Fabric.app | false                                                   |
| Xcode Path                  | /Applications/Xcode.app/Contents/Developer/             |
| Xcode Version               | 12.3                                                    |


### System Locale

| Variable | Value       |   |
| -------- | ----------- | - |
| LANG     | en_US.UTF-8 | ✅ |
| LC_ALL   | en_US.UTF-8 | ✅ |
| LANGUAGE |             |   |


### fastlane files:

<details><summary>`./fastlane/Fastfile`</summary>

```ruby

# before_all do
#     ensure_git_branch
#     ensure_git_status_clean
#     git_pull
# end


platform :ios do
    private_lane :update_version do
        app_store_version = get_app_store_version_number(bundle_id: 'selfinvite')
        plist_version = get_version_number_from_plist(xcodeproj: './ios/selfinvite.xcodeproj')
        if Gem::Version.new(plist_version.to_f) == Gem::Version.new(app_store_version.to_f)
            UI.message "bumping minor"
            increment_version_number_in_plist(xcodeproj: './ios/selfinvite.xcodeproj', bump_type: 'minor')
        else
            UI.message "bumping patch"
            increment_version_number_in_plist(xcodeproj: './ios/selfinvite.xcodeproj', bump_type: 'patch')
        end
    end

    private_lane :staging_build do
        increment_build_number_in_plist(xcodeproj: './ios/selfinvite.xcodeproj', target: 'selfinvite')
        gym(scheme: 'selfinvite-staging', workspace: './ios/selfinvite.xcworkspace')
    end

    private_lane :release_build do
        increment_build_number_in_plist(xcodeproj: './ios/selfinvite.xcodeproj', target: 'selfinvite')
        gym(scheme: 'selfinvite-release', workspace: './ios/selfinvite.xcworkspace')
    end

    private_lane :screenshots do
        Dir.chdir('..') do
            # run in parent directory
            sh('detox', 'build -c ios')
            sh('detox', 'test -c ios')
        end
    end

    lane :beta do
        staging_build
        upload_to_testflight(username: '<EMAIL>', app_identifier: 'selfinvite')
        commit_version_bump(message: 'bump build')
        push_to_git_remote
    end

    lane :release do
        release_build
        screenshots
        deliver
        commit_version_bump(message: 'bump build')
        push_to_git_remote
    end
end

platform :android do
    desc "Android build and release to beta"
    lane :beta do
        increment_version_code(app_project_dir: './android/app')
        gradle(task: 'clean', project_dir: './android/')
        gradle(task: 'bundle', build_type: 'Release', project_dir: './android')
        supply(track: 'beta', aab: './android/app/build/outputs/bundle/release/app-release.aab')
    end 
    lane :release do
        increment_version_code(app_project_dir: './android/app')
        increment_version_name(app_project_dir: './android/app', bump_type: 'patch')
        gradle(task: 'clean', project_dir: './android/')
        gradle(task: 'bundle', build_type: 'Release', project_dir: './android')
        supply(track: 'production', aab: './android/app/build/outputs/bundle/release/app-release.aab', release_status: 'draft')
    end
end

lane :codepush_ios do |options|
    current_version = get_version_number(xcodeproj: './ios/selfinvite.xcodeproj', target: 'selfinvite')
    codepush(current_version: current_version, manditory: options[:manditory])
end

lane :codepush_android do |options|
    current_version = google_play_track_release_names
    codepush(current_version: current_version, manditory: options[:manditory])
end

private_lane :select_app_version do |options|
    current_version = options[:current_version]
    current_major = [current_version.split(".").first, 'x', 'x'].join('.')
    current_minor = current_version.split('.').slice(0,2).push('x').join('.')
    target_version_label = UI.select("What version do you want to target?", [
        "All users",
        "Most recent major (#{current_major})",
        "Most recent minor (#{current_mior})",
        "Current version (#{current_version})"
    ])

    next "\"*\"" if target_version_label.match(/All/)
    next current_major if target_version_label.match(/major/)
    next current_minor if target_version_label.match(/minor/)

    current_version
end

lane :codepush do |options|
    manditory = !!options[:manditory]
    manditory_string = manditory ? " -m" : ""
    version = select_app_version(current_version: options[:current_version])
    if UI.confirm("Going to CodePush #{version} to production. Feeling lucky punk?")
        Dir.chdir("..") do
            sh "appcenter codepush release-react -a selfinvite -d Production -t #{version}#{maditory_string} --output-dir ./build" do |status, result, command|
            unless status.success?
                UI.error "Command #{command} failed with status #{status.exitstatus}"
            end
            UI.message "Finished! Check out the release on App center."
        end
        end
    else
        UI.error "Not going to push"
    end
end
```
</details>

<details><summary>`./fastlane/Appfile`</summary>

```ruby
json_key_file("./pc-api-4790442714061326313-135-db072052e498.json") # Path to the json secret file - Follow https://docs.fastlane.tools/actions/supply/#setup to get one
package_name("com.j.autoinvito") # e.g. com.krausefx.app

```
</details>

### fastlane gems

| Gem      | Version | Update-Status |
| -------- | ------- | ------------- |
| fastlane | 2.173.0 | ✅ Up-To-Date  |


### Loaded fastlane plugins:

| Plugin                                 | Version | Update-Status |
| -------------------------------------- | ------- | ------------- |
| fastlane-plugin-increment_version_code | 0.4.3   | ✅ Up-To-Date  |
| fastlane-plugin-get_version_code       | 0.2.0   | ✅ Up-To-Date  |
| fastlane-plugin-versioning             | 0.4.4   | ✅ Up-To-Date  |


<details><summary><b>Loaded gems</b></summary>

| Gem                                    | Version      |
| -------------------------------------- | ------------ |
| did_you_mean                           | 1.4.0        |
| slack-notifier                         | 2.3.2        |
| atomos                                 | 0.1.3        |
| CFPropertyList                         | 3.0.3        |
| claide                                 | 1.0.3        |
| colored2                               | 3.1.2        |
| nanaimo                                | 0.3.0        |
| xcodeproj                              | 1.19.0       |
| rouge                                  | 2.0.7        |
| xcpretty                               | 0.3.0        |
| terminal-notifier                      | 2.0.0        |
| unicode-display_width                  | 1.7.0        |
| terminal-table                         | 1.8.0        |
| public_suffix                          | 4.0.6        |
| addressable                            | 2.7.0        |
| multipart-post                         | 2.0.0        |
| word_wrap                              | 1.0.0        |
| tty-screen                             | 0.8.1        |
| tty-cursor                             | 0.7.1        |
| tty-spinner                            | 0.9.3        |
| artifactory                            | 3.0.15       |
| babosa                                 | 1.0.4        |
| colored                                | 1.2          |
| highline                               | 1.7.10       |
| commander-fastlane                     | 4.4.6        |
| unf_ext                                | *******      |
| unf                                    | 0.1.4        |
| domain_name                            | 0.5.20190701 |
| http-cookie                            | 1.0.3        |
| faraday-cookie_jar                     | 0.0.7        |
| faraday_middleware                     | 1.0.0        |
| gh_inspector                           | 1.1.3        |
| mini_magick                            | 4.11.0       |
| rubyzip                                | 2.3.0        |
| security                               | 0.1.3        |
| dotenv                                 | 2.7.6        |
| bundler                                | 2.1.4        |
| simctl                                 | 1.6.8        |
| jwt                                    | 2.2.2        |
| uber                                   | 0.1.0        |
| declarative                            | 0.0.20       |
| declarative-option                     | 0.1.0        |
| representable                          | 3.0.4        |
| retriable                              | 3.1.2        |
| mini_mime                              | 1.0.2        |
| httpclient                             | 2.8.3        |
| google-api-client                      | 0.38.0       |
| emoji_regex                            | 3.2.1        |
| uri                                    | 0.10.0       |
| plist                                  | 3.6.0        |
| excon                                  | 0.79.0       |
| ruby2_keywords                         | 0.0.4        |
| faraday-net_http                       | 1.0.1        |
| faraday                                | 1.3.0        |
| fastimage                              | 2.2.2        |
| json                                   | 2.5.1        |
| xcpretty-travis-formatter              | 1.0.1        |
| naturally                              | 2.2.1        |
| multi_json                             | 1.15.0       |
| signet                                 | 0.14.1       |
| os                                     | 1.1.1        |
| memoist                                | 0.16.2       |
| googleauth                             | 0.15.1       |
| rake                                   | 13.0.3       |
| digest-crc                             | 0.6.3        |
| webrick                                | 1.7.0        |
| rexml                                  | 3.2.4        |
| google-apis-core                       | 0.2.1        |
| google-apis-storage_v1                 | 0.2.0        |
| google-apis-iamcredentials_v1          | 0.1.0        |
| google-cloud-errors                    | 1.0.1        |
| google-cloud-env                       | 1.4.0        |
| google-cloud-core                      | 1.5.0        |
| google-cloud-storage                   | 1.30.0       |
| aws-eventstream                        | 1.1.0        |
| aws-sigv4                              | 1.2.2        |
| aws-partitions                         | 1.425.0      |
| jmespath                               | 1.4.0        |
| aws-sdk-core                           | 3.112.0      |
| aws-sdk-kms                            | 1.42.0       |
| aws-sdk-s3                             | 1.88.0       |
| forwardable                            | 1.3.1        |
| logger                                 | 1.4.2        |
| cgi                                    | 0.1.0        |
| date                                   | 3.0.0        |
| timeout                                | 0.1.0        |
| stringio                               | 0.1.0        |
| zlib                                   | 1.1.0        |
| ipaddr                                 | 1.2.2        |
| openssl                                | 2.1.2        |
| ostruct                                | 0.2.0        |
| strscan                                | 1.0.3        |
| delegate                               | 0.1.0        |
| fileutils                              | 1.4.1        |
| io-console                             | 0.5.6        |
| open3                                  | 0.1.0        |
| singleton                              | 0.1.0        |
| yaml                                   | 0.1.0        |
| psych                                  | 3.1.0        |
| mutex_m                                | 0.1.0        |
| fastlane-plugin-increment_version_code | 0.4.3        |
| fastlane-plugin-get_version_code       | 0.2.0        |
| fastlane-plugin-versioning             | 0.4.4        |
</details>


*generated on:* **2021-02-09**
</details>