
# before_all do
#     ensure_git_branch
#     ensure_git_status_clean
#     git_pull
# end

fastlane_require 'dotenv'
platform :ios do
    desc "iOS build the application"
    
    private_lane :update_version do
        app_store_version = get_app_store_version_number(bundle_id: 'selfinvite')
        plist_version = get_version_number_from_plist(xcodeproj: './ios/selfinvite.xcodeproj')
        if Gem::Version.new(plist_version.to_f) == Gem::Version.new(app_store_version.to_f)
            UI.message "bumping minor"
            increment_version_number_in_plist(xcodeproj: './ios/selfinvite.xcodeproj', bump_type: 'minor')
        else
            UI.message "bumping patch"
            increment_version_number_in_plist(xcodeproj: './ios/selfinvite.xcodeproj', bump_type: 'patch')
        end
    end

    private_lane :staging_build do
        # increment_build_number_in_plist(xcodeproj: './ios/selfinvite.xcodeproj', target: 'selfinvite')
        gym(scheme: 'selfinvite (dev)', workspace: './ios/selfinvite.xcworkspace')
    end

    private_lane :release_build do
        get_certificates(username: '<EMAIL>')
        get_provisioning_profile(username: '<EMAIL>', app_identifier: 'com.j.autoinvito')
        # increment_build_number_in_plist(xcodeproj: './ios/selfinvite.xcodeproj', target: 'selfinvite')
        gym(scheme: 'selfinvite',
            workspace: './ios/selfinvite.xcworkspace',
#   silent: true,
            suppress_xcode_output: true)
    end

    private_lane :screenshots do
        Dir.chdir('..') do
            # run in parent directory
            sh('detox', 'build -c ios')
            sh('detox', 'test -c ios')
        end
    end

    lane :beta do
        staging_build
        upload_to_testflight(username: '<EMAIL>', app_identifier: 'Selfinvite')
        # 'com.j.autoinvito')
        commit_version_bump(message: 'bump build', xcodeproj: 'ios/selfinvite.xcodeproj')
        # push_to_git_remote
    end

    lane :release do
        release_build
        # screenshots
        deliver(username: '<EMAIL>',
            # submit_for_review: true,
            force: true,
            metadata_path: "./fastlane/metadata")
        commit_version_bump(message: 'bump build', xcodeproj: 'ios/selfinvite.xcodeproj')
        add_git_tag(grouping: "ios-builds",includes_lane: true, prefix: "v", build_number: ENV["VERSION_NAME"] )
        # push_to_git_remote
    end
    # lane :release_deliver do
    #     deliver(username: '<EMAIL>',
    #         force: true,
    #         metadata_path: "./fastlane/metadata")
    # end

    # error block is executed when a error occurs
    error do |lane, exception|
      slack(
        slack_url: "*********************************************************************************",
        # message with short human friendly message
        message: exception.to_s, 
        success: false, 
        # Output containing extended log output
        payload: { "Output" => exception.error_info.to_s } 
    )
    end
end

platform :android do
    desc "Android build and release to beta"
    lane :local do
        sh("ENVFILE=.env.local")
        sh("cp", "../.env.local", "../android/.env")
        gradle(task: 'clean', project_dir: './android/')
        gradle(task: 'assemble', build_type: 'Release', project_dir: './android', system_properties: {"ENVFILE": ".env.local"})
    end
    lane :beta_dev do
        sh("ENVFILE=.env.dev")
        sh("cp", "../.env.dev", "../android/.env")
        # increment_version_code(app_folder_name: './android/app')
        gradle(task: 'clean', project_dir: './android/')
        sh("cp", "../.env.dev", "../android/.env")
        gradle(task: 'bundle', build_type: 'Release', project_dir: './android', system_properties: {"ENVFILE": ".env.dev"} )
        supply(track: 'beta', aab: './android/app/build/outputs/bundle/release/app-release.aab')
    end 
    lane :beta_release do
        sh("ENVFILE=.env.prod")
        sh("cp", "../.env.prod", "../android/.env")
        gradle(task: 'clean', project_dir: './android/', system_properties: {"ENVFILE": ".env.prod"} )
        sh("cp", "../.env.prod", "../android/.env")
        gradle(task: 'bundle', build_type: 'Release', project_dir: './android' , system_properties: {"ENVFILE": ".env.prod"} )
        supply(track: 'beta', aab: './android/app/build/outputs/bundle/release/app-release.aab')
    end
    lane :release do
        sh("ENVFILE=.env.prod")
        sh("cp", "../.env.prod", "../android/.env")
        gradle(task: 'clean', project_dir: './android/', system_properties: {"ENVFILE": ".env.prod"} )
        sh("cp", "../.env.prod", "../android/.env")
        gradle(task: 'bundle', build_type: 'Release', project_dir: './android' , system_properties: {"ENVFILE": ".env.prod"} )
        supply(track: 'production', aab: './android/app/build/outputs/bundle/release/app-release.aab',
        skip_upload_screenshots: 'true')
        add_git_tag(grouping: "android-builds",includes_lane: true, prefix: "v", build_number: ENV["VERSION_NAME"] )
        # push_to_git_remote
    end

    # fastlane action supply
end

lane :codepush_ios do |options|
    current_version = get_version_number(xcodeproj: './ios/selfinvite.xcodeproj', target: 'selfinvite')
    codepush(current_version: current_version, manditory: options[:manditory])
end

lane :codepush_android do |options|
    current_version = google_play_track_release_names
    codepush(current_version: current_version, manditory: options[:manditory])
end

private_lane :select_app_version do |options|
    current_version = options[:current_version]
    current_major = [current_version.split(".").first, 'x', 'x'].join('.')
    current_minor = current_version.split('.').slice(0,2).push('x').join('.')
    target_version_label = UI.select("What version do you want to target?", [
        "All users",
        "Most recent major (#{current_major})",
        "Most recent minor (#{current_mior})",
        "Current version (#{current_version})"
    ])

    next "\"*\"" if target_version_label.match(/All/)
    next current_major if target_version_label.match(/major/)
    next current_minor if target_version_label.match(/minor/)

    current_version
end

lane :codepush do |options|
    manditory = !!options[:manditory]
    manditory_string = manditory ? " -m" : ""
    version = select_app_version(current_version: options[:current_version])
    if UI.confirm("Going to CodePush #{version} to production. Feeling lucky punk?")
        Dir.chdir("..") do
            sh "appcenter codepush release-react -a selfinvite -d Production -t #{version}#{maditory_string} --output-dir ./build" do |status, result, command|
            unless status.success?
                UI.error "Command #{command} failed with status #{status.exitstatus}"
            end
            UI.message "Finished! Check out the release on App center."
        end
        end
    else
        UI.error "Not going to push"
    end
end