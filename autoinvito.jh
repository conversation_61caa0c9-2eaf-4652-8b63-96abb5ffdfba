// JDL definition for application 'autoinvito' generated with command 'jhipster export-jdl'
application {
  config {
    baseName                gateway,
    applicationType         gateway,
    packageName             com.j.autoinvito,
    serviceDiscoveryType    eureka,
    authenticationType      uaa,
    buildTool               maven,
    clientFramework         angularX,
    clientPackageManager    npm,
    websocket               spring-websocket,
    uaaBaseName             "uaa",
    useSass                 true,
    enableTranslation       true,
    nativeLanguage          en,
    serverPort              80,
    languages               [en,fr,it,pt-pt,es],
    testFrameworks          [gatling, cucumber, protractor]
  }
  entities *
}

application {
  config {
    baseName                autoinvito,
    applicationType         microservice,
    packageName             com.j.autoinvito,
    serviceDiscoveryType    eureka,
    authenticationType      uaa,
    uaaBaseName             "uaa",
    cacheProvider           hazelcast,
    enableHibernateCache    true,
    prodDatabaseType        mysql,
    devDatabaseType         mysql,
    buildTool               maven,
    skipClient              true,
    skipUserManagement      true
  }
  entities EventOffer, EventOfferKV, Message, Feedback, Files, Payment
}

application {
  config {
    baseName                uaa,
    packageName             com.j.autoinvito,
    applicationType         uaa,
    serverPort              9999,
    buildTool               maven,
    nativeLanguage          en,
    authenticationType      uaa,
    cacheProvider           hazelcast,
    prodDatabaseType        mysql,
    devDatabaseType         mysql,
    searchEngine            elasticsearch,
    enableHibernateCache    true,
    serviceDiscoveryType    eureka,
    testFrameworks          [gatling,cucumber],
    skipClient              true
  }
  entities JhiUserKV, UserFiles
}
/*
application {
  config {
    applicationType monolith,
    baseName autoinvito
    packageName com.j.autoinvito,
    authenticationType jwt,
    prodDatabaseType mysql,
    buildTool maven,
    searchEngine elasticsearch,
    testFrameworks [gatling, cucumber, protractor],
    clientFramework angularX,
    useSass true,
    enableTranslation true,
    nativeLanguage en,
    languages [en,fr,it,pt-pt,es]
  }
  entities EventOffer, Feedback, Message, EventOfferKV
}
*/
entity EventOffer {
  maxPartecipants Integer required
  typeK TypeKitchen
  typeB TypeBeverage
  typeE TypeEvent
  location Location required
  date ZonedDateTime required
  pics ImageBlob
  picsContentType String
  presentation TextBlob
  street String required minlength(8)
  city String required minlength(3)
  postalcode String required minlength(3)
  country String required minlength(3)
  number String minlength(6)
  pricepp Float required
  userLogin String required
  name String minlength(3) required
  lon Double
  lat Double
  state Integer
  type Integer required
  duration Integer required
  dateCreated ZonedDateTime required
}
entity EventOfferKV (event_offerkv) {
  k String required maxlength(255)
  v String required maxlength(2048)
  user String required maxlength(255)
  eOffer String required maxlength(36)
  changeTime ZonedDateTime required
  state Integer
}
entity Message {
  body TextBlob required
  state Integer
  dateTx ZonedDateTime required
  dateRead ZonedDateTime required
  stateTx Integer
  stateRx Integer
  userTx String required
  userRx String required
  request Integer
  thread String required maxlength(36)
}
entity Feedback {
  point Integer
  event String required maxlength(36)
  type Integer
  date Instant
  description String maxlength(255)
  senderId String required maxlength(36)
  receiverId String required maxlength(36)
}

entity Files {
  id String required maxlength(36)
  fileName String required maxlength(128)
  fileType String required maxlength(12)
  data Blob
}

entity UserFiles {
  id String required maxlength(36)
  fileName String required maxlength(128)
  fileType String required maxlength(12)
  data Blob
}
entity JhiUserKV (jhi_userkv) {
  id Long
  changeTime ZonedDateTime required
  k String required maxlength(255)
  v String required maxlength(2048)
  userId Long
}

entity Payment {
  id Long
  user String required maxlength(255)
  eventOfferId String required maxlength(255)
  paymentAmount Integer
  paymentStatus String required maxlength(255)
  paymentDate ZonedDateTime required
}

enum TypeKitchen {
  NOT_SPECIFIED,
  ETHNIC,
  FUSION,
  MEDITERRAN,
  ASIAN,
  CHINESE,
  JAPANESE,
  TURKISH,
  GREEK,
  ITALIAN,
  AFRICAN,
  VEGETERIAN,
  VEGAN,
  STREET_FOOD,
  FISH,
  MEAT,
  OTHER,
  JUNK_FOOD,
  THAILANDESE,
  MEXICAN,
  ARGENTINA,
  BRASILIAN,
  PERUVIAN
}

enum TypeBeverage {
  NOT_SPECIFIED,
  ALCOHOLIC,
  ANALCOHOLIC,
  WINE,
  COCKTAILS,
  BEER
}

enum TypeEvent {
  DINNER,
  LUNCH,
  BRUNCH,
  PICNIC,
  BBQ,
  APERITIF,
  OTHER
}

enum Location {
  HOME,
  GARDEN,
  TERRACE,
  STREET,
  CANTEEN,
  OTHER
}

/*
relationship ManyToOne {
  EventOfferKV{user(login) required} to User,
  EventOfferKV{eOffer(id) required} to EventOffer,
  Message{userTx(login)} to User,
  Message{userRx(login)} to User
}
*/
/*
dto EventOffer, EventOfferKV, Message, Feedback with mapstruct
paginate EventOffer, Message, Feedback with infinite-scroll
service EventOffer, EventOfferKV, Message, Feedback with serviceClass
filter EventOfferKV
*/
