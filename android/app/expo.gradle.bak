// Gradle script for detached apps.

import org.apache.tools.ant.taskdefs.condition.Os

void runBefore(String dependentTaskName, Task task) {
  Task dependentTask = tasks.findByPath(dependentTaskName);
  if (dependentTask != null) {
    dependentTask.dependsOn task
  }
}

afterEvaluate {
  def expoRoot = file("../../")
  def inputExcludes = ["android/**", "ios/**"]

  task exponentPrebuildStep(type: Exec) {
    workingDir expoRoot
    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
      commandLine "cmd", "/c", ".\\node_modules\\expokit\\detach-scripts\\run-exp.bat"
    } else {
      commandLine "./node_modules/expokit/detach-scripts/run-exp.sh", "prepare-detached-build", "--platform", "android", expoRoot
    }
  }
  runBefore("preBuild", exponentPrebuildStep)

  // Based on https://github.com/facebook/react-native/blob/master/react.gradle

  android.applicationVariants.each { variant ->
    def folderName = variant.name
    def targetName = folderName.capitalize()

    def assetsDir = file("$buildDir/intermediates/merged_assets/${folderName}/out")

    // Bundle task name for variant
    def bundleExpoAssetsTaskName = "bundle${targetName}ExpoAssets"

    def currentBundleTask = tasks.create(
        name: bundleExpoAssetsTaskName,
        type: Exec) {
      description = "Expo bundle assets for ${targetName}."

      // Create dirs if they are not there (e.g. the "clean" task just ran)
      doFirst {
        assetsDir.mkdirs()
      }

      // Set up inputs and outputs so gradle can cache the result
      inputs.files fileTree(dir: expoRoot, excludes: inputExcludes)
      outputs.dir assetsDir

      // Set up the call to exp
      workingDir expoRoot

      if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        commandLine("cmd", "/c", ".\\node_modules\\expokit\\detach-scripts\\run-exp.bat", "bundle-assets", expoRoot, "--platform", "android", "--dest", assetsDir)
      } else {
        commandLine("./node_modules/expokit/detach-scripts/run-exp.sh", "bundle-assets", expoRoot, "--platform", "android", "--dest", assetsDir)
      }

      enabled targetName.toLowerCase().contains("release") || targetName.toLowerCase().contains("prod")
    }

    currentBundleTask.dependsOn("merge${targetName}Resources")
    currentBundleTask.dependsOn("merge${targetName}Assets")

    runBefore("process${targetName}Resources", currentBundleTask)
  }
}
