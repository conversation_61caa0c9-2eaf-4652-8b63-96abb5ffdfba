apply plugin: 'com.android.application'
apply plugin: "org.jetbrains.kotlin.android"
apply plugin: "com.facebook.react"
import com.android.build.OutputFile

project.ext.envConfigFiles = [
    debug: ".env.dev",
    release: ".env.prod",
    local: ".env.local",
]
apply from: project(':react-native-config').projectDir.getPath() + "/dotenv.gradle"
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

def projectRoot = rootDir.getAbsoluteFile().getParentFile().getAbsolutePath()
def expoDebuggableVariants = ['debug']
// Override `debuggableVariants` for expo-updates debugging
if (System.getenv('EX_UPDATES_NATIVE_DEBUG') == "1") {
  react {
    expoDebuggableVariants = []
  }
}

/*
 * This is the configuration block to customize your React Native Android app.
 * By default you don't need to apply any configuration, just uncomment the lines you need.
 */

// project.ext.vectoricons = [
// 	iconFontNames: ['MaterialCommunityIcons.ttf', 'EvilIcons.ttf', 'AntDesign.tff', 'Entypo.tff', 'Feather.tff', 'FontAwesome.tff', 'FontAwesome.tff', 'Fontisto.tff', 'Ionicons.tff', 'MaterialIcons.tff', 'Octicons.tff', 'Zocial.tff']
// ]

apply from: "../../node_modules/react-native-vector-icons/fonts.gradle"

react {
    
    entryFile = file(["node", "-e", "require('expo/scripts/resolveAppEntry')", projectRoot, "android", "absolute"].execute(null, rootDir).text.trim())
    reactNativeDir = new File(["node", "--print", "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim()).getParentFile().getAbsoluteFile()

    hermesCommand = new File(["node", "--print", "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim()).getParentFile().getAbsolutePath() + "/sdks/hermesc/%OS-BIN%/hermesc"
    debuggableVariants = expoDebuggableVariants

    codegenDir = new File(["node", "--print", "require.resolve('@react-native/codegen/package.json', { paths: [require.resolve('react-native/package.json')] })"].execute(null, rootDir).text.trim()).getParentFile().getAbsoluteFile()

    cliFile = new File(["node", "--print", "require.resolve('@expo/cli', { paths: [require.resolve('expo/package.json')] })"].execute(null, rootDir).text.trim())
    bundleCommand = "export:embed"
}

ext {
  hermesEnabled = (findProperty('expo.jsEngine') ?: "hermes") == "hermes"
}
/**
 * Set this to true to create four separate APKs instead of one,
 * one for each native architecture. This is useful if you don't
 * use App Bundles (https://developer.android.com/guide/app-bundle/)
 * and want to have separate APKs to upload to the Play Store.
 */
def enableSeparateBuildPerCPUArchitecture = true

/**
 * Set this to true to Run Proguard on Release builds to minify the Java bytecode.
 */
def enableProguardInReleaseBuilds = (findProperty('android.enableProguardInReleaseBuilds') ?: false).toBoolean()

/**
 * The preferred build flavor of JavaScriptCore (JSC)
 *
 * For example, to use the international variant, you can use:
 * `def jscFlavor = 'org.webkit:android-jsc-intl:+'`
 *
 * The international variant includes ICU i18n library and necessary data
 * allowing to use e.g. `Date.toLocaleString` and `String.localeCompare` that
  * give correct results when using with locales other than en-US. Note that
 * this variant is about 6MiB larger per architecture than default.
 */
def jscFlavor = 'org.webkit:android-jsc:+'

/**
 * Private function to get the list of Native Architectures you want to build.
 * This reads the value from reactNativeArchitectures in your gradle.properties
 * file and works together with the --active-arch-only flag of react-native run-android.
 */
def reactNativeArchitectures() {
    def value = project.getProperties().get("reactNativeArchitectures")
    return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

android {
    
    ndkVersion rootProject.ext.ndkVersion
    // compileSdkVersion rootProject.ext.compileSdkVersion
    buildToolsVersion rootProject.ext.buildToolsVersion
    compileSdk rootProject.ext.compileSdkVersion

    namespace 'com.j.autoinvito'
    defaultConfig {
        
        vectorDrawables.useSupportLibrary = true
        // applicationId 'com.j.autoinvito'
        resValue "string", "build_config_package", "com.j.autoinvito"
        applicationId project.env.get("APP_ID")
        manifestPlaceholders = [auth0Domain: project.env.get("REACT_APP_AUTH0_DOMAIN"),
                                auth0Scheme: "${applicationId}.auth0"]
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionName project.env.get("VERSION_NAME")
        versionCode 300
        //Detox
        testBuildType System.getProperty('testBuildType', 'debug')  // This will later be used to control the test apk build type
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        
        resValue "string", "GOOGLE_MAPS_API_KEY", project.env.get("GOOGLE_MAPS_API_KEY")
    }

    packagingOptions {
        jniLibs {
            useLegacyPackaging (findProperty('expo.useLegacyPackaging')?.toBoolean() ?: false)
        }
    }

    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
        release {
            storeFile file(project.env.get("ANDROID_KEYSTORE_FILE"))
            storePassword project.env.get("ANDROID_STORE_PASSWORD")
            keyAlias project.env.get("ANDROID_KEY_ALIAS")
            keyPassword project.env.get("ANDROID_KEY_PASSWORD")
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.debug
        }
        release {
            /* Add the firebaseCrashlytics extension (by default,
            * it's disabled to improve build speeds) and set
            * nativeSymbolUploadEnabled to true. */
            firebaseCrashlytics {
                nativeSymbolUploadEnabled true
                // strippedNativeLibsDir 'build/intermediates/stripped_native_libs/release/out/lib'
                unstrippedNativeLibsDir 'build/intermediates/merged_native_libs/release/out/lib'
            }

            // Caution! In production, you need to generate your own keystore file.
            // see https://facebook.github.io/react-native/docs/signed-apk-android.
            signingConfig signingConfigs.release
            minifyEnabled enableProguardInReleaseBuilds
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            
            // shrinkResources (findProperty('android.enableShrinkResourcesInReleaseBuilds')?.toBoolean() ?: false)
            // Detox-specific additions to pro-guard
            proguardFile "${rootProject.projectDir}/../node_modules/detox/android/detox/proguard-rules-app.pro"
        }
    }
}

// Apply static values from `gradle.properties` to the `android.packagingOptions`
// Accepts values in comma delimited lists, example:
// android.packagingOptions.pickFirsts=/LICENSE,**/picasa.ini
["pickFirsts", "excludes", "merges", "doNotStrip"].each { prop ->
    // Split option: 'foo,bar' -> ['foo', 'bar']
    def options = (findProperty("android.packagingOptions.$prop") ?: "").split(",");
    // Trim all elements in place.
    for (i in 0..<options.size()) options[i] = options[i].trim();
    // `[] - ""` is essentially `[""].filter(Boolean)` removing all empty strings.
    options -= ""

    if (options.length > 0) {
        println "android.packagingOptions.$prop += $options ($options.length)"
        // Ex: android.packagingOptions.pickFirsts += '**/SCCS/**'
        options.each {
            android.packagingOptions[prop] += it
        }
    }
}

dependencies {
    // The version of react-native is set by the React Native Gradle Plugin
    implementation("com.facebook.react:react-android")
    implementation("androidx.swiperefreshlayout:swiperefreshlayout:1.0.0")
    

    def isGifEnabled = (findProperty('expo.gif.enabled') ?: "") == "true";
    def isWebpEnabled = (findProperty('expo.webp.enabled') ?: "") == "true";
    def isWebpAnimatedEnabled = (findProperty('expo.webp.animated') ?: "") == "true";
    // def frescoVersion = rootProject.ext.frescoVersion

    // If your app supports Android versions before Ice Cream Sandwich (API level 14)
    // if (isGifEnabled || isWebpEnabled) {
    //     implementation("com.facebook.fresco:fresco:${frescoVersion}")
    //     implementation("com.facebook.fresco:imagepipeline-okhttp3:${frescoVersion}")
    // }

    if (isGifEnabled) {
        // For animated gif support
        // implementation("com.facebook.fresco:animated-gif:${frescoVersion}")
        implementation("com.facebook.fresco:animated-gif:${reactAndroidLibs.versions.fresco.get()}")
    }

    if (isWebpEnabled) {
        // For webp support
        // implementation("com.facebook.fresco:webpsupport:${frescoVersion}")
        implementation("com.facebook.fresco:webpsupport:${reactAndroidLibs.versions.fresco.get()}")
        if (isWebpAnimatedEnabled) {
            // Animated webp support
            // implementation("com.facebook.fresco:animated-webp:${frescoVersion}")
            implementation("com.facebook.fresco:animated-webp:${reactAndroidLibs.versions.fresco.get()}")
        }
    }

    implementation "com.google.android.gms:play-services-base:16.1.0"
    implementation 'com.google.firebase:firebase-config:19.2.0'
    implementation "com.google.firebase:firebase-messaging:22.0.0"
    implementation 'com.google.firebase:firebase-analytics:19.0.0'

    implementation "com.google.firebase:firebase-crashlytics:18.1.0" 
    implementation "com.google.firebase:firebase-crashlytics-ndk:18.1.0"

    // debugImplementation("com.facebook.flipper:flipper:${FLIPPER_VERSION}")
    // debugImplementation("com.facebook.flipper:flipper-network-plugin:${FLIPPER_VERSION}") {
        // exclude group:'com.squareup.okhttp3', module:'okhttp'
    // }

    // debugImplementation("com.facebook.flipper:flipper-fresco-plugin:${FLIPPER_VERSION}")

    implementation("com.facebook.react:flipper-integration:0.73.7")

    implementation 'com.google.android.gms:play-services-wallet:19.3.0'
    if (hermesEnabled.toBoolean()) {
        implementation("com.facebook.react:hermes-android")
    } else {
        implementation jscFlavor
    }
}

// apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle");
apply from: new File(["node", "--print", "require.resolve('@react-native-community/cli-platform-android/package.json', { paths: [require.resolve('react-native/package.json')] })"].execute(null, rootDir).text.trim(), "../native_modules.gradle");
applyNativeModulesAppBuildGradle(project)
