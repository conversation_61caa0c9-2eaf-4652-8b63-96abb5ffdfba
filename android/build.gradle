// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = findProperty('android.buildToolsVersion') ?: '34.0.0'
        minSdkVersion = Integer.parseInt(findProperty('android.minSdkVersion') ?: '23')
        compileSdkVersion = Integer.parseInt(findProperty('android.compileSdkVersion') ?: '34')
        targetSdkVersion = Integer.parseInt(findProperty('android.targetSdkVersion') ?: '34')
        
        kotlinVersion = findProperty('android.kotlinVersion') ?: '1.8.10'
        
        ndkVersion = "25.1.8937393"
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle'
        classpath 'com.facebook.react:react-native-gradle-plugin'
        classpath 'com.google.gms:google-services:4.3.15'
        classpath 'de.undercouch:gradle-download-task:3.4.3'
        //detox
        // classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

apply plugin: "com.facebook.react.rootproject"

allprojects {
    repositories {
        mavenCentral()
        mavenLocal()
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url(new File(['node', '--print', "require.resolve('react-native/package.json')"].execute(null, rootDir).text.trim(), '../android'))
        }
         maven {
             // Android JSC is installed from npm
             url(new File(['node', '--print', "require.resolve('jsc-android/package.json', { paths: [require.resolve('react-native/package.json')] })"].execute(null, rootDir).text.trim(), '../dist'))
         }

        google()
        maven { url 'https://www.jitpack.io' }
        maven {
            url("$rootDir/../node_modules/detox/Detox-android")
        }
        maven {
            url "$rootDir/../node_modules/expo-camera/android/maven"
        }
    }
}
// @generated begin expo-camera-import - expo prebuild (DO NOT MODIFY) sync-f244f4f3d8bf7229102e8f992b525b8602c74770
def expoCameraMavenPath = new File(["node", "--print", "require.resolve('expo-camera/package.json')"].execute(null, rootDir).text.trim(), "../android/maven")
allprojects { repositories { maven { url(expoCameraMavenPath) } } }
// @generated end expo-camera-import