distributionBase=GRADLE_USER_HOME
distributionPath=wrapper/dists
distributionUrl=https\://services.gradle.org/distributions/gradle-8.3-all.zip
validateDistributionUrl=true
networkTimeout=10000
zipStoreBase=GRADLE_USER_HOME
zipStorePath=wrapper/dists


# 7.6.1
# A problem occurred configuring root project 'Selfinvite'.
# > Could not determine the dependencies of null.
#    > Could not resolve all dependencies for configuration ':classpath'.
#       > The new Java toolchain feature cannot be used at the project level in combination with source and/or target compatibility


# 7.5
# * Where:
# Settings file '/Users/<USER>/projects/si/selfinvite/node_modules/@react-native/gradle-plugin/settings.gradle.kts' line: 16

# * What went wrong:
# An exception occurred applying plugin request [id: 'org.gradle.toolchains.foojay-resolver-convention', version: '0.5.0']
# > Failed to apply plugin class 'org.gradle.toolchains.foojay.FoojayToolchainsPlugin'.
#    > FoojayToolchainsPlugin needs Gradle version 7.6 or higher

# 8.1
# * Where:
# Build file '/Users/<USER>/projects/si/selfinvite/android/app/build.gradle' line: 1

# * What went wrong:
# A problem occurred evaluating project ':app'.
# > Failed to apply plugin 'com.android.internal.version-check'.
#    > Minimum supported Gradle version is 8.2. Current version is 8.1. If using the gradle wrapper, try editing the distributionUrl in /Users/<USER>/projects/si/selfinvite/android/gradle/wrapper/gradle-wrapper.properties to gradle-8.2-all.zip


# 8.2
# FAILURE: Build failed with an exception.

# * What went wrong:
# Execution failed for task ':expo-modules-core$android-annotation:compileKotlin'.
# > Inconsistent JVM-target compatibility detected for tasks 'compileJava' (11) and 'compileKotlin' (17).