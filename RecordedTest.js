describe('Recorded suite', () => {
	it('My Recorded Test', async () => {
		await waitFor(element(by.type("RCTRefreshControl"))).toBeVisible().whileElement(element(by.id("SearchFlatList"))).scroll(50, "down");
		await element(by.type("RCTRefreshControl")).tap();
		await element(by.label("\udb80\ude08")).atIndex(2).tap();
		await element(by.type("RCTImageView")).atIndex(2).tap();
		await element(by.type("RCTImageView")).atIndex(3).tap();
		await element(by.type("RCTView")).atIndex(31).tap();
		await waitFor(element(by.type("RCTCustomScrollView")).atIndex(1)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.type("RCTCustomScrollView")).atIndex(1).scroll(163, "right");
		await element(by.type("RCTCustomScrollView")).atIndex(2).scroll(69.667, "right");
		await element(by.type("RCTCustomScrollView")).atIndex(3).scroll(69.333, "right");
		await waitFor(element(by.type("RCTView")).atIndex(126)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(3)).scroll(50, "down");
		await element(by.type("RCTView")).atIndex(126).tap();
		await element(by.type("RCTCustomScrollView")).atIndex(4).scroll(390, "right");
		await element(by.type("RCTCustomScrollView")).atIndex(4).scroll(1190, "down");
		await element(by.label("Participate")).atIndex(1).tap();
		await element(by.text("OK")).tap();
		await element(by.type("RCTCustomScrollView")).atIndex(0).scroll(1190, "up");
		await element(by.id("header-back")).tap();
		await element(by.label("\udb80\udf4d")).atIndex(1).tap();
		await element(by.text("\udb80\ude08")).tap();
		await element(by.type("RCTImageView")).atIndex(4).tap();
		await element(by.type("RCTView")).atIndex(37).tap();
		await element(by.text("19.2")).tap();
		await element(by.id("header-back")).atIndex(1).tap();
		await element(by.type("GMSVectorMapView")).tap();
		await element(by.text("NEXT")).tap();
		await element(by.text("NEXT")).tap();
		await element(by.type("RCTView")).atIndex(31).tap();
		await element(by.type("GMSVectorMapView")).tap();
		await element(by.text("\udb80\ude08")).tap();
		await element(by.id("header-back")).atIndex(1).tap();
		await element(by.id("header-back")).tap();
		await element(by.id("Searchbar")).tap();
		await element(by.id("Searchbar")).replaceText("C");
		await element(by.id("Searchbar")).replaceText("Ci");
		await element(by.id("Searchbar")).replaceText("Cia");
		await element(by.id("Searchbar")).replaceText("Ciao");
		await element(by.id("Searchbar")).tapReturnKey();
		await element(by.label("\udb80\udd56")).atIndex(1).tap();
		await element(by.label("\udb80\udd56")).atIndex(3).tap();
		await element(by.id("SearchbarButton")).tap();
		await element(by.id("SearchDialogPrice")).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.type("RCTView")).atIndex(113).longPress(500);
		await element(by.id("SearchDialogLocation")).tap();
		await element(by.id("SearchDialogLocationElements")).tap();
		await element(by.id("SearchDialogLocationElements")).tap();
		await waitFor(element(by.label("Date \udb80\udd40")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.label("Date \udb80\udd40")).atIndex(2).tap();
		await waitFor(element(by.id("SearchDialog"))).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await waitFor(element(by.label("Kitchen \udb80\udd40")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.label("Kitchen \udb80\udd40")).atIndex(2).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await waitFor(element(by.label("Guests \udb80\udd40")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.label("Guests \udb80\udd40")).atIndex(2).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await waitFor(element(by.label("Drink \udb80\udd40")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.label("Drink \udb80\udd40")).atIndex(2).tap();
		await element(by.id("SearchDialog")).tap();
		await waitFor(element(by.label("Events \udb80\udd40")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.label("Events \udb80\udd40")).atIndex(2).tap();
		await waitFor(element(by.id("SearchDialog"))).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await waitFor(element(by.label("Food Intolerances \udb80\udd40")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.label("Food Intolerances \udb80\udd40")).atIndex(2).tap();
		await waitFor(element(by.id("SearchDialog"))).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await waitFor(element(by.label("Duration (Minutes) \udb80\udd40")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.label("Duration (Minutes) \udb80\udd40")).atIndex(2).tap();
		await waitFor(element(by.id("SearchDialog"))).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.id("SearchDialog")).tap();
		await element(by.id("SearchDialog")).tap();
		await element(by.label("APPLY")).atIndex(2).tap();
		await element(by.label("Close")).atIndex(2).tap();
		await element(by.label("Close")).atIndex(2).tap();
		await element(by.label("Close")).atIndex(1).tap();
		await element(by.label("Close")).atIndex(7).tap();
		await element(by.label("Close")).atIndex(7).tap();
		await element(by.label("Close")).atIndex(6).tap();
		await element(by.label("Close")).atIndex(6).tap();
		await element(by.label("Close")).atIndex(7).tap();
		await element(by.label("Close")).atIndex(7).tap();
		await element(by.label("Close")).atIndex(6).tap();
		await element(by.label("Close")).atIndex(5).tap();
		await element(by.label("Close")).atIndex(2).tap();
		await element(by.label("Close")).atIndex(1).tap();
		await element(by.label("\udb80\udf5c")).atIndex(3).tap();
		await element(by.text("Login")).tap();
		await element(by.id("username")).tap();
		await element(by.id("username")).replaceText("R");
		await element(by.id("username")).replaceText("Re");
		await element(by.id("username")).replaceText("Res");
		await element(by.id("username")).replaceText("Resu");
		await element(by.id("password")).replaceText("u");
		await element(by.id("password")).replaceText("us");
		await element(by.id("password")).replaceText("use");
		await element(by.id("password")).replaceText("user");
		await element(by.label("\udb80\udf42 LOGIN")).atIndex(2).tap();
		await element(by.label("\udb80\ude08")).atIndex(2).tap();
		await element(by.type("RCTImageView")).atIndex(3).tap();
		await element(by.type("RCTView")).atIndex(40).tap();
		await element(by.type("RCTImageView")).atIndex(2).tap();
		await element(by.label("Reviews: 0 \uf006 \uf006 \uf006 \uf006 \uf006")).tap();
		await element(by.text("\ue87f")).atIndex(1).tap();
		await element(by.type("RCTImageView")).atIndex(1).tap();
		await element(by.type("RCTImageView")).atIndex(1).tap();
		await element(by.label("PAST EVENTS")).atIndex(3).tap();
		await element(by.label("Back")).atIndex(1).tap();
		await element(by.type("RCTImageView")).atIndex(1).tap();
		await element(by.type("RCTCustomScrollView")).atIndex(0).scroll(1173, "down");
		await element(by.label("Participate")).atIndex(1).tap();
		await element(by.id("choice2")).tap();
		await element(by.label("CONTINUE")).atIndex(2).tap();
		await element(by.label("\udb80\udd2c")).atIndex(0).tap();
		await element(by.id("header-back")).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
		await element(by.text("Outgoing requests")).tap();
		await element(by.text("ALL")).tap();
		await element(by.label("\udb82\udd54 Pending")).atIndex(1).tap();
		await element(by.text("PENDING")).tap();
		await element(by.label("\udb80\udd2d Completed")).atIndex(1).tap();
		await element(by.text("COMPLETED")).tap();
		await element(by.label("\udb80\udf9f All")).atIndex(1).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
		await element(by.text("Incoming requests")).tap();
		await element(by.label("\udb80\udd2c OK")).atIndex(3).tap();
		await element(by.label("\udb81\ude41")).atIndex(0).tap();
		await element(by.label("\udb81\udcba SORT")).atIndex(4).tap();
		await element(by.label("\udb81\udcbc Asc")).atIndex(1).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
		await element(by.type("RCTView")).atIndex(22).tap();
		await element(by.text("\udb80\uddb4")).atIndex(0).tap();
		await element(by.text("NO")).tap();
		await element(by.text("\udb80\ude08")).atIndex(0).tap();
		await element(by.id("header-back")).tap();
		await element(by.label("\udb80\udd2c OK")).atIndex(3).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
		await element(by.text("Messages")).tap();
		await element(by.text("user11")).atIndex(0).tap();
		await element(by.id("Type a message...")).tap();
		await element(by.id("Type a message...")).replaceText("C");
		await element(by.id("Type a message...")).replaceText("Ci");
		await element(by.id("Type a message...")).replaceText("Cia");
		await element(by.id("Type a message...")).replaceText("Ciao");
		await element(by.label("\udb83\udcdf")).atIndex(2).tap();
		await element(by.type("RCTCustomScrollView")).scroll(370.667, "down");
		await element(by.id("header-back")).tap();
		await element(by.label("Messages")).atIndex(3).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
		await element(by.text("@resu")).tap();
		await element(by.text("Edit avatar")).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
		await element(by.text("Edit profile")).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
		await element(by.label("\udb80\udd1c Phone number")).atIndex(2).tap();
		await element(by.type("RCTUITextField")).atIndex(2).tap();
		await element(by.text("1")).atIndex(0).replaceText("1");
		await element(by.text("12")).atIndex(0).replaceText("12");
		await element(by.text("123")).atIndex(0).replaceText("123");
		await element(by.text("1233")).atIndex(0).replaceText("1233");
		await element(by.text("12334")).atIndex(0).replaceText("12334");
		await element(by.text("123342")).atIndex(0).replaceText("123342");
		await element(by.label("VERIFY")).atIndex(2).tap();
		await element(by.label("\udb80\udd2c OK")).atIndex(3).tap();
		await element(by.id("ios_touchable_wrapper")).tap();
		await element(by.id("ios_picker")).setColumnToValue(0, "IT (+39)");
		await element(by.id("done_text")).tap();
		await element(by.type("RCTImageView")).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
		await element(by.label("\udb80\udcf3")).atIndex(1).tap();
		await element(by.type("RCTUITextField")).tap();
		await element(by.text("I")).atIndex(0).replaceText("I");
		await element(by.type("RCTUITextField")).clearText();
		await element(by.text("C")).atIndex(0).replaceText("C");
		await element(by.text("Ci")).atIndex(0).replaceText("Ci");
		await element(by.text("Cip")).atIndex(0).replaceText("Cip");
		await element(by.text("Cipp")).atIndex(0).replaceText("Cipp");
		await element(by.text("Cippo")).atIndex(0).replaceText("Cippo");
		await element(by.text("Cippo ")).atIndex(0).replaceText("Cippo ");
		await element(by.text("Cippo \u00a3")).atIndex(0).replaceText("Cippo \u00a3");
		await element(by.text("Cippo \u00a3e")).atIndex(0).replaceText("Cippo \u00a3e");
		await element(by.text("Cippo \u00a3ev")).atIndex(0).replaceText("Cippo \u00a3ev");
		await element(by.text("Cippo \u00a3eve")).atIndex(0).replaceText("Cippo \u00a3eve");
		await element(by.text("Cippo \u00a3even")).atIndex(0).replaceText("Cippo \u00a3even");
		await element(by.text("Cippo \u00a3event")).atIndex(0).replaceText("Cippo \u00a3event");
		await element(by.text("Cippo \u00a3even")).atIndex(0).replaceText("Cippo \u00a3even");
		await element(by.text("Cippo \u00a3eve")).atIndex(0).replaceText("Cippo \u00a3eve");
		await element(by.text("Cippo \u00a3ev")).atIndex(0).replaceText("Cippo \u00a3ev");
		await element(by.text("Cippo \u00a3e")).atIndex(0).replaceText("Cippo \u00a3e");
		await element(by.text("Cippo \u00a3")).atIndex(0).replaceText("Cippo \u00a3");
		await element(by.text("Cippo ")).atIndex(0).replaceText("Cippo ");
		await element(by.text("Cippo \u00a3")).atIndex(0).replaceText("Cippo \u00a3");
		await element(by.text("Cippo ")).atIndex(0).replaceText("Cippo ");
		await element(by.text("Cippo \u2019")).atIndex(0).replaceText("Cippo \u2019");
		await element(by.text("Cippo ")).atIndex(0).replaceText("Cippo ");
		await element(by.text("Cippo e")).atIndex(0).replaceText("Cippo e");
		await element(by.text("Cippo ev")).atIndex(0).replaceText("Cippo ev");
		await element(by.text("Cippo eve")).atIndex(0).replaceText("Cippo eve");
		await element(by.text("Cippo even")).atIndex(0).replaceText("Cippo even");
		await element(by.text("Cippo event")).atIndex(0).replaceText("Cippo event");
		await element(by.type("RCTImageView")).atIndex(2).tap();
		await element(by.type("RCTImageView")).atIndex(2).tap();
		await element(by.type("RCTImageView")).atIndex(2).tap();
		await element(by.text("Select")).atIndex(0).tap();
		await element(by.text("Japanese")).tap();
		await element(by.text("Asian")).tap();
		await element(by.label("Confirm")).atIndex(2).tap();
		await element(by.text("Select")).atIndex(0).tap();
		await element(by.text("Beer")).tap();
		await element(by.text("Liquor")).tap();
		await element(by.label("Confirm")).atIndex(2).tap();
		await element(by.text("Select")).atIndex(0).tap();
		await element(by.label("Dinner")).atIndex(4).tap();
		await element(by.text("Aperitif")).tap();
		await element(by.label("Confirm")).atIndex(2).tap();
		await waitFor(element(by.text("Select")).atIndex(0)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView"))).scroll(50, "down");
		await element(by.text("Select")).atIndex(0).tap();
		await element(by.text("Dairy")).tap();
		await element(by.text("Soy")).tap();
		await element(by.label("Confirm")).atIndex(2).tap();
		await waitFor(element(by.label("Select \ue313")).atIndex(1)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView"))).scroll(50, "down");
		await element(by.label("Select \ue313")).atIndex(1).tap();
		await element(by.text("Park")).tap();
		await waitFor(element(by.text("Select a date"))).toBeVisible().whileElement(element(by.type("RCTCustomScrollView"))).scroll(50, "down");
		await element(by.text("Select a date")).tap();
		await element(by.label("Jul 15, 2021")).atIndex(0).tap();
		await element(by.type("UICollectionView")).atIndex(0).scroll(272, "right");
		await element(by.type("RNDateTimePicker")).setDatePickerDate("2021-10-20T15:01:00+02:00", "ISO8601");
		await element(by.type("UIDatePicker")).atIndex(0).setDatePickerDate("2021-10-20T15:01:00+02:00", "ISO8601");
		await element(by.type("_UIContextMenuContainerView")).tap();
		await element(by.text("Confirm")).tap();
		await waitFor(element(by.label("\uf185 Pick an image")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView"))).scroll(50, "down");
		await element(by.label("\uf185 Pick an image")).atIndex(2).tap();
		await waitFor(element(by.type("RCTUITextView"))).toBeVisible().whileElement(element(by.type("RCTCustomScrollView"))).scroll(50, "down");
		await element(by.type("RCTUITextView")).tap();
		await element(by.text("O")).replaceText("O");
		await element(by.text("Ot")).replaceText("Ot");
		await element(by.text("Ott")).replaceText("Ott");
		await element(by.text("Otti")).replaceText("Otti");
		await element(by.text("Ottim")).replaceText("Ottim");
		await element(by.text("Ottima")).replaceText("Ottima");
		await element(by.text("Ottima ")).replaceText("Ottima ");
		await element(by.text("Ottima p")).replaceText("Ottima p");
		await element(by.text("Ottima pr")).replaceText("Ottima pr");
		await element(by.text("Ottima pre")).replaceText("Ottima pre");
		await element(by.text("Ottima pres")).replaceText("Ottima pres");
		await element(by.text("Ottima prese")).replaceText("Ottima prese");
		await element(by.text("Ottima presen")).replaceText("Ottima presen");
		await element(by.text("Ottima present")).replaceText("Ottima present");
		await element(by.text("Ottima presenta")).replaceText("Ottima presenta");
		await element(by.text("Ottima presentat")).replaceText("Ottima presentat");
		await element(by.text("Ottima presenta")).replaceText("Ottima presenta");
		await element(by.text("Ottima presentaz")).replaceText("Ottima presentaz");
		await element(by.text("Ottima presentazi")).replaceText("Ottima presentazi");
		await element(by.text("Ottima presentazio")).replaceText("Ottima presentazio");
		await element(by.text("Ottima presentazion")).replaceText("Ottima presentazion");
		await element(by.text("Ottima presentazione")).replaceText("Ottima presentazione");
		await element(by.text("Ottima presentazione ")).replaceText("Ottima presentazione ");
		await element(by.text("Ottima presentazione .")).replaceText("Ottima presentazione .");
		await element(by.text("Ottima presentazione ..")).replaceText("Ottima presentazione ..");
		await element(by.text("Ottima presentazione ...")).replaceText("Ottima presentazione ...");
		await element(by.text("Ottima presentazione ....")).replaceText("Ottima presentazione ....");
		await element(by.text("Ottima presentazione .... ")).replaceText("Ottima presentazione .... ");
		await element(by.text("Ottima presentazione .... \n")).replaceText("Ottima presentazione .... \n");
		await element(by.text("Ottima presentazione .... \n\n")).replaceText("Ottima presentazione .... \n\n");
		await element(by.text("Ottima presentazione .... \n\n\n")).replaceText("Ottima presentazione .... \n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWo")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWo");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWow")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWow");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowo")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowo");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowow")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowow");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowo")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowo");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow ")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow ");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nW");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\n")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\n");
		await element(by.text("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nW")).replaceText("Ottima presentazione .... \n\n\n\n\n\n\n\n\n\n\n\n\n\nWowowow \n\nE\n\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nE\nW");
		await element(by.type("RCTImageView")).atIndex(14).tap();
		await element(by.type("RCTImageView")).atIndex(14).tap();
		await element(by.type("RCTImageView")).atIndex(14).tap();
		await element(by.type("RCTImageView")).atIndex(14).tap();
		await element(by.type("RCTImageView")).atIndex(14).tap();
		await element(by.type("RCTView")).atIndex(141).tap();
		await element(by.type("RCTView")).atIndex(141).tap();
		await element(by.type("RCTView")).atIndex(141).tap();
		await element(by.type("RCTView")).atIndex(141).tap();
		await element(by.type("RCTView")).atIndex(141).tap();
		await element(by.type("RCTSwitch")).atIndex(0).tap();
		await element(by.type("RCTSwitch")).atIndex(0).tap();
		await element(by.text("\uf187")).atIndex(0).tap();
		await element(by.type("RCTView")).atIndex(107).tap();
		await element(by.text("\uf187")).atIndex(1).tap();
		await element(by.type("RCTView")).atIndex(107).tap();
		await waitFor(element(by.text("PICK ADDRESS"))).toBeVisible().whileElement(element(by.type("RCTCustomScrollView"))).scroll(50, "down");
		await element(by.text("PICK ADDRESS")).tap();
		await element(by.type("RCTUITextField")).atIndex(1).tap();
		await element(by.text("V")).atIndex(0).replaceText("V");
		await element(by.text("Vi")).atIndex(0).replaceText("Vi");
		await element(by.text("Via")).atIndex(0).replaceText("Via");
		await element(by.text("Via ")).atIndex(0).replaceText("Via ");
		await element(by.text("Via r")).atIndex(0).replaceText("Via r");
		await element(by.text("Via ri")).atIndex(0).replaceText("Via ri");
		await element(by.text("Via rim")).atIndex(0).replaceText("Via rim");
		await element(by.text("Via rima")).atIndex(0).replaceText("Via rima");
		await element(by.text("Via rimag")).atIndex(0).replaceText("Via rimag");
		await element(by.text("Via rimagg")).atIndex(0).replaceText("Via rimagg");
		await element(by.text("Via rimaggi")).atIndex(0).replaceText("Via rimaggi");
		await element(by.text("Via rimaggio")).atIndex(0).replaceText("Via rimaggio");
		await element(by.text("Via rimaggio ")).atIndex(0).replaceText("Via rimaggio ");
		await element(by.text("Via rimaggio 2")).atIndex(0).replaceText("Via rimaggio 2");
		await element(by.text("Via rimaggio 2")).atIndex(0).tapReturnKey();
		await element(by.text("SELECT")).tap();
		await waitFor(element(by.label("SAVE")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView"))).scroll(50, "down");
		await element(by.label("SAVE")).atIndex(2).tap();
		await element(by.label("\udb80\udd2c")).atIndex(0).tap();
		await element(by.text("\udb80\ude08")).atIndex(0).tap();
		await element(by.text("\ue87c User: resu")).tap();
		await element(by.type("RCTImageView")).atIndex(3).tap();
		await element(by.type("RCTView")).atIndex(153).tap();
		await element(by.text("\ue87c User: resu")).tap();
		await waitFor(element(by.type("RCTCustomScrollView")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView")).atIndex(0)).scroll(50, "down");
		await element(by.type("RCTCustomScrollView")).atIndex(2).scroll(31.667, "right");
		await element(by.type("RCTCustomScrollView")).atIndex(3).scroll(31.667, "right");
		await element(by.type("RCTCustomScrollView")).atIndex(3).scroll(1109, "down");
		await element(by.id("header-back")).tap();
		await element(by.label("\uf15f")).atIndex(2).tap();
		await element(by.label("\udb81\udf3a CANCEL \udb80\udd2c OK")).tap();
		await element(by.label("\udb80\udd2c OK")).atIndex(2).tap();
		await element(by.text("Cippo event")).tap();
		await element(by.text("Cippo event ")).atIndex(0).replaceText("Cippo event ");
		await element(by.text("Cippo event m")).atIndex(0).replaceText("Cippo event m");
		await element(by.text("Cippo event mo")).atIndex(0).replaceText("Cippo event mo");
		await element(by.text("Cippo event mod")).atIndex(0).replaceText("Cippo event mod");
		await element(by.text("Cippo event modi")).atIndex(0).replaceText("Cippo event modi");
		await element(by.text("Cippo event modif")).atIndex(0).replaceText("Cippo event modif");
		await element(by.text("Cippo event modifi")).atIndex(0).replaceText("Cippo event modifi");
		await element(by.text("Cippo event modifie")).atIndex(0).replaceText("Cippo event modifie");
		await element(by.text("Cippo event modified")).atIndex(0).replaceText("Cippo event modified");
		await waitFor(element(by.label("SAVE")).atIndex(2)).toBeVisible().whileElement(element(by.type("RCTCustomScrollView"))).scroll(50, "down");
		await element(by.label("SAVE")).atIndex(2).tap();
		await element(by.label("\udb80\udd2c")).atIndex(0).tap();
		await element(by.type("RCTImageView")).atIndex(0).tap();
	})
});