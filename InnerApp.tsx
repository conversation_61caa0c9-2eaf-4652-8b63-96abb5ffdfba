import React, { useEffect } from 'react';
import {
  NavigationContainer,
  useNavigationContainerRef,
} from '@react-navigation/native';
import routes from '@routes/routes';
import { Provider as PaperProvider } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';
import { persistor } from '@services/store';
import { PersistGate } from 'redux-persist/integration/react';
import { slides, theme, styles } from '@constants/intro';
import { Auth0Provider } from 'react-native-auth0';
import { appConfig } from '@config/app-config';
import AppIntroSlider from 'react-native-app-intro-slider';
import { Asset } from 'expo-asset';
import * as Font from 'expo-font';
import * as Icon from '@expo/vector-icons';
import * as SplashScreen from 'expo-splash-screen';
import { fonts } from '@constants/fonts';
import { useCallback, useState } from 'react';
import CommonActions from '@reducers/common.reducer';
import { StripeProvider, useStripe } from '@stripe/stripe-react-native';
import { Linking, Text, View, Image } from 'react-native';
import PushNotificationManager from '@screens/component/PushNotificationManager';
import { useFlipper } from '@react-navigation/devtools';
import notifee from '@notifee/react-native';

SplashScreen.preventAutoHideAsync();

export default function InnerApp() {
  const dispatch = useDispatch();
  const navigationRef = useNavigationContainerRef();
  const { handleURLCallback } = useStripe();
  const [appIsReady, setAppIsReady] = useState(false);
  const common = useSelector(state => state.common);

  useFlipper(navigationRef);

  // Bootstrap sequence function
  async function bootstrap() {
    const initialNotification = await notifee.getInitialNotification();

    if (initialNotification) {
      console.log(
        'Notification caused application to open',
        initialNotification.notification,
      );
      console.log(
        'Press action used to open the app',
        initialNotification.pressAction,
      );
    }
  }

  const handleDeepLink = useCallback(
    async (url: string | null, params: any | undefined): Promise<void> => {
      console.log('handleDeepLink >> ', url, params);
      if (url) {
        console.log('handleDeepLink > ', url);

        const stripeHandled = await handleURLCallback(url);
        console.log('handleDeepLink > stripeHandled', stripeHandled);
        if (stripeHandled) {
          // This was a Stripe URL - you can return or add extra handling here as you see fit
          //     let values = parseUrl(ev.url);
          //     if (values.transactionId) {
          //       this.props.getPaymentStatus(values.transactionId);
          //     }
        } else {
          // This was NOT a Stripe URL – handle as you normally would
        }
      }
    },
    [handleURLCallback],
  );

  useEffect(() => {
    const getUrlAsync = async (): Promise<void> => {
      const initialUrl = await Linking.getInitialURL();
      handleDeepLink(initialUrl, null);
    };

    getUrlAsync();

    const deepLinkListener = Linking.addEventListener(
      'url',
      (event: { url: string }) => {
        handleDeepLink(event.url, null);
      },
    );

    return () => deepLinkListener.remove();
  }, [handleDeepLink]);

  // TODO import and use setCrashlyticsAttributes();

  // const onRegister = async (token: any) => {
  //   console.log('token onRegister', token);
  //   await AsyncStorage.setItem('deviceToken', token.token);
  // };
  // const onNotification = (notification: any) => {
  //   console.log('notification.data ========', notification);

  //   if (!notification.userInteraction) {
  //     console.log('USER_INTERACTION skip navigation');
  //     return;
  //   }
  //   if (notification.data) {
  //     const dataPayload = notification.data;
  //     let path = null;
  //     if (dataPayload.path) {
  //       path = dataPayload.path;
  //     }
  //     console.log('notification dataPayload.data.path', dataPayload.data?.path);
  //     if (dataPayload.data?.path) {
  //       path = dataPayload.data.path;
  //     }
  //     console.log(
  //       'notification notification.foreground',
  //       notification.foreground,
  //     );
  //     console.log(
  //       'notification notification.userInteraction',
  //       notification.userInteraction,
  //     );
  //     if (!notification.foreground) {
  //       path = dataPayload.path;
  //     }
  //     console.log(
  //       'notification dataPayload.data.data.path',
  //       dataPayload.data?.data?.path,
  //     );
  //     if (path == null && dataPayload.data?.data?.path) {
  //       path = dataPayload.data.data.path;
  //     }

  //     if (path !== undefined && path !== null) {
  //       // console.log(
  //       //   'notification path',
  //       //   path,
  //       //   'ismounted',
  //       //   NavigationService.isMounted(),
  //       // );

  //       const { basePath, params } = getParamsObject(path);

  //       // TODO: RE ENABLE THIS
  //       console.log('notification path', basePath, params);
  //       console.log('notificationReg', navigationRef);

  //       navigationRef.navigate(basePath, { params });
  //       // if (NavigationService.isMounted()) {
  //       //   navigationRef.navigate(basePath, params);
  //       // } else {
  //       //   const h = setTimeout(
  //       //     () => navigationRef.navigate(basePath, params),
  //       //     1500,
  //       //   );
  //       //   console.log('notification with timeout', h);
  //       // }
  //     } else {
  //       console.warn('NO path FOUND dataPayload', dataPayload);
  //     }
  //   } else {
  //     console.warn('NO NOTIFICATION DATA FOUND dataPayload', notification);
  //   }
  // };
  // const notificationService = useNotificationService({
  //   onRegister,
  //   onNotification,
  // });
  // useEffect(() => {
  //   // notifService.current = useNotifService({ onRegister, onNotification });
  //   console.log('notificationService.current', notificationService);
  //   if (notificationService !== undefined) {
  //     notificationService.requestPermissions();
  //   }
  // }, [notificationService]);

  useEffect(() => {
    async function prepare() {
      try {
        return Promise.all([
          Asset.loadAsync([]),
          Font.loadAsync({
            // This is the font that we are using for our tab bar
            ...Icon.Feather.font,
            ...Icon.Entypo.font,
            // ...Icon.AntDesign.font, // NOT USED
            ...Icon.MaterialCommunityIcons.font,
            ...Icon.MaterialIcons.font,
            // We include SpaceMono because we use it in HomeScreen.js. Feel free
            // to remove this if you are not using it in your app
            ...fonts,
          }),
        ]);
      } catch (e) {
        console.warn(e);
      } finally {
        // Tell the application to render
        await SplashScreen.hideAsync();
        setAppIsReady(true);
      }
    }
    bootstrap()
      .then(() => console.log('bootstrapped'))
      .catch(err => console.log('bootstrap error', err));
    prepare();
  }, []);

  // const _loadResourcesAsync = async () => {
  //   return Promise.all([
  //     Asset.loadAsync([]),
  //     Font.loadAsync({
  //       // This is the font that we are using for our tab bar
  //       ...Icon.Feather.font,
  //       ...Icon.Entypo.font,
  //       // ...Icon.AntDesign.font, // NOT USED
  //       ...Icon.MaterialCommunityIcons.font,
  //       ...Icon.MaterialIcons.font,
  //       // We include SpaceMono because we use it in HomeScreen.js. Feel free
  //       // to remove this if you are not using it in your app
  //       ...fonts,
  //     }),
  //   ]);
  // };

  // const _handleFinishLoading = async () => {
  //   setLoadingComplete(true);
  //   await SplashScreen.hideAsync();
  // };

  const _onDone = () => {
    // setLoadingComplete(true);
    dispatch(CommonActions.setPresentationRequest(false));
  };

  const _onSkipSlides = async () => {
    // setShowMainApp(true);
    dispatch(CommonActions.setPresentationRequest(false));
  };

  const _renderItem = ({ item }) => {
    return (
      <View
        style={{ ...styles.container, backgroundColor: item.backgroundColor }}>
        <Text style={item.titleStyle}>{item.title}</Text>
        <Image source={item.image} style={item.imageStyle} />
        <Text style={item.textStyle}>{item.text}</Text>
      </View>
    );
  };

  if (!appIsReady) {
    return null;
  }

  console.log('common.presentation', common.presentation);

  if (common.presentation || common.presentation === null) {
    return (
      <AppIntroSlider
        renderItem={_renderItem}
        data={slides}
        onDone={_onDone}
        onSkip={_onSkipSlides}
        showPrevButton
        showSkipButton
      />
    );
  }
  return (
    <PaperProvider theme={theme}>
      <StripeProvider
        publishableKey={appConfig.stripePubKey}
        urlScheme="selfinvite" // required for 3D Secure and bank redirects
        merchantIdentifier="merchant.eu.selfinvite">
        <PushNotificationManager navigation={navigationRef} />
        <Auth0Provider
          domain={appConfig.auth0Domain || 'none'}
          clientId={appConfig.auth0ClientId || 'none'}>
          <PersistGate loading={null} persistor={persistor}>
            <NavigationContainer
              ref={navigationRef}
              linking={routes.linkingConfig}>
              <routes.Routes />
            </NavigationContainer>
          </PersistGate>
        </Auth0Provider>
      </StripeProvider>
    </PaperProvider>
  );
}
