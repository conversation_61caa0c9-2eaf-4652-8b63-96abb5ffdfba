import 'react-native-gesture-handler';
import React from 'react';
import { AppRegistry } from 'react-native';
// import RNBootSplash from 'react-native-bootsplash';
import messaging from '@react-native-firebase/messaging';
import AppWrapper from 'AppWrapper.tsx';
import { parsePushNotification } from './constants';

// Handle background messages using setBackgroundMessageHandler
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('Message handled in the background!', remoteMessage);
  const data = await parsePushNotification(remoteMessage);
  console.log('Message data', data);
});

// notifee.onBackgroundEvent(async ({ type, detail }) => {
//   console.log('notifee onBackgroundEvent!', type, detail);
//   const { notification, pressAction } = detail;

//   // Check if the user pressed the "Mark as read" action
//   if (type === EventType.ACTION_PRESS && pressAction.id === 'mark-as-read') {
//     // Update external API
//     await fetch(`https://my-api.com/chat/${notification.data.chatId}/read`, {
//       method: 'POST',
//     });

//     // Remove the notification
//     await notifee.cancelNotification(notification.id);
//   }
// });

function HeadlessCheck({ isHeadless }) {
  console.log('isHeadless', isHeadless);
  if (isHeadless) {
    // App has been launched in the background by iOS, ignore
    return null;
  }
  //   hideSplashScreen={RNBootSplash.hide} />;

  // Render the app component on foreground launch
  return <AppWrapper />;
}

AppRegistry.registerComponent('selfinvite', () => HeadlessCheck);
export default AppWrapper;
