module.exports = function (api) {
  api.cache(true);
  return {
    presets: [
      'babel-preset-expo',
      // '@babel/preset-env',
      // 'module:@react-native/babel-preset',
      // '@babel/preset-react',
      // '@babel/preset-flow',
    ],
    plugins: [
      ['@babel/plugin-transform-modules-commonjs'],
      [
        'module-resolver',
        {
          root: ['./'],
          extensions: [
            '.ios.ts',
            '.android.ts',
            '.ts',
            '.ios.tsx',
            '.android.tsx',
            '.tsx',
            '.jsx',
            '.js',
            '.json',
          ],
          alias: {
            '@helpers': './helpers',
            '@config': './config',
            '@screens': './screens',
            '@routes': './routes',
            '@services': './services',
            '@components': './components',
            '@constants': './constants',
            '@actions': './_actions',
            '@reducers': './_reducers',
            '@hooks': './hooks',
            '@sagas': './_sagas',
            '@assets': './assets',
            '@translations': './translations',
          },
        },
      ],
      ['@babel/plugin-transform-private-property-in-object', { 'loose': true }],
      'react-native-reanimated/plugin',
    ],
  };
};
