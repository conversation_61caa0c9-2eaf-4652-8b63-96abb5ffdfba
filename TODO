react native notification alternatives:
- https://github.com/wix/react-native-notifications
- https://notifee.app/
- https://www.npmjs.com/package/react-native-push-notification


- verify deeplinking IOS
- fix add/clean filter in search
- verify review/feedback plugin

# DONE
- refund button
- refund button sends system message
- verify open refund timing windows
- payment with 3d code needs deeplinking
- deep linking
- update event
  - already passed, possible? if yes, the state should be changed as well (lat lon also),
    better include a clone button? YES so eokv attached won't be a problem
- refund confirmed complain
- cancel paid offer 

- carousel for better presentation of the process

setup facebook button login in IOS
https://www.npmjs.com/package/react-native-fbsdk

loadEarlier, onLoadEarlier and infiniteScroll for Gifted chat
https://github.com/FaridSafi/react-native-gifted-chat#props
 

 - status bar https://stackoverflow.com/questions/32750260/is-there-a-way-to-change-the-android-status-bar-color-with-react-native

 

 UseEffect UseReducers needs react-redux 7


 manage message when app is in exit mode


React native bottom dialog
https://www.npmjs.com/package/react-native-raw-bottom-sheet


to build ios APP: https://stackoverflow.com/questions/42110496/how-to-build-ipa-application-for-react-native-ios/42130914#42130914


# to create app icons
https://appicon.co/
https://makeappicon.com/



# fastlane commands
fastlane android release --verbose --env prod


if android network doesn't work, configure dns to public one like ******* in network and /etc/resolv.conf


# issue resolving android expo package like expo-constants and expo-screen, solved passing to expo42

# issue with prod profile the application can;t connect to internet

=> selfinvite.eu certificate expired


--------------------------------------------------------------------------------------------------
Failed to install the requested application
Domain: NSPOSIXErrorDomain
Code: 22
Failure Reason: The application's Info.plist does not contain a valid CFBundleVersion.
Recovery Suggestion: Ensure your bundle contains a valid CFBundleVersion.
User Info: {
    bundleURL = "file:///Users/<USER>/Library/Developer/Xcode/DerivedData/Selfinvite-cmxiehnksrmjqffzmgrpiizxafzz/Build/Products/Debug-iphonesimulator/autoinvito.app/";
}
--


System Information

macOS Version 11.2.3 (Build 20D91)
Xcode 12.5.1 (18212) (Build 12E507)
Timestamp: 2021-11-15T21:20:39+01:00


react-native-config ios bug
=> missing script preBuild to copy env file



Guest open Refund in event state 4, HOST does not receive notification

quando il guest invia il feedback, l'host vede 'unknown' e rimborso accettato ????


Host denied the refund: no push

> Feedback & review with category: hospitality, food, ambient

facebook config: prev Class Name:
 host.exp.exponent.MainActivity


 - use google geocode api

 - ios deeplink from share event
 
 - max text line check, see Review.js
 https://callstack.github.io/react-native-paper/text-input.html
 https://github.com/callstack/react-native-paper/pull/1213/files#diff-cb6d7c0e5604fc8879e449d032b0ed3ab579b2fefd2fbd14f31b0c203e8c9aac



 - add link to location map when event offer is paid and open/closing/close
  - button above the map (eventDetails)
  - button in the event offer list
 - add option to add event in the calendar with details



 2 nov 2023
 - remove apple fb auth reference library


 17 Jun 24
 - verified google pay

DONE:
- verify duplicate tags on update/get event detail

TODO:
- verify refund
- verify payout
- add location filter on algolia
- improve push notification appearence
- add system message in chat
- deselect menu drawer selection when message drawer is clicked

- user profile feedback show the number of feedback sent, not received
- going to profile screen , load stripe kyc url (always)
- public label on event, too much (remove it)

- when event is updated, notify all the subscribers
- notification on: 
  - kyc confirmed
  - payout emit
  - refund emit


- stripe kyc custom api manage https://docs.stripe.com/connect/custom/onboarding#EU--additional-information-on-the-individual

