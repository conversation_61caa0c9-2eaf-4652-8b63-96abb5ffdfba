{
  "extends": "@react-native/typescript-config/tsconfig.json",
  "compilerOptions": {
    /* Basic Options */
    "lib": ["ES2015"],
    "jsx": "react",
    "incremental": true,
    "importHelpers": true,
    "sourceMap": true,
    "baseUrl": "./",
    "typeRoots": ["node_modules/@types", "@types"],
    "paths": {
      "@helpers/*": ["helpers/*"],
      "@config/*": ["config/*"],
      "@screens/*": ["screens/*"],
      "@routes/*": ["routes/*"],
      "@services/*": ["services/*"],
      "@components/*": ["components/*"],
      "@constants/*": ["constants/*"],
      "@actions/*": ["_actions/*"],
      "@reducers/*": ["_reducers/*"],
      "@hooks/*": ["hooks/*"],
      "@sagas/*": ["sagas/*"],
      "@assets/*": ["assets/*"],
      "@translations/*": ["translations/*"]
    }
  }
}
