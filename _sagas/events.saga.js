import { call, put, takeLatest } from 'redux-saga/effects';
import { callApi } from './call-api.saga';
import EventActions, { EventTypes } from '@reducers/events.reducer';
import { agent } from '@services';
import CommonActions from '@reducers/common.reducer';
import SearchActions from '@reducers/search.reducer';

//############################################
// Saga registration
//############################################

export function* getEventOfferSaga() {
  yield takeLatest(EventTypes.EVENT_REQUEST, getEventRequest);
}
export function* getEventEditRequestSaga() {
  yield takeLatest(EventTypes.EVENT_EDIT_REQUEST, getEventEditRequest);
}
export function* getEventOffersSaga() {
  yield takeLatest(EventTypes.EVENT_ALL_REQUEST, getEventAllRequest);
}
export function* updateEventOfferSaga() {
  yield takeLatest(EventTypes.EVENT_UPDATE_REQUEST, updateEventRequest);
}
export function* deleteEventOfferSaga() {
  yield takeLatest(EventTypes.EVENT_DELETE_REQUEST, deleteEventRequest);
}

//############################################
// Sagas
//############################################

function* getEventEditRequest(action) {
  const { eventId } = action;
  const apiCall = call(agent.getEventEditById, eventId);
  const response = yield call(callApi, apiCall);

  console.log('getEventEditRequest', response);
  // success?
  if (response.ok) {
    response.data = mapDateFields(response.data);
    yield put(EventActions.eventEditSuccess(response.data));
  } else {
    yield put(EventActions.eventEditFailure(response.data));
  }
}

function* getEventRequest(action) {
  const { eventId } = action;
  // make the call to the api
  const apiCall = call(agent.getEventOffer, eventId);
  const response = yield call(callApi, apiCall);

  console.log('getEventRequest response ==> ok', response.ok);
  // success?
  if (response.ok) {
    response.data = mapDateFields(response.data);
    console.log('getEventRequest response ==> ', response.data);
    yield put(EventActions.eventSuccess(response.data));
  } else {
    yield put(EventActions.eventFailure(response.data));
  }
}

function* getEventAllRequest(action) {
  // console.log('getEventAllRequest action', action)
  const { options } = action;
  // make the call to the api
  const apiCall = call(agent.getEventOffers, options ? options : '');
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(EventActions.eventAllSuccess(response.data));
    yield put(CommonActions.setLoadUserEventsRequest(false));
  } else {
    yield put(EventActions.eventAllFailure(response.data));
  }
}

function* updateEventRequest(action) {
  const { event } = action;
  // make the call to the api
  const idIsNotNull = !!event.id;
  console.log('event.id', event.id, 'idIsNotNull', idIsNotNull);
  const apiCall = call(
    idIsNotNull ? agent.updateEventOffer : agent.createEventOffer,
    event,
  );
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    response.data = mapDateFields(response.data);
    yield put(EventActions.eventUpdateSuccess(response.data));
    yield put(
      SearchActions.eventSearchRequest({
        filters: {},
        initialFilters: {},
        page: 0,
      }),
    );
  } else {
    yield put(EventActions.eventUpdateFailure(response.data));
  }
}

function* deleteEventRequest(action) {
  const { eventId } = action;
  // make the call to the api
  const apiCall = call(agent.deleteEventOffer, eventId);
  const response = yield call(callApi, apiCall);

  // console.log('deleteEventRequest action', response)
  // success?
  if (response.ok && response.status === 200) {
    yield put(EventActions.eventDeleteSuccess());
  } else {
    yield put(
      EventActions.eventDeleteFailure(
        response.status === 226
          ? '__event_is_used'
          : response.data && response.data.detail,
      ),
    );
  }
}
function mapDateFields(data) {
  if (data.date) {
    data.date = new Date(data.date).toISOString();
  }
  return data;
}
