import { agent } from '@services';
import { call, put, takeLatest } from 'redux-saga/effects';
import { callApi } from '@sagas/call-api.saga';
import MessageActions, { MessageTypes } from '@reducers/message.reducer';

//############################################
// Saga registration
//############################################

export function* messageRequestSaga() {
  yield takeLatest(MessageTypes.MESSAGE_REQUEST, messageRequest);
}

export function* messageAllRequestSaga() {
  yield takeLatest(MessageTypes.MESSAGE_ALL_REQUEST, messageAllRequest);
}

export function* createUpdateMessageRequestSaga() {
  yield takeLatest(MessageTypes.MESSAGE_UPDATE_REQUEST, updateMessage);
}

export function* getMessagesByThreadRequestSaga() {
  yield takeLatest(MessageTypes.MESSAGE_BY_THREAD_REQUEST, getMessagesByThread);
}
export function* deleteMessageRequestSaga() {
  yield takeLatest(MessageTypes.MESSAGE_DELETE_REQUEST, deleteMessage);
}
export function* rearRequestSaga() {
  yield takeLatest(MessageTypes.MESSAGE_READ_REQUEST, readRequest);
}

export function* appendUnreadMessageDrawerSaga() {
  yield takeLatest(
    MessageTypes.APPEND_UNREAD_MESSAGE_DRAWER,
    appendUnreadMessageDrawer,
  );
}

//############################################
// Sagas
//############################################

function* messageRequest(action) {
  const { messageId } = action;

  // const apiCall = call(agent.getMessage, messageId)
  // const response = yield call(callApi, apiCall)
  const response = yield call(agent.getMessage, messageId);

  if (response.ok) {
    response.data = mapDateFields(response.data);
    yield put(MessageActions.messageSuccess(response.data));
  } else {
    yield put(MessageActions.messageFailure(response.data));
  }
}

function* messageAllRequest() {
  const apiCall = call(agent.getMessages, '');
  const response = yield call(callApi, apiCall);

  // Remove the flag for unread messages
  yield put(MessageActions.appendUnreadMessageDrawerSuccess());

  if (response.ok) {
    response.data = mapDateFields(response.data);
    yield put(MessageActions.messageAllSuccess(response.data));
  } else {
    yield put(MessageActions.messageAllFailure(response.data));
  }
}

/**
 * to get user info regarding an event
 * @param {string} userInfoId
 */
function* getMessagesByThread(action) {
  const { threadId } = action;
  // make the call to the api
  const apiCall = call(agent.getMessagesByThread, threadId);
  const response = yield call(callApi, apiCall);

  // const response = yield call(agent.getMessagesByThread, threadId)
  // success?
  if (response.ok) {
    response.data = mapDateFields(response.data);
    yield put(MessageActions.messageByThreadSuccess(response.data));
  } else {
    yield put(MessageActions.messageByThreadFailure(response.data));
  }
}

function* updateMessage(action) {
  console.log('updateMessage action ', action);
  const { message } = action;
  // make the call to the api
  // const idIsNotNull = false// !!message.id
  // const apiCall = call(idIsNotNull ? agent.updateMessage : agent.createMessage, message)
  // const response = yield call(callApi, apiCall)

  const response = yield call(agent.createMessage, message);

  response.data = mapDateFields(response.data);
  // success?
  if (response.ok) {
    yield put(MessageActions.messageUpdateSuccess(response.data));
  } else {
    yield put(MessageActions.messageUpdateFailure(response.data));
  }
}

function* deleteMessage(action) {
  const { messageId } = action;
  // make the call to the api
  const apiCall = call(agent.deleteMessage, messageId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.status === 200) {
    response.data = mapDateFields(response.data);
    yield put(MessageActions.messageDeleteSuccess(response.data));
  } else {
    yield put(MessageActions.messageDeleteFailure(response.data));
  }
}

function* appendUnreadMessageDrawer(action) {
  console.log('appendUnreadMessageDrawer - action', action);

  put(MessageActions.appendUnreadMessageDrawerSuccess());
  return true;
}

function* readRequest(action) {
  const { threadId } = action;
  // make the call to the api
  const apiCall = call(agent.markMessageRead, threadId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.status === 200) {
    yield put(MessageActions.messageReadSuccess());
  } else {
    yield put(MessageActions.messageReadFailure(response.data));
  }
}

//############################################
// helpers
//############################################

function mapDateFields(data) {
  if (data.dateTx) {
    data.dateTx = new Date(data.dateTx).toISOString();
  }
  if (data.dateRead) {
    data.dateRead = new Date(data.dateRead).toISOString();
  }
  return data;
}
