import * as commonSagas from './common.sagas';
import * as accountSagas from './account.sagas';
import * as searchSaga from './search.saga';
import * as eventsSaga from './events.saga';
import * as messageSaga from './message.saga';
import * as feedbacksSaga from './feedbacks.saga';
import * as paymentSaga from './payment.sagas';
import { fork, all } from 'redux-saga/effects';
import * as userSaga from './user.saga';
import * as eventkvSaga from './eventKv.saga';

export default function* rootsaga() {
  try {
    yield all(
      [
        ...Object.values(commonSagas),
        ...Object.values(accountSagas),
        ...Object.values(searchSaga),
        ...Object.values(eventsSaga),
        ...Object.values(messageSaga),
        ...Object.values(feedbacksSaga),
        ...Object.values(userSaga),
        ...Object.values(eventkvSaga),
        ...Object.values(paymentSaga),
      ].map(fork),
    );
  } catch (e) {
    console.error('rootsaga error', e);
  }
}
