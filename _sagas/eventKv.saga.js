import { call, put, takeLatest } from 'redux-saga/effects';
import { callApi } from './call-api.saga';
import EventKVActions, { EventKVTypes } from '@reducers/eventKv.reducer';
import { agent } from '@services';

//############################################
// Saga registration
//############################################

export function* getEventKVAllByIdRequestSaga() {
  yield takeLatest(
    EventKVTypes.EVENT_KV_ALL_BY_ID_REQUEST,
    getEventKVAllByIdRequest,
  );
}
export function* getEventKVSRxRequestSaga() {
  yield takeLatest(EventKVTypes.EVENT_KV_ALL_RX_REQUEST, getEventKVRxRequest);
}
export function* getEventKVSTxRequestSaga() {
  yield takeLatest(EventKVTypes.EVENT_KV_ALL_TX_REQUEST, getEventKVTxRequest);
}
export function* updateEventKVSaga() {
  yield takeLatest(EventKVTypes.EVENT_KV_UPDATE_REQUEST, updateEventKV);
}
export function* reportEventKVSaga() {
  yield takeLatest(EventKVTypes.EVENT_KV_REPORT_REQUEST, reportEventKV);
}
export function* deleteEventKVSaga() {
  yield takeLatest(EventKVTypes.EVENT_KV_DELETE_REQUEST, deleteEventKV);
}

//############################################
// Sagas
//############################################
function* getEventKVAllByIdRequest(action) {
  const { eventId } = action;
  // make the call to the api
  const apiCall = call(agent.getEventKVSById, eventId);
  const response = yield call(callApi, apiCall);

  response.data = response.data.map(mapDateFields);
  if (response.ok) {
    yield put(EventKVActions.eventKvAllByIdSuccess(response.data));
  } else {
    yield put(EventKVActions.eventKvAllByIdFailure(response.data));
  }
}

function* getEventKVRxRequest(action) {
  const { options } = action;
  // make the call to the api
  const apiCall = call(agent.getEventKVSRxRequest, options);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(EventKVActions.eventKvAllRxSuccess(response.data));
  } else {
    yield put(EventKVActions.eventKvAllRxFailure(response.data));
  }
}

function* getEventKVTxRequest(action) {
  const { page } = action;
  // make the call to the api
  const apiCall = call(agent.getEventKVSTxRequest, { page: page ?? 0 });
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(EventKVActions.eventKvAllTxSuccess(response.data));
  } else {
    yield put(EventKVActions.eventKvAllTxFailure(response.data));
  }
}

function* reportEventKV(action) {
  const { eventKv } = action;
  const apiCall = call(agent.reportEventOffer, eventKv);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(EventKVActions.eventKvReportSuccess(response.data));
  } else {
    yield put(EventKVActions.eventKvReportFailure(response.data));
  }
}

function* updateEventKV(action) {
  console.log('updateEventKV', action);
  const { eventKv, after } = action.eventKv;
  // make the call to the api
  const apiCall = call(
    eventKv.id !== undefined
      ? eventKv.refundChangeState
        ? agent.updateEventRefund
        : agent.updateEventKV
      : agent.createEventKV,
    eventKv,
  );
  const response = yield call(callApi, apiCall);

  response.data = mapDateFields(response.data);
  if (response.ok) {
    yield put(EventKVActions.eventKvUpdateSuccess(response.data));
    //reload
    if (after === 'tx') {
      yield put(EventKVActions.eventKvAllTxRequest());
    } else if (after === 'rx') {
      yield put(EventKVActions.eventKvAllRxRequest());
    }
  } else {
    yield put(EventKVActions.eventKvUpdateFailure(response.data));
  }
}

function* deleteEventKV(action) {
  const { eventKvId, after } = action.eventKvId;
  // make the call to the api
  const apiCall = call(agent.deleteEventKV, eventKvId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(EventKVActions.eventKvDeleteSuccess());
    //reload
    if (after === 'tx') {
      yield put(EventKVActions.eventKvAllTxRequest());
    } else if (after === 'rx') {
      yield put(EventKVActions.eventKvAllRxRequest());
    }
  } else {
    yield put(EventKVActions.eventKvDeleteFailure(response.data));
  }
}

function mapDateFields(data) {
  if (data.date) {
    data.date = new Date(data.date);
  }
  return data;
}
