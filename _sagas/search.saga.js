import { call, put, takeLatest } from 'redux-saga/effects';
import { callApi } from './call-api.saga';
import SearchActions, { SearchTypes } from '@reducers/search.reducer';
import { agent } from '@services';

const getAlgoliaQuery = (initialFilters, filters, page) => {
  let query = '';
  let composedFilter = [];
  const numericFilters = [];

  console.log('initialFilters>>', initialFilters);
  console.log('filters>>', filters);

  if (filters?.name) {
    query += `${filters.name}`;
  }

  console.log('Facet filters?>>', filters);
  // Facet Filters
  if (filters?.location?.length > 0) {
    composedFilter.push(
      `( ${filters.location.map(k => `typeLs:${k}`).join(' OR ')} )`,
    );
  }
  if (filters?.kitchen?.length > 0) {
    composedFilter.push(
      `( ${filters.kitchen.map(k => `typeKs:${k}`).join(' OR ')} )`,
    );
  }
  if (filters?.drink?.length > 0) {
    composedFilter.push(
      `( ${filters.drink.map(k => `typeBs:${k}`).join(' OR ')} )`,
    );
  }
  if (filters?.eventTypes?.length > 0) {
    composedFilter.push(
      `( ${filters.eventTypes.map(k => `typeEs:${k}`).join(' OR ')} )`,
    );
  }
  if (filters?.intolerances?.length > 0) {
    composedFilter.push(
      `( ${filters.intolerances.map(k => `intolerances:${k}`).join(' OR ')} )`,
    );
  }

  // Numeric Filters
  if (filters?.adults > 0) {
    numericFilters.push(`maxPartecipants>=${filters.adults}`);
  }

  if (
    filters?.price?.length > 0 &&
    filters.price.length === initialFilters?.price?.length &&
    (filters.price[0] !== initialFilters.price[0] ||
      filters.price[1] !== initialFilters.price[1])
  ) {
    numericFilters.push(`pricepp>=${filters.price[0]}`);
    numericFilters.push(`pricepp<=${filters.price[1]}`);
  }

  if (
    filters?.duration?.length > 0 &&
    filters.duration.length === initialFilters?.duration?.length &&
    (filters.duration[0] !== initialFilters.duration[0] ||
      filters.duration[1] !== initialFilters.duration[1])
  ) {
    numericFilters.push(`duration>=${filters.duration[0]}`);
    numericFilters.push(`duration<=${filters.duration[1]}`);
  }
  if (
    (filters?.selectedStartDate &&
      filters?.selectedStartDate !== initialFilters?.selectedStartDate) ||
    (filters?.selectedEndDate &&
      filters?.selectedEndDate !== initialFilters?.selectedEndDate)
  ) {
    numericFilters.push(
      `dateTimeStamp>=${Date.parse(filters.selectedStartDate) / 1000}`,
    );
    numericFilters.push(
      `dateTimeStamp<=${Date.parse(filters.selectedEndDate) / 1000}`,
    );
  }
  console.log('numericFilters>>', numericFilters);
  console.log('composedFilter>>', composedFilter.join(' AND '));

  return {
    filters: composedFilter.join(' AND '),
    // facetFilters,
    numericFilters,
    params: `query=${query}&hitsPerPage=20&page=${page}`,
  };
};

//############################################
// Saga registration
//############################################

export function* searchEventOffersSaga() {
  yield takeLatest(SearchTypes.EVENT_SEARCH_REQUEST, searchEventRequest);
}

export function* searchEventOffersTagSaga() {
  yield takeLatest(SearchTypes.EVENT_TAG_SEARCH_REQUEST, searchEventTagRequest);
}

function* searchEventRequest(action) {
  console.group('Filters');
  console.log('action is >>>', action);
  const {
    data: { initialFilters, filters, page = 0 },
  } = action;
  const paramsAlgolia = getAlgoliaQuery(initialFilters, filters, page);

  // make the call to the api
  console.log('algoliaRequest', paramsAlgolia);
  const algoliaApiCall = call(
    agent.searchAlgoliaEventOfferNative,
    paramsAlgolia,
  );
  const { ok, data } = yield call(callApi, algoliaApiCall);
  console.log('algoliaResponse', ok, data);
  console.groupEnd();

  // response.data = response.data.map(mapDateFields);
  if (ok) {
    const events = data.hits.map(mapDateFields);
    yield put(
      SearchActions.eventSearchSuccess({
        events,
        page: data.page,
        total: data.nbHits,
        last: data.nbPages,
      }),
    );
  } else {
    yield put(
      SearchActions.eventSearchFailure({
        events: [],
        page: 0,
        total: 1,
      }),
    );
  }
}

function* searchEventTagRequest(action) {
  const { tag } = action;
  console.log('searchEventTagRequest', action);
  const apiCall = call(
    agent.getEventsByTag,
    tag.replace('@', '').replace('#', ''),
  );

  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(SearchActions.eventTagSearchSuccess(response.data));
  } else {
    yield put(SearchActions.eventTagSearchFailure(response.data));
  }
}

function mapDateFields(data) {
  if (data.date) {
    data.date = new Date(data.date).toISOString();
  }
  if (data.createdDate) {
    data.createdDate = new Date(data.createdDate).toISOString();
  }
  if (data.lastModifiedDate) {
    data.lastModifiedDate = new Date(data.lastModifiedDate).toISOString();
  }
  return data;
}
