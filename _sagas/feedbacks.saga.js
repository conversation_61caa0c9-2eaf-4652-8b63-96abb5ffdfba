import { call, put, takeLatest } from 'redux-saga/effects';
import { callApi } from '@sagas/call-api.saga';
import FeedbackActions, { FeedbackTypes } from '@reducers/feedbacks.reducer';
import { agent } from '@services';
import CommonActions from '@reducers/common.reducer';

export function* getFeedbackSaga() {
  yield takeLatest(FeedbackTypes.FEEDBACK_REQUEST, getFeedback);
}
export function* getReviewsSaga() {
  yield takeLatest(FeedbackTypes.REVIEWS_ALL_REQUEST, getUserReviews);
}
export function* getFeedbacksSaga() {
  yield takeLatest(FeedbackTypes.FEEDBACKS_ALL_REQUEST, getUserFeedbacks);
}
export function* updateFeedbackSaga() {
  yield takeLatest(FeedbackTypes.FEEDBACK_UPDATE_REQUEST, updateFeedback);
}
export function* updateReviewSaga() {
  yield takeLatest(FeedbackTypes.REVIEW_UPDATE_REQUEST, updateReview);
}
export function* deleteFeebackSaga() {
  yield takeLatest(FeedbackTypes.FEEDBACK_DELETE_REQUEST, deleteFeedback);
}
export function* feedbackStatsSaga() {
  yield takeLatest(FeedbackTypes.FEEDBACK_STATS_REQUEST, feedbackStats);
}
export function* reviewStatsSaga() {
  yield takeLatest(FeedbackTypes.REVIEW_STATS_REQUEST, reviewStats);
}

function* reviewStats(action) {
  const { userId } = action;
  // make the call to the api
  const apiCall = call(agent.getReviewsStas, userId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(FeedbackActions.reviewStatsSuccess(response.data));
  } else {
    yield put(FeedbackActions.reviewStatsFailure(response.data));
  }
}

function* feedbackStats(action) {
  const { userId } = action;
  // make the call to the api
  const apiCall = call(agent.getFeedbacksStas, userId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(FeedbackActions.feedbackStatsSuccess(response.data));
  } else {
    yield put(FeedbackActions.feedbackStatsFailure(response.data));
  }
}

function* getFeedback(action) {
  const { feedbackId } = action;
  // make the call to the api
  const apiCall = call(agent.getFeedback, feedbackId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    response.data = mapDateFields(response.data);
    yield put(FeedbackActions.feedbackSuccess(response.data));
  } else {
    yield put(FeedbackActions.feedbackFailure(response.data));
  }
}

function* getUserReviews(action) {
  const { userId } = action;
  // make the call to the api
  const apiCall = call(agent.getUserReviews, userId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(FeedbackActions.reviewsAllSuccess(response.data));
    yield put(CommonActions.setLoadReviewsRequest(false));
  } else {
    yield put(FeedbackActions.reviewsAllFailure(response.data));
  }
}

function* getUserFeedbacks(action) {
  const { userId } = action;
  // make the call to the api
  const apiCall = call(agent.getUserFeedbacks, userId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(FeedbackActions.feedbacksAllSuccess(response.data));
    yield put(CommonActions.setLoadFeedbacksRequest(false));
  } else {
    yield put(FeedbackActions.feedbacksAllFailure(response.data));
  }
}

function* updateReview(action) {
  const { review } = action;
  console.log('updateReview....', review);
  // make the call to the api
  const idIsNotNull = !!review.id;
  const apiCall = call(
    idIsNotNull ? agent.updateReview : agent.createReview,
    review,
  );
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    response.data = mapDateFields(response.data);
    yield put(FeedbackActions.reviewUpdateSuccess(response.data));
  } else {
    yield put(FeedbackActions.reviewUpdateFailure(response.data));
  }
}

function* updateFeedback(action) {
  const { feedback } = action;
  console.log('updateFeedback....', feedback);
  // make the call to the api
  const idIsNotNull = !!feedback.id;
  const apiCall = call(
    idIsNotNull ? agent.updateFeedback : agent.createFeedback,
    feedback,
  );
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    response.data = mapDateFields(response.data);
    yield put(FeedbackActions.feedbackUpdateSuccess(response.data));
  } else {
    yield put(FeedbackActions.feedbackUpdateFailure(response.data));
  }
}

function* deleteFeedback(action) {
  const { feedbackId } = action;
  // make the call to the api
  const apiCall = call(agent.deleteFeedback, feedbackId);
  const response = yield call(callApi, apiCall);

  // success?
  if (response.ok) {
    yield put(FeedbackActions.feedbackDeleteSuccess());
  } else {
    yield put(FeedbackActions.feedbackDeleteFailure(response.data));
  }
}
function mapDateFields(data) {
  if (data.date) {
    data.date = new Date(data.date);
  }
  return data;
}
