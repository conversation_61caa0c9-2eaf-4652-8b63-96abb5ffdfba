import { call, put, takeLatest } from 'redux-saga/effects';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { agent } from '@services';
import { callApi } from './call-api.saga';
import AccountActions, { AccountTypes } from '@reducers/account.reducer';
import LoginActions from '@reducers/login.reducer';

export function* deviceTokenSaga() {
  yield takeLatest(AccountTypes.DEVICE_TOKEN_REQUEST, deviceTokenRequest);
}

export function* getUserDataSaga() {
  yield takeLatest(AccountTypes.ACCOUNT_REQUEST, getUserDataRequest);
}

export function* getAccountPreferencesSaga() {
  yield takeLatest(
    AccountTypes.ACCOUNT_PREFERENCES_REQUEST,
    getAccountPreferencesRequest,
  );
}

export function* savePhoneNumberSaga() {
  yield takeLatest(AccountTypes.PHONE_REQUEST, savePhoneNumber);
}
export function* verifyOtpCodeSaga() {
  yield takeLatest(AccountTypes.OTP_REQUEST, verifyOtpCode);
}

export function* updateAccountRequestSaga() {
  yield takeLatest(AccountTypes.ACCOUNT_UPDATE_REQUEST, updateAccountRequest);
}
export function* accountPreferencesUpdateRequestSaga() {
  yield takeLatest(
    AccountTypes.ACCOUNT_PREFERENCES_UPDATE_REQUEST,
    accountPreferencesUpdateRequest,
  );
}
export function* addAccountPreferencesRequestSaga() {
  yield takeLatest(
    AccountTypes.ADD_ACCOUNT_PREFERENCES_REQUEST,
    addAccountPreferencesRequest,
  );
}

export function* usersBlockRequestSaga() {
  yield takeLatest(AccountTypes.USERS_BLOCK_REQUEST, usersBlockRequest);
}
export function* deleteAccountRequestSaga() {
  yield takeLatest(AccountTypes.DELETE_ACCOUNT_REQUEST, deleteAccountRequest);
}

function* deleteAccountRequest(action) {
  yield call(agent.deleteAccount);

  // console.log('deleteAccountRequest response', response);
  yield put(LoginActions.logoutSuccess());
  yield put(AccountActions.accountReset());
}

function* addAccountPreferencesRequest(action) {
  // console.log('addAccountPreferences', action);

  const apiCall = call(agent.addAccountPreferences, action.preferences);
  const response = yield call(callApi, apiCall);

  // console.log('addAccountPreferencesRequest', response);
  if (response.ok) {
    yield put(AccountActions.addAccountPreferencesSuccess(response.data));
    yield put(AccountActions.accountPreferencesRequest());
  } else {
    yield put(
      AccountActions.addAccountPreferencesFailure(
        (response.data && response.data.detail) || 'Bad credentials',
      ),
    );
  }
}

function* accountPreferencesUpdateRequest(action) {
  const apiCall = call(agent.updateAccountPreferences, action.preferences);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(AccountActions.accountPreferencesUpdateSuccess(response.data));
  } else {
    yield put(
      AccountActions.accountPreferencesUpdateFailure(
        (response.data && response.data.detail) || 'Bad credentials',
      ),
    );
  }
}

function* updateAccountRequest(action) {
  try {
    action.account = mapDateFields(action.account);
    const apiCall = call(agent.updateAccount, action.account);
    const response = yield call(callApi, apiCall);

    if (response.ok) {
      yield put(AccountActions.accountSuccess(response.data));
    } else {
      yield put(
        AccountActions.accountFailure(
          (response.data && response.data.detail) || 'Bad credentials',
        ),
      );
    }
  } catch (e) {
    console.error('saveUserDataRequest', e);
  }
}

function* savePhoneNumber(action) {
  // console.log('savePhoneNumber ', action);
  try {
    const apiCall = call(agent.savePhoneNumber, action.number);
    const response = yield call(callApi, apiCall);

    if (response.ok) {
      yield put(AccountActions.phoneSuccess(response.data));
    } else {
      yield put(
        AccountActions.phoneFailure(
          (response.data && response.data.detail) || 'Bad credentials',
        ),
      );
    }
  } catch (e) {
    console.error('savePhoneNumer', e);
  }
}

function* verifyOtpCode(action) {
  // console.log('verifyOtpCode ', action);

  try {
    const apiCall = call(agent.verifyOtp, action.otp);
    const response = yield call(callApi, apiCall);

    if (response.ok) {
      yield put(AccountActions.otpSuccess(response.data));
      yield put(AccountActions.accountRequest());
    } else {
      yield put(
        AccountActions.otpFailure(
          (response.data && response.data.detail) || 'Bad credentials',
        ),
      );
    }
  } catch (e) {
    console.error('verifyOtpCode error here', e);
  }
}

function* usersBlockRequest(action) {
  // console.log('usersBlockRequest action ', action);
  try {
    const apiCall = call(agent.updateUsersBlock, action.users);
    const response = yield call(callApi, apiCall);
    if (response.ok) {
      yield put(AccountActions.usersBlockSuccess(response.data));
      // const userBlocked = response.data;

      // e gli altri filtri
      // const query = packUserBlockedInQuery(null, userBlocked);

      // yield put(SearchActions.eventSearchRequest(query, null, 0));
    } else {
      yield put(
        AccountActions.usersBlockFailure(
          (response.data && response.data.detail) || '__error',
        ),
      );
    }
  } catch (e) {
    console.error('usersBlockRequest error here', e);
  }
}

function* getAccountPreferencesRequest() {
  const response = yield call(agent.getAccountPreferences);

  if (response.ok) {
    yield put(AccountActions.accountPreferencesSuccess(response.data));
  } else {
    yield put(AccountActions.accountPreferencesFailure(response.error));
  }
}

function* deviceTokenRequest(action) {
  let { deviceToken } = action;
  // console.log('deviceTokenRequest deviceToken', deviceToken);
  if (!deviceToken) deviceToken = yield AsyncStorage.getItem('deviceToken');

  const apiCall = call(agent.registerDeviceToken, deviceToken);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(AccountActions.deviceTokenSuccess(response.data));
  } else {
    console.warn('deviceTokenRequest - FAIL');
    yield put(
      AccountActions.deviceTokenFailure(
        (response.data && response.data.detail) || '__device_token_error',
      ),
    );
  }
}

function* getUserDataRequest() {
  const apiCall = call(agent.getAccount);
  const response = yield call(callApi, apiCall);

  // console.log('getUserDataRequest', response.data)
  if (response.ok) {
    yield put(AccountActions.accountSuccess(response.data));
  } else {
    console.log('Account - FAIL');
    yield put(
      AccountActions.accountFailure(
        (response.data && response.data.detail) || 'Failed to get account',
      ),
    );
  }
}

function mapDateFields(data) {
  if (data.dob) {
    data.dob = new Date(data.dob).toISOString();
  }
  return data;
}
