import { call, put, takeLatest } from 'redux-saga/effects';
import { agent } from '../services/index';
import AccountActions from '@reducers/account.reducer';
import LoginActions, { LoginTypes } from '@reducers/login.reducer';
import CommonActions from '@reducers/common.reducer';
import PaymentActions from '@reducers/payment.reducer';
import * as SecureStore from 'expo-secure-store';
import jwt_decode from 'jwt-decode';

export function* logoutSaga() {
  yield takeLatest(LoginTypes.LOGOUT_REQUEST, loggedOutRequest);
}
export function* loginSocialSaga() {
  yield takeLatest(LoginTypes.LOGIN_SOCIAL_REQUEST, registerAndLogin);
}

function* registerAndLogin(action) {
  const { credentials } = action;
  try {
    console.log('decodedIdToken before', credentials.idToken);
    const decodedIdToken = jwt_decode(credentials.idToken);
    console.log(`decodedIdToken after`, decodedIdToken);
    console.log(`credentials.idToken after`, credentials.idToken);

    yield call(SecureStore.setItemAsync, 'token', credentials.idToken);

    let response = yield call(agent.isAccountExist, {
      email: decodedIdToken.email,
      token: credentials.idToken,
    });

    console.log('isExist.data', response.data);
    if (!response.ok || !response.data?.email) {
      const registerLoginPayload = {
        nickname: decodedIdToken.email,
        idToken: credentials.idToken,
      };
      response = yield call(agent.registerSocialLogin, registerLoginPayload);
    }

    if (response.ok) {
      yield put(LoginActions.loginSocialSuccess(response.data));
      yield put(CommonActions.configurationRequest());
      yield put(AccountActions.accountRequest());
      yield put(AccountActions.deviceTokenRequest());
      yield put(AccountActions.accountPreferencesRequest());
      // they will be perfomed again in their screens
      // yield put(EventsActions.eventAllRequest());
      // yield put(MessageActions.messageAllRequest());
      // yield put(EventKVActions.eventKvAllRxRequest());
      // yield put(EventKVActions.eventKvAllTxRequest());
      yield put(PaymentActions.paymentKycStatusRequest());

      yield put({ type: 'RELOGIN_OK' });
    } else {
      //email exist to register fb user || something else
      // console.log('responseRegister ', responseRegister);
      yield put(
        LoginActions.loginSocialFailure(
          (response.data && response.data.message) || '__email_exist',
        ),
      );
    }
  } catch (e) {
    console.log('cazzo error', e);
  }
}

function* loggedOutRequest() {
  yield call(agent.logout);
  yield call(SecureStore.deleteItemAsync, 'token');
  yield put(LoginActions.logoutSuccess());
  yield put(AccountActions.accountReset());
}
