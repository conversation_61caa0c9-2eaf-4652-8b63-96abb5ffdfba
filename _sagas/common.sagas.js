import { call, put, takeLatest } from 'redux-saga/effects';
import { agent } from '@services';
import { callApi } from './call-api.saga';
import CommonActions, { CommonTypes } from '@reducers/common.reducer';
import { Platform } from 'react-native';

export function* fetchingConfigurationSaga() {
  yield takeLatest(CommonTypes.CONFIGURATION_REQUEST, fetchingConfiguration);
}
export function* updateImageRequestSaga() {
  yield takeLatest(CommonTypes.UPDATE_IMAGE_REQUEST, updateImageRequest);
}
export function* updatePresentationSaga() {
  yield takeLatest(CommonTypes.SET_PRESENTATION_REQUEST, updatePresentation);
}
export function* userInfoRequestSaga() {
  yield takeLatest(CommonTypes.USER_INFO_REQUEST, getUserInfoRequest);
}
export function* setUserInfoRequestSaga() {
  yield takeLatest(CommonTypes.SET_USER_INFO_REQUEST, setUserInfoRequest);
}

export function* setLoadFeedbacksRequestSaga() {
  yield takeLatest(
    CommonTypes.SET_LOAD_FEEDBACKS_REQUEST,
    setLoadFeedbacksRequest,
  );
}
export function* setLoadReviewsRequestSaga() {
  yield takeLatest(CommonTypes.SET_LOAD_REVIEWS_REQUEST, setLoadReviewsRequest);
}

function* setLoadFeedbacksRequest(action) {
  const { status } = action;
  put(CommonActions.setLoadFeedbacksRequest(status));
  return true;
}

function* setLoadReviewsRequest(action) {
  const { status } = action;
  put(CommonActions.setLoadReviewsRequest(status));
  return true;
}

function* setUserInfoRequest(action) {
  const { userInfoId } = action;
  put(CommonActions.setUserInfoRequest(userInfoId));
  return true;
}

function* getUserInfoRequest(action) {
  const { userInfoId } = action;
  const apiCall = call(agent.userInfo, userInfoId);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(CommonActions.userInfoSuccess(response.data));
  } else {
    yield put(CommonActions.userInfoFailure(response.data));
  }
}

function* fetchingConfiguration(action) {
  const apiCall = call(agent.getConfiguration);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(CommonActions.configurationSuccess(response.data));
  } else {
    yield put(CommonActions.configurationFailure(response.data));
  }
}

function* updatePresentation(action) {
  const { presentation } = action;
  yield put(CommonActions.setPresentationSuccess(presentation));
}

function* updateImageRequest(action) {
  console.log('updateImageRequest - action', action);
  const { image } = action;
  let formdata = new FormData();
  const file = yield fetch(image.uri);
  const mimeType =
    !file.type || file.type === 'image' ? 'image/png' : file.type;
  formdata.append('file', {
    uri: Platform.OS === 'android' ? file.url : file.url.replace('file://', ''),
    type: mimeType,
    name: mimeType === 'image/png' ? 'image.png' : images.jpeg,
  });

  const apiCall = call(agent.uploadImageProfile, formdata);
  const response = yield call(callApi, apiCall);
  console.log('updateImageRequest response', response);
  if (response.ok) {
    yield put(CommonActions.updateImageSuccess(response.data));
    // call save user data
    // yield put(CommonActions.updateImageSuccess(response.data))
  } else {
    console.log('updateImageRequest - FAIL');
    yield put(
      CommonActions.updateImageFailure(
        (response.data && response.data.detail) || '__device_token_error',
      ),
    );
  }
}
