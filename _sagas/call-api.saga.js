import { put, take } from 'redux-saga/effects';

// import { loginScreen } from '../../navigation/layouts'
// any js module
// any js module
// import NavigationService from '@routes/NavigationService';
// import { DRAWER_LOGIN_ROUTE } from '@routes/route_constants';
// import NavigationService from '@routes/NavigationService';
// this saga is used for showing the LoginScreen when a 401 error is received
// if login is successful, it will reattempt the request
// if login fails, it will return the error
export function* callApi(apiCall) {
  const response = yield apiCall;
  // return response
  if (!isUnauthorized(response)) {
    return response;
  }
  // this triggers your UI to show a login form

  console.error('callApi GOTO LOGIN');
  // TODO: re-enable this
  // NavigationService.navigate(DRAWER_LOGIN_ROUTE); // ==>from , { userName: 'Lucy' });

  const action = yield take(['RELOGIN_OK', 'RELOGIN_ABORT']);

  if (action.type === 'RELOGIN_ABORT') {
    return response;
  }

  // // this re-calls the api with the new authorization
  return yield apiCall;
}

function isUnauthorized(resp) {
  return resp.status === 401; // || resp.status === 403;
}
