import { call, put, takeLatest } from 'redux-saga/effects';
import { callApi } from './call-api.saga';
import PaymentActions, { PaymentTypes } from '@reducers/payment.reducer';
import EventKVActions from '@reducers/eventKv.reducer';
import { agent } from '@services';

//############################################
// Saga registration
//############################################

export function* paymentUpdateRequestSaga() {
  yield takeLatest(PaymentTypes.PAYMENT_UPDATE_REQUEST, updatePayment);
}

export function* getPaymentTokenSaga() {
  yield takeLatest(PaymentTypes.PAYMENT_TOKEN_REQUEST, getPaymentToken);
}

export function* getKycStatusSaga() {
  yield takeLatest(PaymentTypes.PAYMENT_KYC_STATUS_REQUEST, getUserWallet);
}

export function* uploadKycSaga() {
  yield takeLatest(PaymentTypes.PAYMENT_KYC_UPLOAD_REQUEST, uploadKyc);
}

export function* paymentBankRequestSaga() {
  yield takeLatest(PaymentTypes.PAYMENT_BANK_REQUEST, paymentBankRequest);
}
export function* paymentStatusRequestSaga() {
  yield takeLatest(PaymentTypes.PAYMENT_STATUS_REQUEST, paymentStatusRequest);
}

export function* paymentKycManagedFlowRequestSaga() {
  yield takeLatest(
    PaymentTypes.PAYMENT_KYC_MANAGED_FLOW_REQUEST,
    paymentKycManagedRequest,
  );
}

export function* paymentKycManagedFlowCleanupSaga() {
  yield takeLatest(
    PaymentTypes.PAYMENT_KYC_MANAGED_FLOW_CLEANUP,
    cleanupPaymentManagedFlowAction,
  );
}

export function* cleanupPaymentObjectsSaga() {
  yield takeLatest(
    PaymentTypes.CLEANUP_PAYMENT_OBJECTS,
    cleanupPaymentObjectsAction,
  );
}
//############################################
// Sagas
//############################################

function* paymentKycManagedRequest(action) {
  const apiCall = call(agent.initKycManagedFlow);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(PaymentActions.paymentKycManagedFlowSuccess(response.data));
  } else {
    yield put(PaymentActions.paymentKycManagedFlowFailure(response.data));
  }
}

function* cleanupPaymentObjectsAction(action) {
  yield put(PaymentActions.cleanupPaymentObjects());
  return true;
}

function* cleanupPaymentManagedFlowAction(action) {
  yield put(PaymentActions.paymentKycManagedFlowCleanup());
  return true;
}

function* paymentStatusRequest(action) {
  const { transactionId } = action;

  const apiCall = call(agent.getPaymentStatus, transactionId);
  const response = yield call(callApi, apiCall);

  console.log('paymentStatusRequest - response', response);

  if (response.ok) {
    yield put(PaymentActions.paymentStatusSuccess(response.data));
  } else {
    yield put(PaymentActions.paymentStatusFailure(response.data));
  }
}

function* paymentBankRequest(action) {
  const { bankAccount } = action;

  const apiCall = call(agent.updateBankAccount, bankAccount);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(PaymentActions.paymentBankSuccess(response.data));
  } else {
    yield put(PaymentActions.paymentBankFailure(response.data));
  }
}

function* uploadKyc(action) {
  const { documents } = action;

  const apiCall = call(agent.uploadKycDocument, documents);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(PaymentActions.paymentKycUploadSuccess(response.data));
  } else {
    yield put(PaymentActions.paymentKycUploadFailure(response.data));
  }
}

function* getUserWallet(action) {
  const apiCall = call(agent.getUserWallet);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(PaymentActions.paymentKycStatusSuccess(response.data));
  } else {
    yield put(PaymentActions.paymentKycStatusFailure(response.data));
  }
}

function* updatePayment(action) {
  const { payment } = action;

  const apiCall = call(agent.createPaymentMango, payment);
  const response = yield call(callApi, apiCall);

  if (response.ok) {
    yield put(PaymentActions.paymentUpdateSuccess(response.data));
    // update eventKv tx status
    yield put(EventKVActions.eventKvAllTxRequest());
  } else {
    yield put(PaymentActions.paymentUpdateFailure(response.data));
  }
}

function* getPaymentToken(action) {
  const { eventOfferKvId } = action;
  console.log('getPaymentToken', action);
  const apiCall = call(agent.getStripePaymentIntent, eventOfferKvId);
  const response = yield call(callApi, apiCall);
  console.log('response', response.data);
  console.log('response2', response);

  if (response.ok) {
    yield put(PaymentActions.paymentTokenSuccess(response.data));
  } else {
    yield put(PaymentActions.paymentTokenFailure(response.data));
  }
}

function mapDateFields(data) {
  if (data.paymentDate) {
    data.paymentDate = new Date(data.paymentDate);
  }
  return data;
}
