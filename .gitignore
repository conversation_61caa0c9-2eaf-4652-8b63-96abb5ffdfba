# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IJ
#
*.iml
*.hprof
.idea
.gradle
local.properties
android/app/src/main/assets
android/app/src/main/res/raw
android/app/src/main/res/**/*assets_images*
android/app/src/main/res/**/node_modules_*

# node.js
#
node_modules/
npm-debug.log
.yarn/

# BUCK
buck-out/
\.buckd/
android/app/libs
android/keystores/debug.keystore
.vscode
index1.android.js
index1.js
index2.ios.js
.expo

# pod
#
ios/Pods
assets/ic_launcher.png.bak.png
assets/ic_launcher.png.bak2.png
android/app/.settings/org.eclipse.jdt.core.prefs
android/app/.classpath
android/app/.classpath
ios/selfinvite/Supporting/EXBuildConstants.plist.bak
ios/selfinvite.xcodeproj/project.pbxproj
android/.settings/org.eclipse.buildship.core.prefs
ios/selfinvite.xcodeproj/project.pbxproj
ios/Podfile.lock
android/app/src/main/java/host/exp/exponent/generated/DetachBuildConstants.java
android/app/src/main/java/host/exp/exponent/generated/DetachBuildConstants.java

android_eject/
ios_bak/
yarn-error.log
android/app/.settings/org.eclipse.buildship.core.prefs
android/app/src/main/java/host/exp/exponent/generated/DetachBuildConstants.java
android/app/src/main/java/host/exp/exponent/generated/AppConstants.java
ios/selfinvite/Supporting/shell-app.bundle
web-build/register-service-worker.js
ios/selfinvite/Supporting/shell-app.bundle
ios/selfinvite/Supporting/shell-app.bundle
ios/Podfile.lock
ios/selfinvite/Supporting/EXBuildConstants.plist
ios/selfinvite/Supporting/EXBuildConstants.json
android/app/src/main/assets/shell-app-manifest.json
android/app/src/main/assets/shell-app.bundle
android/app/src/main/java/host/exp/exponent/generated/AppConstants.java
android/app/src/main/java/host/exp/exponent/generated/BasePackageList.java
android/app/src/main/java/host/exp/exponent/generated/DetachBuildConstants.java
ios/selfinvite/Supporting/shell-app.bundle
ios/Podfile.lock
ios/Build target si_2020-09-17T19-53-27.txt

# The following contents were automatically generated by expo-cli during eject
# ----------------------------------------------------------------------------

# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifacts
*.jsbundle

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/
# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*
# testing
/coverage

# Expo
.expo/*
web-build/

# @generated expo-cli sync-2ef881133233622bdc507b622808fd28cb52985b
# The following patterns were generated by expo-cli

# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Expo
.expo/*
web-build/

# @end expo-cli


previousApp/
autoinvito.png
ios/selfinvite/Supporting/app.bundle
ios/selfinvite/Supporting/app.manifest
pc-api-4790442714061326313-135-0d06e58e19eb.json
android/fastlane/metadata/
ios/selfinvite/Supporting/app.bundle
ios/selfinvite/Supporting/app.manifest
android/fastlane/README.md
fastlane/metadata/android/it-IT/full_description.txt
fastlane/metadata/android/it-IT/short_description.txt
fastlane/metadata/android/it-IT/title.txt
fastlane/metadata/android/it-IT/video.txt
fastlane/metadata/android/it-IT/images/featureGraphic.jpeg
fastlane/metadata/android/it-IT/images/icon.png
fastlane/metadata/android/it-IT/images/phoneScreenshots/1_it-IT.png
fastlane/metadata/android/it-IT/images/phoneScreenshots/2_it-IT.png
fastlane/metadata/android/it-IT/images/phoneScreenshots/3_it-IT.png
fastlane/metadata/android/it-IT/images/phoneScreenshots/4_it-IT.png
fastlane/metadata/android/it-IT/images/sevenInchScreenshots/1_it-IT.png
fastlane/metadata/android/it-IT/images/sevenInchScreenshots/2_it-IT.png
fastlane/metadata/android/it-IT/images/sevenInchScreenshots/3_it-IT.png
fastlane/metadata/android/it-IT/images/sevenInchScreenshots/4_it-IT.png
fastlane/metadata/android/it-IT/images/tenInchScreenshots/1_it-IT.png
fastlane/metadata/android/it-IT/images/tenInchScreenshots/2_it-IT.png
fastlane/metadata/android/it-IT/images/tenInchScreenshots/3_it-IT.png
fastlane/metadata/android/it-IT/images/tenInchScreenshots/4_it-IT.png
fastlane/report.xml
fastlane/README.md

vendor
.npmrc

*.log
selfinvite 2021-11-19 21-56-49/
selfinvite.app.dSYm.zip
preview.html

# react-native-config codegen
ios/tmp.xcconfig
android/.env*
android/.env

.env
.env.local
android/.settings/*
.jest


*.cer
*.mobileprovision
*.jks

package-lock.json

# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
# tasks.json
# tasks/ 
