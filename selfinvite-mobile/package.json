{"name": "selfinvite-mobile", "version": "1.0.0", "description": "Selfinvite Mobile App - Social Dining Platform", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:android:production": "eas build --platform android --profile production", "build:ios:production": "eas build --platform ios --profile production", "submit:android": "eas submit --platform android --profile production", "submit:ios": "eas submit --platform ios --profile production", "deploy:android": "yarn build:android:production && yarn submit:android", "deploy:ios": "yarn build:ios:production && yarn submit:ios", "deploy:all": "yarn deploy:android && yarn deploy:ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@stripe/stripe-react-native": "0.45.0", "@supabase/supabase-js": "^2.52.0", "@tanstack/react-query": "^5.83.0", "@types/moment": "^2.11.29", "algoliasearch": "^5.37.0", "expo": "~53.0.20", "expo-apple-authentication": "~7.2.4", "expo-auth-session": "^6.2.1", "expo-constants": "^17.1.7", "expo-crypto": "~14.1.5", "expo-device": "~7.1.4", "expo-file-system": "^18.1.11", "expo-font": "~13.3.2", "expo-image": "~2.4.0", "expo-image-manipulator": "^13.1.7", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.7", "expo-localization": "^16.1.6", "expo-location": "~18.1.6", "expo-media-library": "^17.1.7", "expo-notifications": "~0.31.4", "expo-router": "^5.1.4", "expo-secure-store": "~14.2.3", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "i18n-js": "^4.5.1", "moment": "^2.30.1", "react": "19.0.0", "react-hook-form": "^7.60.0", "react-instantsearch-core": "^7.16.3", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-image-picker": "^8.2.1", "react-native-logs": "^5.3.0", "react-native-maps": "1.20.1", "react-native-modal-datetime-picker": "^18.0.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-simple-radio-button": "^2.7.4", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.28.0", "@types/react": "~19.0.10", "eslint": "^9.31.0", "eslint-config-expo": "^9.2.0", "jest": "~29.7.0", "prettier": "^3.6.2", "typescript": "~5.8.3"}, "packageManager": "yarn@1.22.22", "private": true}