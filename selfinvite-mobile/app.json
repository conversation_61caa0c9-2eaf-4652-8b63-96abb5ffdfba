{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "selfinvite-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "automatic", "scheme": "selfinvite", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#Dd4e58"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.selfinvite.mobile", "infoPlist": {"NSAppTransportSecurity": {"NSAllowsArbitraryLoads": true, "NSExceptionDomains": {"api.selfinvite.eu": {"NSExceptionAllowsInsecureHTTPLoads": true, "NSExceptionMinimumTLSVersion": "1.0", "NSIncludesSubdomains": true}, "localhost": {"NSExceptionAllowsInsecureHTTPLoads": true}}}, "ITSAppUsesNonExemptEncryption": false}}, "android": {"package": "com.selfinvite.mobile", "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#F24958"}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"backgroundColor": "#232323", "image": "./assets/splash-icon.png", "dark": {"image": "./assets/splash-icon-dark.png", "backgroundColor": "#000000"}, "imageWidth": 200}], "expo-font", "expo-secure-store", "expo-location", ["expo-notifications", {"icon": "./assets/notification-icon.png", "color": "#F24958"}], "expo-web-browser"], "experiments": {"typedRoutes": true, "reactCanary": true}, "extra": {"router": {}, "eas": {"projectId": "00d2df2e-0cb4-457f-b775-da1661161e4b"}}}}