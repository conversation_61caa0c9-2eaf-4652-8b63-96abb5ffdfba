import { TokenManager } from './tokenManager';
import supabase from '../../lib/supabase';
import log from '@/common/logger';
import { Platform } from 'react-native';
import Constants from 'expo-constants';

const getLocalApiUrl = () => {
  const manifest = Constants.expoConfig;
  log.info('Constants.expoConfig', Constants.expoConfig);
  const debuggerHost = manifest?.hostUri?.split(':')[0];
  log.info('debuggerHost', debuggerHost);
  return debuggerHost
  // TODO: change to http://${debuggerHost} if tunnel
    ? `http://${debuggerHost}:3000/api`
    : 'http://localhost:3000/api';
};

const API_BASE_URL = Platform.select({
  ios: __DEV__ ? getLocalApiUrl() : process.env.EXPO_PUBLIC_SUPABASE_URL,
  android: __DEV__
    ? 'http://********:3000/api'
    : process.env.EXPO_PUBLIC_SUPABASE_URL,
  default: process.env.EXPO_PUBLIC_SUPABASE_URL,
});

// Create a custom fetch function that automatically handles token refresh
export const apiClient = {
  async fetch<T = any>(url: string, options: RequestInit = {}): Promise<T> {
    try {
      // Get the current access token
      let accessToken = await TokenManager.getAccessToken();

      log.info('accessToken', accessToken);

      // Check if token is expired and refresh if needed
      const session = await TokenManager.getStoredSession();
      if (TokenManager.isTokenExpired(session)) {
        log.info('Token expired, refreshing...');
        const newSession = await TokenManager.refreshAndSaveSession(supabase);
        accessToken = newSession?.access_token || accessToken;
      }

      // Add authorization header if we have a token
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...(options.headers as Record<string, string>),
      };

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      // Make the request
      const response = await fetch(`${API_BASE_URL}${url}`, {
        ...options,
        headers,
      });

      // If we get a 401, try to refresh the token and retry once
      if (response.status === 401) {
        log.info('Got 401, attempting token refresh...');
        const newSession = await TokenManager.refreshAndSaveSession(supabase);
        if (newSession?.access_token) {
          headers['Authorization'] = `Bearer ${newSession.access_token}`;
          const retryResponse = await fetch(url, {
            ...options,
            headers,
          });

          if (!retryResponse.ok) {
            throw new Error(
              `Request failed: ${retryResponse.status} ${retryResponse.statusText}`,
            );
          }

          // Parse and return JSON response
          const contentType = retryResponse.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            return await retryResponse.json();
          }
          return {} as T;
        }
      }

      if (response.status >= 500) {
        throw new Error(
          `Request failed: ${response.status} ${response.statusText}`,
        );
      }

      // Parse and return JSON response
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      return {} as T;
    } catch (error: any) {
      log.error(`❌ Network request ${API_BASE_URL}${url} failed: `, error);
      throw new Error(
        `Network request ${API_BASE_URL}${url} failed: ${error.message}`,
      );
    }
  },

  // Convenience methods
  async get<T = any>(url: string, options: RequestInit = {}): Promise<T> {
    return this.fetch<T>(url, { ...options, method: 'GET' });
  },

  async post<T = any>(
    url: string,
    data: any,
    options: RequestInit = {},
  ): Promise<T> {
    return this.fetch<T>(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  async put<T = any>(
    url: string,
    data: any,
    options: RequestInit = {},
  ): Promise<T> {
    return this.fetch<T>(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  async patch<T = any>(
    url: string,
    data: any,
    options: RequestInit = {},
  ): Promise<T> {
    return this.fetch<T>(url, {
      ...options,
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  },

  async delete<T = any>(url: string, options: RequestInit = {}): Promise<T> {
    return this.fetch<T>(url, { ...options, method: 'DELETE' });
  },

  // Custom request method for special cases like FormData
  async request<T = any>(url: string, options: RequestInit = {}): Promise<T> {
    return this.fetch<T>(url, options);
  },
};

// Example usage functions
export const authApi = {
  // Example: Get user profile
  async getUserProfile() {
    return apiClient.get('/user/profile');
  },

  // Example: Update user profile
  async updateUserProfile(profileData: any) {
    return apiClient.put('/user/profile', profileData);
  },

  // Example: Get user events
  async getUserEvents() {
    return apiClient.get('/events/my-events');
  },

  // Example: Create event
  async createEvent(eventData: any) {
    return apiClient.post('/events', eventData);
  },
};
