import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Title, Paragraph } from 'react-native-paper';
import { useNotifications } from '../hooks/useNotifications';
import { sendMessageNotification, sendBookingNotification } from '../utils/notificationHelpers';
import logger from '../common/logger';

interface NotificationTestButtonProps {
  style?: any;
}

export function NotificationTestButton({ style }: NotificationTestButtonProps) {
  const { isEnabled, isInitialized, pushToken, requestPermissions } = useNotifications();

  const handleTestMessageNotification = async () => {
    try {
      await sendMessageNotification(
        'test-thread-123',
        'test-user-456',
        'Test User',
        'This is a test message notification!'
      );
      logger.info('Test message notification sent');
    } catch (error) {
      logger.error('Failed to send test message notification:', error);
    }
  };

  const handleTestBookingNotification = async () => {
    try {
      await sendBookingNotification(
        'test-booking-789',
        'test-event-101',
        'Test Event: Italian Pasta Night',
        'accepted',
        'Test Host',
        'Test Guest'
      );
      logger.info('Test booking notification sent');
    } catch (error) {
      logger.error('Failed to send test booking notification:', error);
    }
  };

  if (!isInitialized) {
    return (
      <Card style={style}>
        <Card.Content>
          <Title>Test Notifications</Title>
          <Paragraph>
            Notification system is initializing...
          </Paragraph>
        </Card.Content>
      </Card>
    );
  }

  if (!isEnabled) {
    return (
      <Card style={style}>
        <Card.Content>
          <Title>Test Notifications</Title>
          <Paragraph>
            Notifications are not enabled. Please enable notifications in your device settings to test the notification system.
          </Paragraph>
          <Paragraph style={{ marginTop: 8, fontSize: 12, opacity: 0.7 }}>
            Debug: Initialized: {isInitialized ? 'Yes' : 'No'}, Push Token: {pushToken ? 'Available' : 'None'}
          </Paragraph>
          <Button
            mode="contained"
            onPress={requestPermissions}
            style={{ marginTop: 8 }}
          >
            Request Permissions
          </Button>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card style={style}>
      <Card.Content>
        <Title>Test Notifications</Title>
        <Paragraph>
          Test the notification system (only visible when notifications are enabled)
        </Paragraph>
        <Paragraph style={{ marginTop: 8, fontSize: 12, opacity: 0.7 }}>
          Debug: Initialized: {isInitialized ? 'Yes' : 'No'}, Push Token: {pushToken ? 'Available' : 'None'}
        </Paragraph>
        <Button
          mode="outlined"
          onPress={handleTestMessageNotification}
          style={{ marginTop: 8 }}
        >
          Test Message Notification
        </Button>
        <Button
          mode="outlined"
          onPress={handleTestBookingNotification}
          style={{ marginTop: 8 }}
        >
          Test Booking Notification
        </Button>
      </Card.Content>
    </Card>
  );
}
