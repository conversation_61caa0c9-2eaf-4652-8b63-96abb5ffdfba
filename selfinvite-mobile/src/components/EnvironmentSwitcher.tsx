import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Surface, Text, Button, Chip, Divider } from 'react-native-paper';
import { useTheme } from 'react-native-paper';
import logger from '../common/logger';

interface EnvironmentConfig {
  name: string;
  label: string;
  color: string;
  description: string;
}

const ENVIRONMENTS: EnvironmentConfig[] = [
  {
    name: 'development',
    label: 'Development',
    color: '#4CAF50',
    description: 'Local development environment'
  },
  {
    name: 'preview',
    label: 'Preview',
    color: '#FF9800',
    description: 'Staging/preview environment'
  },
  {
    name: 'production',
    label: 'Production',
    color: '#F44336',
    description: 'Live production environment'
  }
];

interface EnvironmentSwitcherProps {
  style?: any;
}

export function EnvironmentSwitcher({ style }: EnvironmentSwitcherProps) {
  const theme = useTheme();
  const [selectedEnv, setSelectedEnv] = useState(process.env.EXPO_PUBLIC_APP_ENV || 'development');

  const handleEnvironmentSwitch = (envName: string) => {
    Alert.alert(
      'Switch Environment',
      `Are you sure you want to switch to ${envName}? This will require an app restart.`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Switch',
          style: 'destructive',
          onPress: () => {
            // In a real implementation, this would:
            // 1. Update the environment configuration
            // 2. Restart the app with new config
            // 3. Or reload the configuration dynamically
            
            setSelectedEnv(envName);
            logger.info(`Environment switched to: ${envName}`);
            
            // For demo purposes, just show a success message
            Alert.alert(
              'Environment Switched',
              `Environment has been switched to ${envName}. In a real app, this would trigger a restart.`,
              [{ text: 'OK' }]
            );
          },
        },
      ]
    );
  };

  const getCurrentEnvConfig = () => {
    return ENVIRONMENTS.find(env => env.name === selectedEnv) || ENVIRONMENTS[0];
  };

  const currentConfig = getCurrentEnvConfig();

  return (
    <Surface style={[styles.container, { backgroundColor: theme.colors.surfaceVariant }, style]}>
      <Text style={[styles.title, { color: theme.colors.onSurface }]}>
        Environment Configuration
      </Text>
      
      <View style={styles.currentEnvContainer}>
        <Text style={[styles.currentEnvLabel, { color: theme.colors.onSurface }]}>
          Current Environment:
        </Text>
        <Chip 
          mode="outlined" 
          style={[styles.currentEnvChip, { borderColor: currentConfig.color }]}
          textStyle={{ color: currentConfig.color }}
        >
          {currentConfig.label}
        </Chip>
      </View>

      <Text style={[styles.description, { color: theme.colors.onSurface }]}>
        {currentConfig.description}
      </Text>

      <Divider style={styles.divider} />

      <Text style={[styles.switchLabel, { color: theme.colors.onSurface }]}>
        Switch Environment:
      </Text>

      <View style={styles.environmentButtons}>
        {ENVIRONMENTS.map((env) => (
          <Button
            key={env.name}
            mode={selectedEnv === env.name ? "contained" : "outlined"}
            onPress={() => handleEnvironmentSwitch(env.name)}
            style={[
              styles.envButton,
              selectedEnv === env.name && { backgroundColor: env.color }
            ]}
            labelStyle={{
              color: selectedEnv === env.name ? 'white' : env.color
            }}
          >
            {env.label}
          </Button>
        ))}
      </View>

      <View style={styles.configInfo}>
        <Text style={[styles.configTitle, { color: theme.colors.onSurface }]}>
          Current Configuration:
        </Text>
        
        <View style={styles.configRow}>
          <Text style={[styles.configLabel, { color: theme.colors.onSurface }]}>
            Supabase URL:
          </Text>
          <Text style={[styles.configValue, { color: theme.colors.onSurface }]} numberOfLines={1}>
            {process.env.EXPO_PUBLIC_SUPABASE_URL ? 
              `${process.env.EXPO_PUBLIC_SUPABASE_URL.substring(0, 30)}...` : 
              'Not set'
            }
          </Text>
        </View>

        <View style={styles.configRow}>
          <Text style={[styles.configLabel, { color: theme.colors.onSurface }]}>
            Algolia App ID:
          </Text>
          <Text style={[styles.configValue, { color: theme.colors.onSurface }]}>
            {process.env.EXPO_PUBLIC_ALGOLIA_APP_ID || 'Not set'}
          </Text>
        </View>

        <View style={styles.configRow}>
          <Text style={[styles.configLabel, { color: theme.colors.onSurface }]}>
            Stripe Key:
          </Text>
          <Text style={[styles.configValue, { color: theme.colors.onSurface }]}>
            {process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY ? 
              `${process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY.substring(0, 20)}...` : 
              'Not set'
            }
          </Text>
        </View>
      </View>
    </Surface>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    marginVertical: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  currentEnvContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  currentEnvLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 8,
  },
  currentEnvChip: {
    marginLeft: 8,
  },
  description: {
    fontSize: 12,
    opacity: 0.7,
    marginBottom: 16,
  },
  divider: {
    marginVertical: 16,
  },
  switchLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 12,
  },
  environmentButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  envButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  configInfo: {
    marginTop: 16,
  },
  configTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 12,
  },
  configRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  configLabel: {
    fontSize: 12,
    fontWeight: '500',
    flex: 1,
  },
  configValue: {
    fontSize: 11,
    opacity: 0.8,
    flex: 1,
    textAlign: 'right',
  },
});

export default EnvironmentSwitcher;
