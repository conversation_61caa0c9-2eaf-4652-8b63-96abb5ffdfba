import React from 'react'
import { <PERSON><PERSON>, StyleSheet, View } from 'react-native'
import * as AuthSession from 'expo-auth-session'
import * as WebBrowser from 'expo-web-browser'
import { Button } from 'react-native-paper'
import { useAuthStore } from '../../src/stores/authStore'
import supabase from '../../lib/supabase'
import log from '@/common/logger'

WebBrowser.maybeCompleteAuthSession()

export default function SocialAuth() {
  const { setSession, setLoading, isLoading } = useAuthStore()

  const signInWithGoogle = async () => {
    setLoading(true)
    
    try {
      // Create redirect URI for mobile
      const redirectUri = AuthSession.makeRedirectUri({
        scheme: 'selfinvite',
        path: '/auth/callback'
      })
      
      log.info('Mobile redirect URI:', redirectUri)
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUri,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        }
      })
      
      if (error) {
        log.error('OAuth error:', error)
        Alert.alert('Error', error.message)
        return
      }
      
      if (data?.url) {
        const result = await WebBrowser.openAuthSessionAsync(
          data.url,
          redirectUri,
        )
        
        log.info('Auth session result:', result)
        
        if (result.type === 'success') {
          // Parse the URL to get the auth code
          const url = new URL(result.url)
          const code = url.searchParams.get('code')
          
          if (code) {
            // Exchange the code for a session
            const { data: session, error: sessionError } = await supabase.auth.exchangeCodeForSession(code)
            
            if (sessionError) {
              log.error('Session exchange error:', sessionError)
              Alert.alert('Error', 'Failed to complete authentication')
              return
            }
            
            // Store the session in our auth store
            setSession(session.session)
            
            log.info('Authentication successful!')
            log.info('Access token stored:', session.session.access_token)
            Alert.alert('Success', 'You have been signed in!')
            
          }
        } else if (result.type === 'cancel') {
          Alert.alert('Cancelled', 'Authentication was cancelled')
        } else {
          Alert.alert('Error', 'Authentication failed')
        }
      }
    } catch (error) {
      log.error('Sign in error:', error)
      Alert.alert('Error', 'An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const signInWithApple = async () => {
    setLoading(true)
    
    try {
      const redirectUri = AuthSession.makeRedirectUri({
        scheme: 'selfinvite',
        path: '/auth/callback'
      })
      
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: redirectUri,
        }
      })
      
      if (error) {
        log.error('Apple OAuth error:', error)
        Alert.alert('Error', error.message)
        return
      }
      
      if (data?.url) {
        const result = await WebBrowser.openAuthSessionAsync(
          data.url,
          redirectUri,
        )
        
        if (result.type === 'success') {
          const url = new URL(result.url)
          const code = url.searchParams.get('code')
          
          if (code) {
            const { data: session, error: sessionError } = await supabase.auth.exchangeCodeForSession(code)
            
            if (sessionError) {
              log.error('Session exchange error:', sessionError)
              Alert.alert('Error', 'Failed to complete authentication')
              return
            }
            
            setSession(session.session)
            Alert.alert('Success', 'You have been signed in!')
          }
        }
      }
    } catch (error) {
      log.error('Apple sign in error:', error)
      Alert.alert('Error', 'An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.verticallySpaced}>
        <Button
          mode="contained"
          icon="google"
          disabled={isLoading}
          onPress={signInWithGoogle}
          style={styles.button}
          contentStyle={styles.buttonContent}
        >
          Sign in with Google
        </Button>
      </View>
      
      <View style={styles.verticallySpaced}>
        <Button
          mode="outlined"
          icon="apple"
          disabled={isLoading}
          onPress={signInWithApple}
          style={styles.button}
          contentStyle={styles.buttonContent}
        >
          Sign in with Apple
        </Button>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 40,
    padding: 12,
  },
  verticallySpaced: {
    paddingTop: 4,
    paddingBottom: 4,
    alignSelf: 'stretch',
  },
  button: {
    marginVertical: 8,
  },
  buttonContent: {
    height: 50,
  },
}) 