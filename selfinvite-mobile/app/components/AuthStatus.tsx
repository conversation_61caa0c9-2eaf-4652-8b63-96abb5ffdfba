import React from 'react'
import { View, StyleSheet } from 'react-native'
import { Text, <PERSON>ton, Card, Chip } from 'react-native-paper'
import { useAuthStore, getAccessToken } from '../../src/stores/authStore'

export default function AuthStatus() {
  const { isAuthenticated, user, session, signOut, isLoading } = useAuthStore()

  if (isLoading) {
    return (
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="bodyMedium">Loading...</Text>
        </Card.Content>
      </Card>
    )
  }

  if (!isAuthenticated) {
    return (
      <Card style={styles.card}>
        <Card.Content>
          <Text variant="bodyMedium">Not authenticated</Text>
        </Card.Content>
      </Card>
    )
  }

  // Example of accessing the access token
  const accessToken = getAccessToken()

  return (
    <Card style={styles.card}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.title}>
          Authentication Status
        </Text>
        
        <View style={styles.statusRow}>
          <Chip icon="check-circle" mode="outlined">
            Authenticated
          </Chip>
        </View>

        {user && (
          <View style={styles.userInfo}>
            <Text variant="bodyMedium" style={styles.label}>
              Email: {user.email}
            </Text>
            <Text variant="bodyMedium" style={styles.label}>
              User ID: {user.id}
            </Text>
            <Text variant="bodyMedium" style={styles.label}>
              Provider: {user.app_metadata.provider}
            </Text>
          </View>
        )}

        {accessToken && (
          <View style={styles.tokenInfo}>
            <Text variant="bodyMedium" style={styles.label}>
              Access Token: {accessToken.substring(0, 20)}...
            </Text>
            <Text variant="bodySmall" style={styles.hint}>
              (truncated for display)
            </Text>
          </View>
        )}

        {session && (
          <View style={styles.sessionInfo}>
            <Text variant="bodyMedium" style={styles.label}>
              Expires At: {new Date(session.expires_at! * 1000).toLocaleString()}
            </Text>
          </View>
        )}

        <Button 
          mode="outlined" 
          onPress={signOut}
          style={styles.signOutButton}
        >
          Sign Out
        </Button>
      </Card.Content>
    </Card>
  )
}

const styles = StyleSheet.create({
  card: {
    margin: 16,
  },
  title: {
    marginBottom: 12,
    fontWeight: 'bold',
  },
  statusRow: {
    marginBottom: 16,
  },
  userInfo: {
    marginBottom: 12,
  },
  tokenInfo: {
    marginBottom: 12,
  },
  sessionInfo: {
    marginBottom: 16,
  },
  label: {
    marginBottom: 4,
  },
  hint: {
    color: '#666',
    fontStyle: 'italic',
  },
  signOutButton: {
    marginTop: 8,
  },
}) 