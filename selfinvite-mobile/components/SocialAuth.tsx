import React, { useState } from 'react'
import { StyleSheet, View } from 'react-native'
import * as AuthSession from 'expo-auth-session';
import supabase from '../lib/supabase'
import { Button, TextInput } from 'react-native-paper'
import * as WebBrowser from 'expo-web-browser';
import * as Linking from 'expo-linking';
import log from '@/common/logger'

WebBrowser.maybeCompleteAuthSession()

// const redirectUri = AuthSession.makeRedirectUri({
//   scheme: 'selfinvite',
//   // native: 'false',
//   // path: process.env.EXPO_PUBLIC_SUPABASE_URL! + '/auth/v1/callback'
// })

// const redirectUri = AuthSession.makeRedirectUri({
//     scheme: 'exp',
//   })

export default function SocialAuth() {
  const [loading, setLoading] = useState(false)

  const signInWithGoogle = async () => {
    // const redirectUri = Linking.createURL('/')
    //   console.log('Redirect URL:', redirectUri)
    log.info('makeRedirectUri:', AuthSession.makeRedirectUri({ path: '/auth/v1/callback' }))
    log.info('Linking.createURL:', Linking.createURL('/auth/v1/callback'))

    const redirectUri = AuthSession.makeRedirectUri({
      scheme: 'exp', // Let Expo determine the scheme
      path: '/auth/v1/callback'
    })
    // const redirectUri = process.env.EXPO_PUBLIC_SUPABASE_URL! + '/auth/v1/callback'
    log.info('redirectUri', redirectUri)
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: redirectUri,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      }
    })
    log.info('data', data)
    log.info('error', error)
  
    if (data?.url) {
      const result = await WebBrowser.openAuthSessionAsync(
        data.url,
        redirectUri,
      )
  
      log.info('result', result)
      if (result.type === 'success') {
        // User is now authenticated - check session
        const { data: session } = await supabase.auth.getSession()
        return { data: session }
      }
    }
  
    return { error: 'Authentication failed' }
  }

  return (
    <View style={styles.container}>
      <View style={styles.verticallySpaced}>
        <Button
          mode="contained"
          icon="google"
          disabled={loading}
          onPress={() => signInWithGoogle()}
          style={{ marginTop: 16 }}
        >
          Sign in with Google
        </Button>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 40,
    padding: 12,
  },
  verticallySpaced: {
    paddingTop: 4,
    paddingBottom: 4,
    alignSelf: 'stretch',
  },
  mt20: {
    marginTop: 20,
  },
})