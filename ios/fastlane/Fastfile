# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane
default_platform(:ios)

platform :ios do

  desc "Generate new localized screenshots"
  lane :screenshots do
    capture_screenshots(workspace: "selfinvite.xcworkspace", scheme: "selfinvite")
  end

  desc "Push a new beta build to TestFlight"
  desc "This will also make sure the profile is up to date"  
  lane :beta do
    # UI.important "Important Message"
    # UI.error "O NO!"
    # UI.message "Basic blah"
    answer = UI.select("Would you like to add a Changelog?", ["Yes", "No"])
    changelog = ask('Type Changelog message:') if answer == "Yes"
    
    # Get latest certs appstore
    match(type: 'appstore')
    
    increment_build_number(xcodeproj: "selfinvite.xcodeproj")
    build_app(scheme: "selfinvite")
    
    if changelog
      upload_to_testflight(changelog: changelog)
    else
      upload_to_testflight
    end    
    
    # slack(
    #   message: "App successfully released",
    #   channel: "#myAppChannel"
    # )    
  end
end
