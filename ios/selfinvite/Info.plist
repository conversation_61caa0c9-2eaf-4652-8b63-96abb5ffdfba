<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleAllowMixedLocalizations</key>
	<string>true</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Selfinvite</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(VERSION_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>selfinvite</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>selfinvite</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>selfinvite</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.j.autoinvito</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>selfinvite</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb345992691296635</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>auth0</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>$(PRODUCT_BUNDLE_IDENTIFIER).auth0</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(VERSION_CODE)</string>
	<key>FacebookAppID</key>
	<string>$(FB_APP_ID)</string>
	<key>FacebookDisplayName</key>
	<string>selfinvite</string>
	<key>GADApplicationIdentifier</key>
	<string>ca-app-pub-3940256099942544~1458002511</string>
	<key>GADDelayAppMeasurementInit</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi20130214</string>
		<string>fbapi20130410</string>
		<string>fbapi20130702</string>
		<string>fbapi20131010</string>
		<string>fbapi20131219</string>
		<string>fbapi20140410</string>
		<string>fbapi20140116</string>
		<string>fbapi20150313</string>
		<string>fbapi20150629</string>
		<string>fbapi20160328</string>
		<string>fbauth</string>
		<string>fb-messenger-share-api</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAllowsArbitraryLoads</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSCalendarsUsageDescription</key>
	<string>Allow Selfinvite to access your calendar</string>
	<key>NSCameraUsageDescription</key>
	<string>Allow Selfinvite to use your camera to take picture for Profile/Events</string>
	<key>NSContactsUsageDescription</key>
	<string>Allow Selfinvite to access your contacts</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Allow Selfinvite to use your location to show your distance to the events</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Allow Selfinvite to use your location to show your distance to the events</string>
	<key>NSLocationUsageDescription</key>
	<string>Allow Selfinvite to use your location to show your distance to the events</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Allow Selfinvite to use your location to show your distance to the events</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Allow Selfinvite to access your microphone</string>
	<key>NSMotionUsageDescription</key>
	<string>Allow Selfinvite to access your device's accelerometer</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Give Selfinvite permission to save photos for User Profile/Events</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Give Selfinvite permission to access your photos for User Profile/Events</string>
	<key>NSRemindersUsageDescription</key>
	<string>Allow Selfinvite to access your reminders</string>
	<key>SKAdNetworkIdentifier</key>
	<string>n38lu8286q.skadnetwork</string>
	<key>UIAppFonts</key>
	<array>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>EvilIcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>MaterialCommunityIcons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>Zocial.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>SplashScreen.storyboard</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
