<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Selfinvite</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>com.j.autoinvito</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Selfinvite</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.1.6</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>selfinvite</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>OAuthRedirect</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.j.autoinvito</string>
			</array>
		</dict>
	</array>
	<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>fb345992691296635</string>
				</array>
			</dict>
		</array>
	<key>FacebookAppID</key>
	<string>$(FB_APP_ID)</string>
	<key>FacebookDisplayName</key>
	<string>Selfinvite</string>


	<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>fbapi</string>
			<string>fbapi20130214</string>
			<string>fbapi20130410</string>
			<string>fbapi20130702</string>
			<string>fbapi20131010</string>
			<string>fbapi20131219</string>
			<string>fbapi20140410</string>
			<string>fbapi20140116</string>
			<string>fbapi20150313</string>
			<string>fbapi20150629</string>
			<string>fbapi20160328</string>
			<string>fbauth</string>
			<string>fb-messenger-share-api</string>
			<string>fbauth2</string>
			<string>fbshareextension</string>
		</array>

	<key>CFBundleVersion</key>
	<string>1.1.6</string>
	<key>FacebookAdvertiserIDCollectionEnabled</key>
	<false/>
	<key>FacebookAutoInitEnabled</key>
	<false/>
	<key>FacebookAutoLogAppEventsEnabled</key>
	<false/>
	<key>GADApplicationIdentifier</key>
	<string>ca-app-pub-3940256099942544~1458002511</string>
	<key>GADDelayAppMeasurementInit</key>
	<true/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCalendarsUsageDescription</key>
	<string>Allow Selfinvite to access your calendar</string>
	<key>NSCameraUsageDescription</key>
	<string>Allow Selfinvite to use your camera</string>
	<key>NSContactsUsageDescription</key>
	<string>Allow Selfinvite to access your contacts</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Allow Selfinvite to use your location</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Allow Selfinvite to access your microphone</string>
	<key>NSMotionUsageDescription</key>
	<string>Allow Selfinvite to access your device's accelerometer</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Give Selfinvite permission to save photos</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Give Selfinvite permission to access your photos</string>
	<key>NSRemindersUsageDescription</key>
	<string>Allow Selfinvite to access your reminders</string>
	<key>UIAppFonts</key>
	<array>
    <string>AntDesign.ttf</string>
    <string>Entypo.ttf</string>
    <string>EvilIcons.ttf</string>
    <string>Feather.ttf</string>
    <string>FontAwesome.ttf</string>
    <string>FontAwesome5_Brands.ttf</string>
    <string>FontAwesome5_Regular.ttf</string>
    <string>FontAwesome5_Solid.ttf</string>
    <string>Foundation.ttf</string>
    <string>Ionicons.ttf</string>
    <string>MaterialIcons.ttf</string>
    <string>MaterialCommunityIcons.ttf</string>
    <string>SimpleLineIcons.ttf</string>
    <string>Octicons.ttf</string>
    <string>Zocial.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array/>
	<key>UIRequiresFullScreen</key>
	<true/>
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleLightContent</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
