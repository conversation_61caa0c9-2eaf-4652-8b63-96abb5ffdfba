{"ios": {"buildNumber": "1.1.6", "supportsTablet": true, "bundleIdentifier": "com.j.autoinvito", "publishBundlePath": "ios/selfinvite/Supporting/shell-app.bundle", "publishManifestPath": "ios/selfinvite/Supporting/shell-app-manifest.json"}, "icon": "./assets/ic_launcher.png", "name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON>", "detach": {"iosExpoViewUrl": "https://s3.amazonaws.com/exp-exponent-view-code/ios-v2.14.3-sdk36.0.0-00f587c0-b8c9-4b6b-88f6-2ccd4f20dc9c.tar.gz", "androidExpoViewUrl": "https://s3.amazonaws.com/exp-exponent-view-code/android-v2.14.2-sdk36.0.0-ce7e16db-fd5d-4fdd-bdeb-701ba94a04d1.tar.gz"}, "scheme": "selfinvite", "splash": {"image": "./assets/splash.png", "imageUrl": "https://d1wp6m56sqw74a.cloudfront.net/~assets/d5addf2db9b66b2100c344820563cea2", "resizeMode": "contain", "backgroundColor": "#F24958"}, "android": {"package": "com.j.autoinvito", "versionCode": 7, "publishBundlePath": "android/app/src/main/assets/shell-app.bundle", "publishManifestPath": "android/app/src/main/assets/shell-app-manifest.json"}, "iconUrl": "https://d1wp6m56sqw74a.cloudfront.net/~assets/8836bddee53f9786cb1a0a16de971dc7", "locales": {}, "privacy": "public", "updates": {"fallbackToCacheTimeout": 0}, "version": "1.1.6", "platforms": ["ios", "android"], "isDetached": true, "sdkVersion": "36.0.0", "description": "Self Invite app", "orientation": "portrait", "dependencies": ["@ptomasroos/react-native-multi-slider", "@react-native-community/datetimepicker", "@react-native-community/masked-view", "@types/googlemaps", "@types/markerclustererplus", "@types/react", "apisauce", "base64-img", "expo", "expo-constants", "expo-facebook", "expo-file-system", "expo-image-picker", "expo-localization", "expo-location", "expo-permissions", "expokit", "fetch-defaults", "i18n-js", "install", "libphonenumber-js", "moment", "npm", "prop-types", "react", "react-devtools", "react-dom", "react-lifecycles-compat", "react-native", "react-native-app-intro-slider", "react-native-calendar-picker", "react-native-calendario", "react-native-cookies", "react-native-fbsdk", "react-native-gesture-handler", "react-native-gifted-chat", "react-native-image-base64", "react-native-maps", "react-native-modal-datetime-picker", "react-native-paper", "react-native-picker-select", "react-native-reanimated", "react-native-safe-area-context", "react-native-screens", "react-native-sectioned-multi-select", "react-native-simple-radio-button", "react-native-star-rating", "react-native-swipe-gestures", "react-native-ui-stepper", "react-native-vector-icons", "react-navigation", "react-navigation-drawer", "react-navigation-stack", "react-navigation-tabs", "react-phone-number-input", "react-redux", "redux", "redux-promise", "redux-saga", "<PERSON><PERSON>h", "validate.js"], "bundledAssets": ["asset_3a2ba31570920eeb9b1d217cabe58315.ttf", "asset_8b12b3e16d591abc926165fa8f760e3b.json", "asset_744ce60078c17d86006dd0edabcd59a7.ttf", "asset_461d9bba8b6a3c91675039df12cfe6ca.json", "asset_140c53a7643ea949007aa9a282153849.ttf", "asset_94c4ffdcbffeb0570c635d7f8edd8a25.json", "asset_6beba7e6834963f7f171d3bdd075c915.ttf", "asset_648f2d510967a87880abfed9476aeb28.json", "asset_b06871f281fee6b241d60582ae9369b9.ttf", "asset_f1f91feb805137c9283fb766620ec5eb.json", "asset_09dd345dbd4ec5a0874841d5749ac153.json", "asset_0886a6b127c6057cee83f9c65c7ffd62.json", "asset_2e562d4ebf15395f00bc738738f79291.ttf", "asset_872545dde71de3842234bf6afe80c4cb.ttf", "asset_c6aef942e3668158ec29d4adcb2e768f.ttf", "asset_e20945d7c929279ef7a6f1db184a4470.ttf", "asset_60668d999bbaf663420340f7bdd580d7.json", "asset_b2e0fc821c6886fb3940f85a3320003e.ttf", "asset_3e6805fbc794680014716b8c752f20b8.json", "asset_5a293a273bee8d740a045d9922b9a9ae.ttf", "asset_b582e1c8a605c3b9a1c26e09789a78d4.json", "asset_a37b0c01c0baf1888ca812cc0508f6e2.ttf", "asset_7e078700f0c35367a56c5bbb2047dda7.json", "asset_8e7f807ef943bff1f6d3c2c6e0f3769e.ttf", "asset_fdc01171a7a7ea76b187afcd162dee7d.json", "asset_d2285965fe34b05465047401b8595dd0.ttf", "asset_647543ebfccf6e5495434383598453d1.json", "asset_5cdf883b18a5651a29a4d1ef276d2457.ttf", "asset_74d124a3caeac2bea111f3ca2f2dd34a.json", "asset_2379ae894c2c9f63b852a9f3676c2763.png", "asset_8f03b83455473d2bcdcf1404c9e3486f.jpeg", "asset_b01f55a8ec3c1b03fd6aa72c2b400977.jpg", "asset_33945e126f7f346d0afcf143dcdb474f.jpeg", "asset_07982798f8ea36b3e94e8359f452bd8d.jpeg", "asset_9ec07bf0d44e2049190e70021ba84bc5.jpeg", "asset_3cb9aa7f399cad6201bd58099620fe40.png", "asset_9ba278ba2d5b4cd067f1426ceaa264dd.png", "asset_100b38fa184634fc89bd07a84453992c.ttf", "asset_9c46095118380d38f12e67c916b427f9.ttf", "asset_a98626e1aef6ceba5dfc1ee7112e235a.ttf", "asset_9bc77c3bca968c7490de95d1532d0e87.ttf", "asset_38bc5e073a0692a4eddd8e61c821d57a.ttf", "asset_0052573bbf05658a18ba557303123533.ttf", "asset_f94f8f7b1fa1614f2f2e2fd394fa27c9.png", "asset_8ce2cbc6cd3981de52764b1bddd05c17.png", "asset_c90bbcc3c193332b39027772b163a92b.png", "asset_906352c305fb3ee3a8710a6b0ddd669f.png", "asset_9181b083d0bf6e5bcadabfc43b2444ea.png", "asset_11b4e8e94ad6acf0bfea73e575a6095e.png", "asset_2d04e7bbd51181bcd9e390727b306fe3.png", "asset_7d40544b395c5949f4646f5e150fe020.png", "asset_cdd04e13d4ec83ff0cd13ec8dabdc341.png", "asset_a132ecc4ba5c1517ff83c0fb321bc7fc.png", "asset_0ea69b5077e7c4696db85dbcba75b0e1.png", "asset_f5b790e2ac193b3d41015edb3551f9b8.png", "asset_6165c9d7a2e729ba57b23dd93add5366.png", "asset_2afd382532eb1a426e1fd522c213a2a6.png", "asset_82c0a3e29fb50c26b993f187578d4aa0.json", "asset_778ffc9fe8773a878e9c30a6304784de.png", "asset_376d6a4c7f622917c39feb23671ef71d.png", "asset_c79c3606a1cf168006ad3979763c7e0c.png", "asset_02bc1fa7c0313217bde2d65ccbff40c9.png", "asset_35ba0eaec5a4f5ed12ca16fabeae451d.png"], "id": "@jonno85/Selfinvite", "releaseId": "3817af57-a48b-467a-a088-3bd9db5d9aaf", "revisionId": "1.1.6-r.HJ30lGsVU", "publishedTime": "2020-03-02T23:03:48.196Z", "commitTime": "2020-03-02T23:03:48.232Z", "bundleUrl": "https://d1wp6m56sqw74a.cloudfront.net/%40jonno85%2FSelfInvite%2F1.1.6%2F566a13e3c8f87a9ecd86bfe3e76ca430-36.0.0-ios.js", "releaseChannel": "default", "hostUri": "exp.host/@jonno85/Selfinvite"}