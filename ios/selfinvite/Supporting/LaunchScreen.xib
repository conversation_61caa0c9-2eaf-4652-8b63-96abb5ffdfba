<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14113" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clipsSubviews="YES" contentMode="scaleToFill" id="OfY-5Y-tS4">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <subviews>
                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="launch_background_image.png" translatesAutoresizingMaskIntoConstraints="NO" id="Bsh-cT-K4l" userLabel="Background Image View">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                </imageView>
                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="launch_icon.png" translatesAutoresizingMaskIntoConstraints="NO" id="g6l-cP-5Ll" userLabel="Icon Image View">
                    <rect key="frame" x="123.5" y="269.5" width="128" height="128"/>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="128" id="fjm-AJ-FS8"/>
                        <constraint firstAttribute="width" constant="128" id="gAL-Wy-loA"/>
                    </constraints>
                </imageView>
            </subviews>
            <color key="backgroundColor" red="0.9490196078431372" green="0.28627450980392155" blue="0.34509803921568627" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="Bsh-cT-K4l" secondAttribute="trailing" id="4Bz-Vy-RfD"/>
                <constraint firstItem="Bsh-cT-K4l" firstAttribute="top" secondItem="OfY-5Y-tS4" secondAttribute="top" id="5FF-OI-m4s"/>
                <constraint firstAttribute="centerX" secondItem="g6l-cP-5Ll" secondAttribute="centerX" id="9G9-hV-oVV"/>
                <constraint firstItem="g6l-cP-5Ll" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="OfY-5Y-tS4" secondAttribute="leading" id="JQM-pd-zJi"/>
                <constraint firstItem="g6l-cP-5Ll" firstAttribute="top" relation="greaterThanOrEqual" secondItem="OfY-5Y-tS4" secondAttribute="top" id="SxB-Cd-7Gy"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="g6l-cP-5Ll" secondAttribute="trailing" id="VDa-AF-XEa"/>
                <constraint firstItem="Bsh-cT-K4l" firstAttribute="leading" secondItem="OfY-5Y-tS4" secondAttribute="leading" id="ZLr-Ag-Lsg"/>
                <constraint firstAttribute="top" secondItem="g6l-cP-5Ll" secondAttribute="bottom" constant="10" id="cuH-ve-e1H"/>
                <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="g6l-cP-5Ll" secondAttribute="bottom" id="fbb-we-kfv"/>
                <constraint firstItem="g6l-cP-5Ll" firstAttribute="centerY" secondItem="OfY-5Y-tS4" secondAttribute="centerY" id="hI6-Px-LsN"/>
                <constraint firstItem="g6l-cP-5Ll" firstAttribute="centerX" secondItem="OfY-5Y-tS4" secondAttribute="centerX" id="hV2-Y5-obV"/>
                <constraint firstAttribute="bottom" secondItem="Bsh-cT-K4l" secondAttribute="bottom" id="xDi-Tc-3P0"/>
            </constraints>
            <point key="canvasLocation" x="201" y="453"/>
        </view>
    </objects>
    <resources>
        <image name="launch_background_image.png" width="64" height="64"/>
        <image name="launch_icon.png" width="64" height="64"/>
    </resources>
</document>