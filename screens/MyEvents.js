import React, { Component } from 'react';
import { useNavigation } from '@react-navigation/native';
import {
  View,
  FlatList,
  ImageBackground,
  StyleSheet,
  ActivityIndicator,
  Share,
  Text,
} from 'react-native';
import { Surface, Card, FAB, IconButton, Subheading } from 'react-native-paper';
import * as Icon from '@expo/vector-icons';
import { connect } from 'react-redux';
import {
  colors,
  defaultDateTimeFormatClean,
  EVENT_OFFER_TYPE_OFFER_PRIVATE,
  EVENT_OFFER_TYPE_OFFER_PUBLIC,
  EVENT_STATE_CLOSING,
  EVENT_STATE_FEEDBACK_CLOSE,
  EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE,
  EVENT_STATE_OPEN,
  getFirstImage,
  getIcon,
  getShareableObject,
  globalStyles,
  _ERROR,
} from '@constants';
import AlertDialog from '@components/AlertDialog';
import ConfirmDialog from '@components/ConfirmDialog';
import i18n from '@translations/index';
import EventsActions from '@reducers/events.reducer';
import moment from 'moment';
import {
  CREATE_EVENT_ROUTE,
  EVENT_DETAILS_ROUTE,
} from '@routes/route_constants';
import TagText from '@components/TagText';

class Event extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showConfirmDelete: false,
      showConfirmUpdate: false,
      showConfirmClone: false,
    };
  }

  render() {
    const { item, navigate, deleteEvent, shareHandle, isReadOnly, push } =
      this.props;

    return (
      <Surface
        style={{
          marginHorizontal: 20,
          marginVertical: 10,
          borderRadius: 30,
          borderBottomLeftRadius: 30,
          elevation: 15,
        }}>
        <ConfirmDialog
          title={'__sure_?'}
          description={'__confirm_del'}
          onOk={() => {
            this.setState({ showConfirmDelete: false });
            deleteEvent(item.id);
          }}
          visible={this.state.showConfirmDelete}
          onClose={() => {
            this.setState({ showConfirmDelete: false });
          }}
        />
        <ConfirmDialog
          title={'__sure_?'}
          description={'__confirm_ed'}
          onOk={() => {
            this.setState({ showConfirmUpdate: false });
            console.log('navigate to CREATE_EVENT_ROUTE', CREATE_EVENT_ROUTE);
            navigate(CREATE_EVENT_ROUTE, { event: item });
          }}
          visible={this.state.showConfirmUpdate}
          onClose={() => {
            this.setState({ showConfirmUpdate: false });
          }}
        />
        <ConfirmDialog
          title={'__sure_?'}
          description={'__confirm_clone'}
          onOk={() => {
            this.setState({ showConfirmClone: false });
            navigate(CREATE_EVENT_ROUTE, { event: item, clone: true });
          }}
          visible={this.state.showConfirmClone}
          onClose={() => {
            this.setState({ showConfirmClone: false });
          }}
        />
        <ImageBackground
          source={getFirstImage(item)}
          style={{
            width: '100%',
            height: 220,
            borderRadius: 30,
            borderBottomLeftRadius: 30,
            overflow: 'hidden',
            justifyContent: 'flex-end',
          }}>
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              margin: 10,
              flexDirection: 'row',
            }}>
            {!isReadOnly &&
            item.state !== EVENT_STATE_FEEDBACK_CLOSE &&
            item.state !== EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE ? (
              <Surface Surface style={styles.surface}>
                <IconButton
                  onPress={() => shareHandle(item)}
                  color={colors.declined}
                  size={16}
                  icon={({ size, color }) => (
                    <Icon.MaterialCommunityIcons
                      name="share-variant"
                      color={color}
                      size={size}
                    />
                  )}
                />
              </Surface>
            ) : null}
            {!isReadOnly ? (
              <Surface style={styles.surface}>
                <IconButton
                  onPress={() =>
                    push(EVENT_DETAILS_ROUTE, {
                      id: item.id,
                      showFab: false,
                      userInfoEnable: false,
                      pushedStack: true,
                    })
                  }
                  color={colors.declined}
                  size={16}
                  icon={({ size, color }) => (
                    <Icon.MaterialCommunityIcons
                      name="eye"
                      color={color}
                      size={size}
                    />
                  )}
                />
              </Surface>
            ) : null}
          </View>
          <View
            style={{
              position: 'absolute',
              top: 0,
              right: 0,
              margin: 10,
              flexDirection: 'row',
            }}>
            {!isReadOnly ? (
              <Surface style={styles.surface}>
                <IconButton
                  onPress={() => {
                    item.state === EVENT_STATE_OPEN ||
                    item.state === EVENT_STATE_CLOSING
                      ? this.setState({ showConfirmUpdate: true })
                      : this.setState({ showConfirmClone: true });
                  }}
                  color={colors.declined}
                  size={16}
                  icon={({ size, color }) => (
                    <Icon.Feather
                      name={
                        item.state === EVENT_STATE_OPEN ||
                        item.state === EVENT_STATE_CLOSING
                          ? 'edit'
                          : 'copy'
                      }
                      color={color}
                      size={size}
                    />
                  )}
                />
              </Surface>
            ) : null}
            {
              !isReadOnly && (
                // (item.state === EVENT_STATE_FEEDBACK_CLOSE ||
                //   item.state === EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE) ? (
                <Surface style={styles.surface}>
                  <IconButton
                    onPress={() => {
                      this.setState({ showConfirmDelete: true });
                    }}
                    color={colors.declined}
                    size={16}
                    icon={({ size, color }) => (
                      <Icon.Feather name="trash" color={color} size={size} />
                    )}
                  />
                </Surface>
              )
              // ) : null
            }
          </View>
          <Card.Title
            style={{
              backgroundColor: colors.whiteBackground,
              margin: 10,
              padding: 0,
              borderRadius: 20,
              borderBottomLeftRadius: 20,
              elevation: 10,
            }}
            right={() => {
              return (
                <View
                  style={{
                    flexDirection: 'column',
                    alignItems: 'flex-end',
                    justifyContent: 'space-between',
                    marginBottom: 0,
                  }}>
                  <View style={{ flexDirection: 'row' }}>
                    <View style={{ marginRight: 5 }}>
                      {item.type === EVENT_OFFER_TYPE_OFFER_PUBLIC ? (
                        <Text
                          style={{
                            ...styles.label,
                            borderColor: colors.success,
                            color: colors.success,
                          }}>
                          {i18n.t('__event_public')}
                        </Text>
                      ) : item.type === EVENT_OFFER_TYPE_OFFER_PRIVATE ? (
                        <Text
                          style={{
                            ...styles.label,
                            borderColor: colors.warning,
                            color: colors.warning,
                          }}>
                          {i18n.t('__event_private')}
                        </Text>
                      ) : (
                        <></>
                      )}
                    </View>
                    <View>
                      {item.state === EVENT_STATE_OPEN ? (
                        <Text
                          style={{
                            ...styles.label,
                            borderColor: colors.success,
                            color: colors.success,
                          }}>
                          {i18n.t('__open')}
                        </Text>
                      ) : item.state === EVENT_STATE_CLOSING ? (
                        <Text
                          style={{
                            ...styles.label,
                            borderColor: colors.warning,
                            color: colors.warning,
                          }}>
                          {i18n.t('__closing')}
                        </Text>
                      ) : (
                        <Text
                          style={{
                            ...styles.label,
                            borderColor: colors.error,
                            color: colors.error,
                          }}>
                          {i18n.t('__closed')}
                        </Text>
                      )}
                    </View>
                  </View>
                </View>
              );
            }}
            rightStyle={{
              marginBottom: -45,
              marginRight: 10,
              paddingBottom: 0,
            }}
            titleStyle={{ fontFamily: 'medium', fontSize: 16 }}
            subtitleStyle={{ fontFamily: 'regular', opacity: 1 }}
            title={<TagText value={item.name} />}
            subtitle={moment(item.date).format(defaultDateTimeFormatClean)}
          />
        </ImageBackground>
      </Surface>
    );
  }
}

class MyEvents extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAlertModal: false,
      messageType: '',
      message: '',
      readOnly: false,
      login: props.route.params?.login ?? '',
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.events.fetchingAll && !this.props.events.fetchingAll) {
      if (this.props.events.error) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__server_error_get_events',
        });
      }
    }
    if (prevProps.events.deleting && !this.props.events.deleting) {
      if (this.props.events.errorDeleting) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: this.props.events.errorDeleting,
          //TODO check if it is '__not_deleteable'
        });
      } else {
        this.getEventOffers();
      }
    }
  }

  getEventOffers = () => {
    const { userInfoId } = this.props.common;
    let { readOnly } = this.state;
    if (
      this.props.navigation.state?.params !== undefined &&
      this.props.navigation.state?.params?.readOnly !== undefined
    )
      readOnly = this.props.navigation?.state?.params?.readOnly;

    this.setState({ readOnly });

    let options = '?status=closed&userId=' + userInfoId;
    // console.log('getEventOffers options', options, 'readOnly ', readOnly);
    this.props.getEventOffers(!readOnly ? '' : options);
  };

  deleteThisEvent = item => {
    this.props.deleteEvent(item.id);
  };

  shareHandle = async item => {
    try {
      console.log('shareHandle ', item);
      const result = await Share.share(getShareableObject(item));
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
          console.log('shareHandle result with activity', result);
        } else {
          // shared
          console.log('shareHandle result', result);
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
        console.log('shareHandle result dismissedAction', result);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  componentDidMount() {
    this.focusSubscription = this.props.navigation.addListener(
      'focus',
      this.getEventOffers,
    );
    console.log('this.focusSubscription', this.focusSubscription);
  }
  componentWillUnmount() {
    this.focusSubscription();
  }

  render() {
    const { showAlertModal, message, messageType, readOnly } = this.state;
    const { events, fetchingAll } = this.props.events;
    return (
      <View
        style={{
          flex: 1,
          backgroundColor: colors.primary,
        }}>
        {/* <NavigationEvents onDidFocus={this.getEventOffers}></NavigationEvents> */}
        <Surface
          style={{
            flex: 1,
            elevation: 30,
            borderTopRightRadius: 15,
            borderTopLeftRadius: 15,
            paddingTop: 5,
          }}>
          <Subheading style={{ paddingHorizontal: 20 }}>
            {i18n.t('__total') + ' '}
            {events !== undefined && events !== null ? events.length : 0}
            {' ' + i18n.t('__events')}
          </Subheading>
          {fetchingAll === true ? (
            <View style={globalStyles.container}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : events.length === 0 ? (
            <View style={globalStyles.container}>
              <Icon.MaterialCommunityIcons
                color={colors.black}
                name="calendar"
                size={80}
              />
              <Text style={{ color: colors.black }}>
                {i18n.t('__no_events')}
              </Text>
            </View>
          ) : (
            <FlatList
              data={events}
              keyExtractor={(item, index) => index.toString()}
              renderItem={({ item }) => (
                <Event
                  item={item}
                  deleteEvent={() => this.deleteThisEvent(item)}
                  navigate={this.props.navigation.navigate}
                  push={this.props.navigation.push}
                  reloadHandle={this.reloadHandle}
                  shareHandle={this.shareHandle}
                  isReadOnly={readOnly}
                />
              )}
            />
          )}
        </Surface>
        {readOnly === false ? (
          <FAB
            onPress={() =>
              this.props.navigation.navigate(CREATE_EVENT_ROUTE, {})
            }
            style={styles.fab}
            icon={getIcon('add')}
          />
        ) : null}
        <AlertDialog
          onClose={() => {
            this.setState({ showAlertModal: false });
          }}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    myEvents: appState.eventOffers,
    events: appState.events,
    common: appState.common,
    account: appState.account,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    getEventOffers: options => dispatch(EventsActions.eventAllRequest(options)),
    deleteEvent: eventId => dispatch(EventsActions.eventDeleteRequest(eventId)),
  };
}
const MyEventsNavigation = props => {
  const navigation = useNavigation();
  console.log('MyEventsNavigation ', props);
  console.log('MyEventsNavigation navigation', navigation);
  return <MyEvents {...props} navigation={navigation} />;
};
export default connect(mapStateToProps, mapDispatchToProps)(MyEventsNavigation);

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    backgroundColor: colors.secondary,
    color: 'white',
    zIndex: 100,
    elevation: 50,
    margin: 20,
    right: 0,
    bottom: 0,
  },
  label: {
    borderRadius: 10,
    borderWidth: 1,
    paddingLeft: 5,
    paddingRight: 5,
    backgroundColor: colors.whiteBackground,
    overflow: 'hidden',
  },
  surface: {
    backgroundColor: 'white',
    elevation: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
});
