import React, { Component } from 'react';
import {
  View,
  FlatList,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import {
  Subheading,
  Portal,
  Dialog,
  Paragraph,
  But<PERSON>,
  <PERSON>,
  Menu,
} from 'react-native-paper';
import { MyOffer } from '@components';
import { connect } from 'react-redux';
import {
  colors,
  EVENT_OFFER_KV_STATE_HOST_CONFIRM,
  EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND,
  EVENT_OFFER_KV_STATE_HOST_CANCEL,
  EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL,
  _ERROR,
  _SUCCESS,
  EVENT_OFFER_KV_STATE_GUEST_REQUEST,
  getIcon,
  EVENT_OFFER_KV_STATE_GUEST_PAID,
  EVENT_OFFER_KV_STATE_GUEST_CANCEL,
  EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL,
  EVENT_STATE_FEEDBACK_OPEN,
  EVENT_STATE_FEEDBACK_CLOSE,
  EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT,
  _WARNING,
  globalStyles,
  EVENT_OFFER_KV_HOST_DENIES_REFUND,
  EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST,
  EVENT_OFFER_KV_HOST_CONFIRMS_REFUND,
  _DELETE,
  EVENT_OFFER_KV_STATE_NO_REFUND,
  EOKV_V_PARTICIPANTS,
  _CANCEL,
  EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST_REFUNDED_DONE,
  PHONE_VERIFIED,
} from '@constants';
import * as Icon from '@expo/vector-icons';
import i18n from '@translations/index';
import AlertDialog from '@components/AlertDialog';
import EventKVActions from '@reducers/eventKv.reducer';
import { REVIEW_ROUTE } from '@routes/route_constants';
import { isLoggedIn } from '@reducers/account.reducer';

class YourRequests extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showModal: false,
      showConfirmationModal: false,
      showResultModal: false,
      confirmMessage: '',
      confirmMessageType: '',
      showDeleteModal: false,
      confirmDisabled: true,
      confirmLoading: false,
      message: '__cancel',
      messageType: _SUCCESS,
      event_id: 0,
      nextState: -1,
      refundChangeState: false,
      offers: [],
      showMenu: false,
      sortType: 'desc',
      focused: false,
      showToggleMenu: false,
      filterType: '__all',
      disablePayButton: true,
      selectedId: props.route.params?.eokvid,
      page: 0,
    };
    // console.log('props.route>>', props.route);
    console.log('YourRequests selectedId ', this.state.selectedId);
  }

  showDeleteDialog = (text, event) => {
    this.setState({
      showDeleteModal: true,
      message: text,
      selectedEvent: event,
      messageType: _DELETE,
    });
  };

  loadMore() {
    const nextPage = this.state.page + 1;
    this.setState({ page: nextPage });
    this.props.getYourRequests(nextPage);
  }

  toggleMenu(status) {
    this.setState({ showToggleMenu: status });
  }

  openMenu() {
    this.setState({ showMenu: true });
  }

  closeMenu() {
    this.setState({ showMenu: false });
  }

  selectFilter = filterType => {
    console.log('selectFilter filterType', filterType);
    let showState = undefined;
    let showOfferState = undefined;
    switch (filterType) {
      case '__all':
        break;
      case '__refund':
        showState = [
          EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND,
          EVENT_OFFER_KV_HOST_CONFIRMS_REFUND,
          EVENT_OFFER_KV_HOST_DENIES_REFUND,
          EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST,
          EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST_REFUNDED_DONE,
        ];
        break;
      case '__pending':
        showState = [EVENT_OFFER_KV_STATE_GUEST_REQUEST];
        break;
      case '__accepted':
        showState = [
          EVENT_OFFER_KV_STATE_HOST_CONFIRM,
          EVENT_OFFER_KV_STATE_GUEST_PAID,
        ];
        break;
      case '__cancelled':
        showState = [
          EVENT_OFFER_KV_STATE_HOST_CANCEL,
          EVENT_OFFER_KV_STATE_GUEST_CANCEL,
          EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL,
          EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL,
        ];
        break;
      case '__completed':
        showState = [
          EVENT_OFFER_KV_STATE_GUEST_PAID,
          EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT,
        ];
        showOfferState = [
          EVENT_STATE_FEEDBACK_CLOSE,
          EVENT_STATE_FEEDBACK_OPEN,
        ];
        break;
    }

    console.log(
      'selectFilter....offers size',
      this.props.eventKv.eventKvTxRequests.length,
    );
    console.log('selectFilter....offers showState', showState);
    console.log('selectFilter....offers showOfferState', showOfferState);

    let offers =
      showState !== undefined || showOfferState !== undefined
        ? (this.props.eventKv.eventKvTxRequests || []).filter(
            ({ eventOfferState, state }) => {
              let flag = true;
              if (showState !== undefined && showState.length > 0) {
                flag = flag && showState.indexOf(state) !== -1;
              }
              if (showOfferState !== undefined && showOfferState.length > 0) {
                flag = flag && showOfferState.indexOf(eventOfferState) !== -1;
              }

              return flag;
            },
          )
        : this.props.eventKv.eventKvTxRequests;

    console.log('selectFilter....offers after size', offers.length);

    this.setState({
      filterType,
      showState,
      showOfferState,
      offers,
      showToggleMenu: false,
    });
  };

  sortByDate(sortType) {
    this.setState({
      showMenu: false,
      sortType,
      offers: [...this.state.offers].reverse(),
    });
  }

  refundHandler = id => {
    console.log('refundHandler ', id);
    this.setState({
      showConfirmationModal: true,
      event_id: id,
      refundChangeState: true,
      confirmMessageType: '__refund',
      confirmMessage: '__dispute_conf',
    });
  };

  onPaymentStatus = data => {
    console.log('onPaymentStatus', data);
    if (data?.status === _SUCCESS) {
      this.setState({
        showResultModal: true,
        message: '__payment_success_event_request',
        messageType: _SUCCESS,
      });
    }
  };

  cancelHandler = (id, cancelState) =>
    this.setState({
      showConfirmationModal: true,
      event_id: id,
      nextState: cancelState,
      confirmMessageType: _CANCEL,
      confirmMessage: '__cancel_conf',
    });

  onPerformAction = () => {
    const { confirmMessageType, event_id, nextState, offers } = this.state;

    const event = offers.find(offer => offer.id === event_id); // this.getSelectedEvent();
    console.log(
      'onPerformAction confirmMessageType ',
      confirmMessageType,
      'event',
      event,
    );

    if (nextState !== -1) {
      this.updateEventOfferKv(event, nextState, 'cancel');
      this.setState({ nextState: -1 });
    } else {
      let nextDesiredState = -1;

      if (event) {
        switch (event.state) {
          case EVENT_OFFER_KV_STATE_HOST_CONFIRM: //probably not used
          case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
            nextDesiredState = EVENT_OFFER_KV_STATE_GUEST_CANCEL;
            break;
          case EVENT_OFFER_KV_STATE_GUEST_PAID:
            nextDesiredState = EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND;
            break;
          default:
            nextDesiredState = EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL;
        }
        console.log(
          'onPerformAction state ',
          event.state,
          ' next state ',
          nextDesiredState,
        );

        this.updateEventOfferKv(event, nextDesiredState, 'cancel');
      }
    }
  };

  // getSelectedEvent() {
  //   let event_id = this.state.event_id;
  //   let offers = this.state.offers;

  //   for (let i = 0; i < offers.length; i++) {
  //     let eventObj = offers[i];

  //     if (eventObj.id === event_id) {
  //       return eventObj;
  //     }
  //   }
  // }

  updateEventOfferKv(selectedEvent, nextState, message) {
    const { refundChangeState } = this.state;

    const obj = {
      id: selectedEvent.eventofferkvid,
      k: selectedEvent.k,
      v: selectedEvent.v,
      state: nextState,
      userId: selectedEvent.user,
      event: {
        id: selectedEvent.id,
      },
      refundState: EVENT_OFFER_KV_STATE_NO_REFUND,
      objectType: 'eventRequest',
      refundChangeState: refundChangeState,
    };
    console.log('updateEventOfferKv', obj);
    /**
     * this share action must know if the request consider changing refund state
     */
    if (refundChangeState) {
      obj.state = selectedEvent.state;
      obj.refundState = EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND;
    }

    this.setState({
      showModal: false,
      showConfirmationModal: false,
      refundChangeState: false,
      message,
    });
    this.props.updateEventOfferKv(obj);
  }

  componentDidMount() {
    this.focusSubscription = this.props.navigation.addListener(
      'focus',
      this.onFocus,
    );
    this.blurSubscription = this.props.navigation.addListener(
      'blur',
      this.onBlur,
    );
  }
  componentWillUnmount() {
    if (this.focusSubscription.remove !== undefined) {
      this.focusSubscription.remove();
    }
    if (this.blurSubscription.remove !== undefined) {
      this.blurSubscription.remove();
    }
  }

  dismissModal = () =>
    this.setState({ showModal: false, showDeleteModal: false });

  dismissCancelModal = () => this.setState({ showConfirmationModal: false });

  dismissResultModal = () => this.setState({ showResultModal: false });

  componentDidUpdate(prevProps) {
    if (
      prevProps.eventKv.fetchingAllTxRequest &&
      !this.props.eventKv.fetchingAllTxRequest
    ) {
      if (this.props.eventKv.errorAllTxRequest) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__server_error_get_data',
        });
      } else {
        this.setState({
          offers: this.props.eventKv.eventKvTxRequests.filter(
            e => e.k === EOKV_V_PARTICIPANTS,
          ),
        });
        if (
          this.props.eventKv.eventKvTxRequests.length > 0 &&
          this.props.eventKv.eventKvTxRequests.filter(
            e => e.state === EVENT_OFFER_KV_STATE_HOST_CONFIRM,
          ).length > 0
        ) {
          // console.log('getClientToken');
          // this.props.getClientToken();
        }
      }
    }
  }

  feedback = item => {
    console.log('open feedback with Id', item);
    this.props.navigation.navigate(REVIEW_ROUTE, {
      item,
      type: '__review',
    });
  };

  checkPaymentId() {
    let messageType = '',
      message = '',
      showResultModal = false,
      disablePayButton = false;
    if (this.props.account?.account?.phoneVerified !== PHONE_VERIFIED) {
      messageType = _WARNING;
      message = '__verify_user_needs';
      showResultModal = true;
      disablePayButton = true;
    }
    this.setState({ messageType, message, showResultModal, disablePayButton });
  }

  deleteEventOffer = () => {
    const { selectedEvent } = this.state;
    this.dismissModal();
    this.props.deleteEventOfferKv(selectedEvent.eventofferkvid);
  };

  getActionsHeader = () => {
    const cashRefundIcon = (
      <Icon.MaterialCommunityIcons size={15} name="cash-refund" />
    );
    const rateViewIcon = <Icon.MaterialIcons size={15} name="rate-review" />;
    const creditCardIcon = <Icon.Entypo size={15} name="credit-card" />;
    return (
      <View style={{ flexDirection: 'row', marginBottom: 10, float: 'right' }}>
        <ScrollView horizontal={true}>
          <Chip icon="check" style={{ marginRight: 5 }} color={colors.black}>
            {i18n.t('__accept')}
          </Chip>
          <Chip icon="cancel" style={{ marginRight: 5 }} color={colors.black}>
            {i18n.t('__cancel')}
          </Chip>
          <Chip
            icon={creditCardIcon}
            style={{ marginRight: 5 }}
            color={colors.black}>
            {i18n.t('__pay')}
          </Chip>
          <Chip
            icon={rateViewIcon}
            style={{ marginRight: 5 }}
            color={colors.black}>
            {i18n.t('__feedback')}
          </Chip>
          <Chip icon={cashRefundIcon} color={colors.black}>
            {i18n.t('__refund')}
          </Chip>
        </ScrollView>
      </View>
    );
  };

  onFocus = () => {
    this.setState({ focused: true });
    this.props.getYourRequests(0);
    this.checkPaymentId();
  };
  onBlur = () => {
    console.log('onDidBlur YourRequests');
    this.setState({
      focused: false,
      showResultModal: false,
      selectedId: undefined,
    });
  };

  renderFooter = () => {
    // If state is loading, return an activity indicator
    if (this.state.loading) {
      return (
        <View style={{ padding: 10 }}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      );
    } else {
      return null; // Return null if no more data to load
    }
  };

  render() {
    const {
      offers,
      message,
      messageType,
      focused,
      showResultModal,
      showDeleteModal,
      showConfirmationModal,
      confirmMessageType,
      confirmMessage,
      disablePayButton,
    } = this.state;
    const { fetchingAllTxRequest } = this.props.eventKv;
    const count = offers.length;

    return (
      <View
        style={{
          padding: 5,
          paddingBottom: 120,
          backgroundColor: colors.background,
        }}>
        <View>
          <View style={styles.chipContainer}>
            <Subheading style={globalStyles.elementCounter}>{`${
              count !== undefined ? count : 0
            } ${i18n.t('__requests')} `}</Subheading>
            <View style={{ flexDirection: 'row' }}>
              <Menu
                visible={this.state.showToggleMenu}
                onDismiss={() => this.toggleMenu(false)}
                anchor={
                  <Button
                    style={{ ...styles.menuButton, marginEnd: 10 }}
                    icon={getIcon(this.state.filterType)}
                    onPress={() => this.toggleMenu(true)}>
                    <Text style={styles.textButton}>
                      {i18n.t(this.state.filterType)}
                    </Text>
                  </Button>
                }>
                <Menu.Item
                  icon={getIcon('__all')}
                  onPress={() => this.selectFilter('__all')}
                  title={
                    <Text
                      style={
                        this.state.filterType === '__all' && { color: 'red' }
                      }>
                      {i18n.t('__all')}
                    </Text>
                  }
                />
                <Menu.Item
                  icon={getIcon('__pending')}
                  onPress={() => this.selectFilter('__pending')}
                  title={
                    <Text
                      style={
                        this.state.filterType === '__pending' && {
                          color: 'red',
                        }
                      }>
                      {i18n.t('__pending')}
                    </Text>
                  }
                />
                <Menu.Item
                  icon={getIcon('__refund')}
                  onPress={() => this.selectFilter('__refund')}
                  title={
                    <Text
                      style={
                        this.state.filterType === '__refund' && {
                          color: 'red',
                        }
                      }>
                      {i18n.t('__refund')}
                    </Text>
                  }
                />
                <Menu.Item
                  icon={getIcon('__accepted')}
                  onPress={() => this.selectFilter('__accepted')}
                  title={
                    <Text
                      style={
                        this.state.filterType === '__accepted' && {
                          color: 'red',
                        }
                      }>
                      {i18n.t('__accepted')}
                    </Text>
                  }
                />
                <Menu.Item
                  icon={getIcon('__cancelled')}
                  onPress={() => this.selectFilter('__cancelled')}
                  title={
                    <Text
                      style={
                        this.state.filterType === '__cancelled' && {
                          color: 'red',
                        }
                      }>
                      {i18n.t('__cancelled')}
                    </Text>
                  }
                />
                <Menu.Item
                  icon={getIcon('__completed')}
                  onPress={() => this.selectFilter('__completed')}
                  title={
                    <Text
                      style={
                        this.state.filterType === '__completed' && {
                          color: 'red',
                        }
                      }>
                      {i18n.t('__completed')}
                    </Text>
                  }
                />
              </Menu>
              <Menu
                visible={this.state.showMenu}
                onDismiss={this.closeMenu.bind(this)}
                anchor={
                  <Button
                    style={styles.menuButton}
                    icon="sort"
                    onPress={this.openMenu.bind(this)}>
                    <Text style={styles.textButton}>{i18n.t('__sort')}</Text>
                  </Button>
                }>
                <Menu.Item
                  icon="sort-ascending"
                  onPress={() => {
                    this.sortByDate('asc');
                  }}
                  title={
                    <Text
                      style={this.state.sortType === 'asc' && { color: 'red' }}>
                      {i18n.t('__sort_asc')}
                    </Text>
                  }
                />
                <Menu.Item
                  icon="sort-descending"
                  onPress={() => {
                    this.sortByDate('desc');
                  }}
                  title={
                    <Text
                      style={
                        this.state.sortType === 'desc' && { color: 'red' }
                      }>
                      {i18n.t('__sort_desc')}
                    </Text>
                  }
                />
              </Menu>
            </View>
          </View>
          {/* {this.getActionsHeader()} */}
          {offers.length === 0 ? (
            <View style={globalStyles.container}>
              <Icon.MaterialCommunityIcons
                name="inbox-arrow-up"
                size={80}
                color={colors.black}
              />
              <Text style={{ color: colors.black }}>
                {i18n.t('__no_requests')}
              </Text>
            </View>
          ) : (
            <FlatList
              showsVerticalScrollIndicator={false}
              data={offers}
              refreshControl={
                <RefreshControl
                  refreshing={fetchingAllTxRequest}
                  onRefresh={() => this.props.getYourRequests(0)}
                />
              }
              onEndReached={() => this.loadMore()}
              onEndReachedThreshold={0.5}
              style={{ height: '100%' }}
              keyExtractor={(item, index) => index.toString()}
              ListFooterComponent={this.renderFooter}
              renderItem={({ item }) => (
                <MyOffer
                  key={item.id}
                  {...item}
                  selected={this.state.selectedId}
                  item={item}
                  disablePayButton={disablePayButton}
                  cancelHandler={this.cancelHandler}
                  onPaymentStatus={this.onPaymentStatus}
                  feedback={() => this.feedback(item)}
                  refund={this.refundHandler}
                  deleteDialog={this.showDeleteDialog}
                  navigation={this.props.navigation}
                  loggedIn={this.props.loggedIn}
                />
              )}
            />
          )}
        </View>

        <Portal>
          <Dialog
            visible={focused && showConfirmationModal}
            onDismiss={this.dismissCancelModal}
            style={{ borderWidth: 2, borderColor: colors.warning }}>
            <View style={{ flexDirection: 'row', padding: 20 }}>
              <Icon.Entypo size={64} color={colors.warning} name="warning" />
              <Dialog.Title>{`${i18n.t(confirmMessageType)}  `}</Dialog.Title>
            </View>
            <Dialog.Content>
              <Paragraph>{i18n.t(confirmMessage)}</Paragraph>
            </Dialog.Content>
            <Dialog.Actions>
              <Button icon="cancel" onPress={this.dismissCancelModal}>
                {i18n.t('__no')}
              </Button>
              <Button icon="check" onPress={this.onPerformAction}>
                {i18n.t('__yes')}
              </Button>
            </Dialog.Actions>
          </Dialog>
        </Portal>

        <AlertDialog
          toCancel={() => this.dismissModal()}
          onClose={this.deleteEventOffer}
          alert={{
            visible: showDeleteModal && this.state.focused,
            message: message + '_' + messageType.substring(2) + '_event_offer',
            messageType: messageType,
          }}
        />

        <AlertDialog
          onClose={this.dismissResultModal}
          alert={{
            visible: focused && showResultModal,
            message: message,
            messageType: messageType,
          }}
        />
      </View>
    );
  }
}

const mapStateToProps = appState => {
  return {
    eventKv: appState.eventkv,
    payment: appState.payment,
    account: appState.account,
    loggedIn: isLoggedIn(appState.account),
  };
};
function mapDispatchToProps(dispatch) {
  return {
    // getClientToken: () => dispatch(PaymentActions.paymentTokenRequest()),
    getYourRequests: page => dispatch(EventKVActions.eventKvAllTxRequest(page)),
    updateEventOfferKv: eventKv =>
      dispatch(EventKVActions.eventKvUpdateRequest({ eventKv, after: 'tx' })),
    deleteEventOfferKv: eventKvId =>
      dispatch(EventKVActions.eventKvDeleteRequest({ eventKvId, after: 'tx' })),
  };
}
export default connect(mapStateToProps, mapDispatchToProps)(YourRequests);

const styles = StyleSheet.create({
  chipContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
    paddingLeft: 10,
  },
  menuButton: {
    backgroundColor: '#fafafa',
    borderColor: '#e9e9e9',
    borderWidth: 1,
  },
  textButton: {
    fontSize: 12,
    color: colors.black,
  },
});
