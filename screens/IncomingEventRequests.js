import React, { Component } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  FlatList,
  RefreshControl,
} from 'react-native';
import { Text, Chip, Subheading, Button, Menu } from 'react-native-paper';
import * as Icon from '@expo/vector-icons';
import { connect } from 'react-redux';
import {
  colors,
  _SUCCESS,
  _ERROR,
  getIcon,
  EVENT_OFFER_KV_HOST_DENIES_REFUND,
  EVENT_OFFER_KV_HOST_CONFIRMS_REFUND,
  _CONFIRM,
  globalStyles,
  _WARNING,
  _DELETE,
  _CANCEL,
  checkBankAccount,
  checkKycValidatedStatus,
  checkPhoneNumber,
  EOKV_V_PARTICIPANTS,
} from '@constants';
import i18n from '@translations/index';
import EventKVActions from '@reducers/eventKv.reducer';
import AlertDialog from '@components/AlertDialog';
import { FEEDBACK_ROUTE } from '@routes/route_constants';
import { Offer } from './Offer';

class IncomingEventRequests extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showModal: false,
      showErrorAlertModal: false,
      showDeleteModal: false,
      errorMessage: '',
      errorMessageType: '',
      message: '',
      messageType: '',
      modalText: '__accept',
      eventKvRxRequestsSorted: ([] = []),
      selectedEvent: null,
      showMenu: false,
      sortType: 'desc',
      selectedId: props.route.params?.eokvid,
    };
    console.log('IncomingEventRequests selectedId ', this.state.selectedId);
  }

  componentDidMount() {
    this.focusSubscription = this.props.navigation.addListener(
      'focus',
      this.onFocus,
    );
    this.blurSubscription = this.props.navigation.addListener(
      'blur',
      this.onBlur,
    );
  }
  componentWillUnmount() {
    if (this.focusSubscription.remove !== undefined) {
      this.focusSubscription.remove();
    }
    if (this.blurSubscription.remove !== undefined) {
      this.blurSubscription.remove();
    }
  }

  openMenu() {
    this.setState({ showMenu: true });
  }

  closeMenu() {
    this.setState({ showMenu: false });
  }

  sortByDate(sortType) {
    this.setState({
      showMenu: false,
      sortType,
      eventKvRxRequestsSorted: [
        ...this.state.eventKvRxRequestsSorted,
      ].reverse(),
    });
  }

  UNSAFE_componentWillMount() {
    this.setState({ destroyModal: true });
  }

  componentDidUpdate(prevProps) {
    if (
      prevProps.eventKv.fetchingAllRxRequest &&
      !this.props.eventKv.fetchingAllRxRequest
    ) {
      if (this.props.eventKv.errorAllRxRequest) {
        this.setState({
          showErrorAlertModal: true,
          errorMessageType: _ERROR,
          errorMessage: '__server_error_get_data',
        });
      } else {
        this.setState({
          eventKvRxRequestsSorted: this.props.eventKv.eventKvRxRequests.filter(
            e => e.k === EOKV_V_PARTICIPANTS,
          ),
        });
      }
    }
    if (prevProps.eventKv.updating && !this.props.eventKv.updating) {
      if (this.props.eventKv.errorUpdating) {
        this.setState({
          showErrorAlertModal: true,
          errorMessageType: _ERROR,
          errorMessage: this.props.eventKv.errorUpdating.errorKey, //TODO: check if it is .title
        });
      }
    }
    if (prevProps.eventKv.deleting && !this.props.eventKv.deleting) {
      if (this.props.eventKv.errorDeleting) {
        this.setState({
          showErrorAlertModal: true,
          errorMessageType: _ERROR,
          errorMessage: this.props.eventKv.errorDeleting.errorKey, //TODO: check if it is .title
        });
      }
    }
  }

  showRefundDialogWithAlternatives = (text, event) => {
    this.setState({
      showModalWithAlternative: true,
      modalText: text,
      selectedEvent: event,
      messageType: _CONFIRM,
    });
  };

  showDialog = (text, event, nextEokvState) => {
    console.log('showDialog', text, event, nextEokvState);
    this.setState({
      showModal: true,
      modalText: text,
      selectedEvent: event,
      messageType: _CONFIRM,
      nextEokvState,
    });
  };

  showDeleteDialog = (text, event) => {
    this.setState({
      showDeleteModal: true,
      modalText: text,
      selectedEvent: event,
      messageType: _DELETE,
    });
  };

  feedback = item => {
    console.log('feedback', item);
    this.props.navigation.navigate(FEEDBACK_ROUTE, {
      item,
      type: '__feedback',
    });
  };

  /**
   *
   * @param {*} nextState is passed only in refund action flow
   * @param {*} isDismissed
   */
  handleEventOffer = (nextRefundState = null) => {
    const { selectedEvent, nextEokvState } = this.state;
    console.log('handleEventOffer nextEokvState ===>', nextEokvState);
    console.log('handleEventOffer nextRefundState ===>', nextRefundState);
    console.log('handleEventOffer selectedEvent ===>', selectedEvent);
    const obj = {
      id: selectedEvent.eventofferkvid,
      k: selectedEvent.k,
      v: selectedEvent.v,
      state: nextEokvState ?? selectedEvent.state,
      userId: selectedEvent.user_id,
      event: {
        id: selectedEvent.id,
      },
      objectType: 'eventOffer',
      refundChangeState: false,
      stateHost: 0,
    };
    if (nextRefundState !== null) {
      obj.refundChangeState = true;
      obj.refundState = nextRefundState;
    }

    this.dismissModal();

    console.log('handleEventOffer obj ===>', obj);

    this.props.updateEventOfferKv(obj);
  };

  deleteEventOffer = () => {
    const { selectedEvent } = this.state;
    console.log('deleteEventOffer obj ===>', selectedEvent.eventofferkvid);

    this.dismissModal();
    this.props.deleteEventOfferKv(selectedEvent.eventofferkvid);
  };

  dismissModal = () =>
    this.setState({
      showModal: false,
      showModalWithAlternative: false,
      showDeleteModal: false,
    });

  checkUserStatus = () => {
    const { userWallet } = this.props.payment;
    const { account } = this.props.account;
    // console.log('checkUserStatus account', account);
    // console.log('checkUserStatus userWallet', userWallet);
    let errorMessage = [{ text: '__verify_host_needs' }];

    //it includes the user's userwallet
    if (!checkPhoneNumber(account)) {
      errorMessage.push({ text: '__phone_number' });
    }
    if (!checkBankAccount(userWallet)) {
      errorMessage.push({ text: '__bank_account' });
    }
    if (!checkKycValidatedStatus(userWallet)) {
      errorMessage.push({ text: '__kyc_onboard' });
    }

    console.log('checkUserStatus errorMessage', errorMessage);
    this.setState({
      errorMessageType: _WARNING,
      errorMessage,
      showErrorAlertModal: errorMessage.length > 1 ? true : false,
    });
  };

  getActionsHeader = () => {
    return (
      <View style={{ flexDirection: 'row', marginBottom: 10 }}>
        <ScrollView horizontal={true}>
          <Chip
            icon={getIcon('__accept')}
            style={{ marginRight: 5 }}
            color={colors.success}>
            {i18n.t('__accept')}
          </Chip>
          <Chip icon={getIcon('__cancel')} style={{ marginRight: 5 }}>
            {i18n.t('__cancel')}
          </Chip>
          <Chip
            icon={() => <Icon.MaterialIcons size={15} name="rate-review" />}
            style={{ marginRight: 5 }}>
            {i18n.t('__feedback')}
          </Chip>
          <Chip icon={getIcon('__refund_verify')}>
            {i18n.t('__refund_check')}
          </Chip>
        </ScrollView>
      </View>
    );
  };

  getSelectedItem = () => {
    if (!this.state.selectedId) {
      return null;
    }

    console.log(
      'getSelectedItem ',
      this.state.eventKvRxRequestsSorted.filter(
        e => e.id === this.state.selectedId,
      )[0],
    );
    return this.state.eventKvRxRequestsSorted.filter(
      e => e.id === this.state.selectedId,
    )[0];
  };

  onFocus = () => {
    this.setState({ focused: true });
    this.props.getAllEventOfferRequests();
    this.checkUserStatus();
  };
  onBlur = () => {
    this.setState({
      focused: false,
      showErrorAlertModal: false,
      selectedId: undefined,
    });
  };

  render() {
    const {
      modalText,
      showDeleteModal,
      showModal,
      showModalWithAlternative,
      showErrorAlertModal,
      errorMessage,
      errorMessageType,
      messageType,
      eventKvRxRequestsSorted,
    } = this.state;

    // console.log('>>eventKvRxRequestsSorted', eventKvRxRequestsSorted);

    const { fetchingAllRxRequest } = this.props.eventKv;

    return (
      <View
        style={{
          padding: 5,
          paddingBottom: 100,
          backgroundColor: colors.background,
        }}>
        <View>
          <View style={globalStyles.chipContainer}>
            <Subheading style={globalStyles.elementCounter}>
              {eventKvRxRequestsSorted !== undefined
                ? eventKvRxRequestsSorted.length
                : 0}{' '}
              {i18n.t('__requests')}
            </Subheading>
            <View>
              <Menu
                visible={this.state.showMenu}
                onDismiss={this.closeMenu.bind(this)}
                anchor={
                  <Button
                    style={styles.menuButton}
                    icon="sort"
                    onPress={this.openMenu.bind(this)}>
                    <Text style={styles.textButton}>{i18n.t('__sort')}</Text>
                  </Button>
                }>
                <Menu.Item
                  icon="sort-ascending"
                  onPress={() => {
                    this.sortByDate('asc');
                  }}
                  title={
                    <Text
                      style={this.state.sortType === 'asc' && { color: 'red' }}>
                      {i18n.t('__sort_asc')}
                    </Text>
                  }
                />
                <Menu.Item
                  icon="sort-descending"
                  onPress={() => {
                    this.sortByDate('desc');
                  }}
                  title={
                    <Text
                      style={
                        this.state.sortType === 'desc' && { color: 'red' }
                      }>
                      {i18n.t('__sort_desc')}
                    </Text>
                  }
                />
              </Menu>
            </View>
          </View>
          {/* {this.getActionsHeader()} */}
          {eventKvRxRequestsSorted.length === 0 ? (
            <View style={globalStyles.container}>
              <Icon.MaterialCommunityIcons
                name="inbox-arrow-down"
                color={colors.black}
                size={80}
              />
              <Text style={{ color: colors.black }}>
                {i18n.t('__no_requests')}
              </Text>
            </View>
          ) : (
            <FlatList
            // clone YourRequest to load more item dinamyically
              showsVerticalScrollIndicator={false}
              data={eventKvRxRequestsSorted}
              style={{ height: '100%' }}
              refreshControl={
                <RefreshControl
                  refreshing={fetchingAllRxRequest}
                  onRefresh={() => this.props.getAllEventOfferRequests()}
                />
              }
              keyExtractor={(item, index) => index.toString()}
              // scrollToItem={this.getSelectedItem()}
              // scrollToIndex={{ index: 0.5 }}
              renderItem={item => (
                <Offer
                  key={item.id}
                  selected={this.state.selectedId}
                  navigate={this.props.navigation.navigate}
                  push={this.props.navigation.push}
                  showDialog={this.showDialog}
                  feedback={this.feedback}
                  refundDialog={this.showRefundDialogWithAlternatives}
                  deleteDialog={this.showDeleteDialog}
                  {...item}
                />
              )}
            />
          )}
        </View>

        <AlertDialog
          toCancel={this.dismissModal}
          onClose={this.deleteEventOffer}
          alert={{
            visible: showDeleteModal && this.state.focused,
            message:
              modalText + '_' + messageType.substring(2) + '_event_offer',
            messageType: messageType,
          }}
        />

        <AlertDialog
          toCancel={
            messageType === _ERROR || messageType === _SUCCESS
              ? undefined
              : () => this.dismissModal()
          }
          onClose={
            messageType === _ERROR || messageType === _SUCCESS
              ? () => this.dismissModal()
              : () => this.handleEventOffer()
          }
          alert={{
            visible: showModal && this.state.focused,
            message:
              modalText + '_' + messageType.substring(2) + '_event_offer',
            messageType: messageType,
          }}
        />

        {/*
          Alert dialog for refund's choice: yes and no trigger different actions,
          it needs the next refund definition
        */}
        <AlertDialog
          toCancel={() =>
            this.handleEventOffer(EVENT_OFFER_KV_HOST_DENIES_REFUND)
          }
          onClose={() =>
            this.handleEventOffer(EVENT_OFFER_KV_HOST_CONFIRMS_REFUND)
          }
          alert={{
            visible: showModalWithAlternative && this.state.focused,
            message:
              modalText + '_' + messageType.substring(2) + '_event_offer',
            messageType: messageType,
          }}
        />

        <AlertDialog
          onClose={() => this.setState({ showErrorAlertModal: false })}
          alert={{
            visible: showErrorAlertModal,
            message: errorMessage,
            messageType: errorMessageType,
          }}
        />
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    eventKv: appState.eventkv,
    payment: appState.payment,
    account: appState.account,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    getAllEventOfferRequests: () =>
      dispatch(EventKVActions.eventKvAllRxRequest()),
    updateEventOfferKv: eventKv =>
      dispatch(EventKVActions.eventKvUpdateRequest({ eventKv, after: 'rx' })),
    deleteEventOfferKv: eventKvId =>
      dispatch(EventKVActions.eventKvDeleteRequest({ eventKvId, after: 'rx' })),
  };
}
export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(IncomingEventRequests);

const styles = StyleSheet.create({
  menuButton: {
    backgroundColor: '#fafafa',
    borderColor: '#e9e9e9',
    borderWidth: 1,
  },
  element: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textButton: {
    fontSize: 12,
    color: colors.black,
  },
});
