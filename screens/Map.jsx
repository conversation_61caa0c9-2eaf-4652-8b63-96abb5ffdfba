import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { View, Image } from 'react-native';
import {
  colors,
  getBoundedRegion,
  getBoundedRegionForCoords,
  getLocationPermission,
  icon_size_medium,
  images,
} from '@constants';

import MapView, { Marker, Circle } from 'react-native-maps';
import { EVENT_DETAILS_ROUTE } from '@routes/route_constants';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useDispatch, useSelector } from 'react-redux';
import EventMapCard from './component/EventMapCard';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import { isLoggedIn } from '@reducers/account.reducer';
import { useNavigation } from '@react-navigation/native';

const ZOOM = 0.009;

const Map = props => {
  const mapViewRef = useRef(null);
  const rbSheetRef = useRef(null);

  const navigation = useNavigation();
  const searchedEvents = useSelector(state => state.searchedEvents);
  const receivedEvents = useMemo(
    () => searchedEvents?.events || [],
    [searchedEvents],
  );

  const [stateRegion, setRegion] = useState(null);
  const [events, setEvents] = useState(receivedEvents);
  const [markers, setMarkers] = useState([]);
  const [item, setItem] = useState({});
  const [currentIndex, setCurrentIndex] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(8);
  const [rbHeight, setRbHeight] = useState(hp('45%'));

  const addMarkers = useCallback(() => {
    const nextMarkers = [];

    if (events !== null && events !== undefined) {
      events.forEach(element => {
        console.log('element item is', element.objectID);
        const nextItem = {
          id: element.objectID,
          item: element,
          latlng: {
            latitude: element.lat,
            longitude: element.lon,
          },
        };
        nextMarkers.push(nextItem);
        console.log('map nextItem is', nextItem);
      });
    }

    const nextIndex = 0;
    console.log('nextMarkers are', nextMarkers);
    console.log('item is', nextMarkers[nextIndex]);
    setMarkers(nextMarkers); //TODO trigger error here !!!
    setCurrentIndex(nextIndex);
    if (nextMarkers.length > 0) {
      markerClick(nextMarkers[nextIndex]);
    }
  }, []);

  const markerClick = e => {
    if (e === undefined) {
      return;
    }
    // console.log('Marker was clicked', e);
    setItem(e.item);
    // this.setState({ item: e.item });
    rbSheetRef?.current.open();
    const nextCenter = Object.assign({}, e.latlng);

    // console.log("Marker was zoomLevel", zoomLevel);
    // switch (zoomLevel) {
    //   case 14:
    //   case 13:
    //   case 12:
    //   case 11:
    //   case 10:
    //     nextCenter.latitude -= 0.01
    //     break
    //   case 9:
    //   case 8:
    //   case 7:
    //     nextCenter.latitude -= 0.2
    //     break
    //   case 6:
    //   case 5:
    //   case 4:
    //     nextCenter.latitude -= 2
    //     break
    //   case 3:
    //     nextCenter.latitude -= 15
    //     break
    //   case 2:
    //   case 1:
    //     nextCenter.latitude -= 25
    //     break
    //   default:
    //     nextCenter.latitude -= 2
    // }

    // nextCenter.latitudeDelta = 1
    // nextCenter.longitudeDelta = 1
    // nextCenter.latitudeDelta = 0.09,
    // nextCenter.longitudeDelta = 0.04,
    // this._mapView.animateToRegion(nextCenter);
    console.log('nextCenter', nextCenter);
    console.log(
      'getBoundedRegion',
      getBoundedRegionForCoords(nextCenter.latitude, nextCenter.longitude),
    );
    mapViewRef?.current.animateToRegion(nextCenter);
  };

  const goPrevious = () => {
    const nextIndex = currentIndex === 0 ? events.length - 1 : currentIndex - 1;
    setCurrentIndex(nextIndex);
    markerClick(markers[nextIndex]);
  };

  const goNext = () => {
    const nextIndex = currentIndex === events.length - 1 ? 0 : currentIndex + 1;
    setCurrentIndex(nextIndex);
    markerClick(markers[nextIndex]);
  };

  const showEventDetail = () => {
    rbSheetRef?.current.close();
    navigation.navigate(EVENT_DETAILS_ROUTE, { id: item.objectID });
  };

  const checkRegion = region => {
    const zoom = Math.round(Math.log(360 / region.longitudeDelta) / Math.LN2);
    console.log('checkRegion ', region, 'zoom ', zoom);
    setZoomLevel(zoom);
  };

  useEffect(() => {
    console.log('Map props', props);
    console.log('Map receivedEvents', receivedEvents);
    const region = getBoundedRegion(receivedEvents);
    setRegion(region);

    async function fetchData() {
      await getLocationPermission();
    }

    fetchData();
    addMarkers();
  }, [addMarkers, props, receivedEvents]);

  return (
    <View style={{ flex: 1 }}>
      <MapView
        ref={mapViewRef}
        showsCompass
        showsUserLocation
        showsMyLocationButton
        zoomEnabled={true}
        zoomControlEnabled
        provider="google"
        moveOnMarkerPress
        initialRegion={stateRegion}
        minZoomLevel={3}
        maxZoomLevel={14}
        onRegionChangeComplete={checkRegion}
        style={{ flex: 1 }}>
        {markers &&
          markers.map((eventOffer, i) => (
            <View key={'V_' + i}>
              <Marker
                coordinate={eventOffer.latlng}
                key={'M_' + i}
                opacity={1}
                calloutOffset={{ x: 0.35, y: -0.1 }}
                calloutAnchor={{ x: 0.35, y: -0.1 }}
                onPress={() => markerClick(eventOffer)}
                onSelect={() => markerClick(eventOffer)}>
                <Image
                  source={images._map_marker}
                  style={{
                    height: icon_size_medium,
                    width: icon_size_medium,
                  }}
                />
              </Marker>
              <Circle
                center={eventOffer.latlng}
                radius={5000}
                zIndex={20}
                fillColor="rgba(250,0,0,0.3)"
                strokeColor="red"
                strokeWidth={0.5}
                key={'C_' + i}
              />
            </View>
          ))}
      </MapView>
      <RBSheet
        ref={rbSheetRef}
        closeOnDragDown={true}
        closeOnPressMask={true}
        height={rbHeight}
        customStyles={{
          wrapper: {
            backgroundColor: 'transparent',
          },
          draggableIcon: {
            backgroundColor: '#000',
          },
          container: {
            backgroundColor: colors.background,
            borderTopRightRadius: 25,
            borderTopLeftRadius: 25,
            borderTopColor: colors.primary,
            borderLeftColor: colors.primary,
            borderRightColor: colors.primary,
            borderTopWidth: 2,
            borderLeftWidth: 0.5,
            borderRightWidth: 0.5,
            overflow: 'hidden',
          },
        }}>
        <EventMapCard
          item={item}
          loggedIn={useSelector(isLoggedIn)}
          goPrevious={goPrevious}
          goNext={goNext}
          showEventDetail={showEventDetail}
          totalCount={searchedEvents.totalCount}
          currentIndex={currentIndex + 1}
          navigation={navigation}
        />
      </RBSheet>
    </View>
  );
};

export default Map;
