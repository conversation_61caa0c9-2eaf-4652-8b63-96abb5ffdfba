import React, { Component } from 'react';
import { View } from 'react-native';
import { Headline, TextInput, Button } from 'react-native-paper';

import { colors, _ERROR, FEEDBACK_GUEST_2_HOST, _SUCCESS } from '@constants';
import StarRating from 'react-native-star-rating';
import i18n from '@translations/index';
import FeedbackActions from '@reducers/feedbacks.reducer';
import AlertDialog from '@components/AlertDialog';
import { connect } from 'react-redux';
import { getLogin } from '@reducers/account.reducer';
import { feedbackSchema } from '@constants/schemas';

class FeedbackComponent extends Component {

  constructor(props) {
    super(props);
    console.log('FeedbackComponent>>', props);
    this.state = {
      rating: null,
      showAlertModal: false,
      messageType: '',
      message: '',

      isNotFilled: true,
      description: '',
      point: -1,
      item: props.route.params.item,
      type: props.route.params.type,
      form: {
        ...feedbackSchema,
      },
    };
  }

  componentDidUpdate = prevProps => {
    if (
      this.props.feedbacks.updating === false &&
      prevProps.feedbacks.updating === true
    ) {
      console.log('componentDidUpdate ', this.props.feedbacks.errorUpdating);
      if (this.props.feedbacks.errorUpdating) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: this.props.feedbacks.errorUpdating.title,
        });
      } else {
        this.setState({
          showAlertModal: true,
          messageType: _SUCCESS,
          message: '__feedback_created_ok',
          navigateBack: true,
        });
      }
    }
  };

  serverRequest = () => {
    console.log('server request', this.state.item);
    const feedback = {
      description: this.state.description,
      eventOffer: {
        id: this.state.item.id,
      },
      type: FEEDBACK_GUEST_2_HOST,
      point: this.state.rating * 100,
      senderId: this.props.username,
      receiverId: this.state.item.eokvuserid,
    };
    this.props.createFeedback(feedback);
  };

  setRating = rating => {
    const isNotFilled =
      this.state.description.length > 5 && rating !== null ? false : true;
    this.setState({ rating, isNotFilled });
  };

  setDescription = description => {
    const isNotFilled =
      description.length > 5 && this.state.rating !== null ? false : true;
    this.setState({ description, isNotFilled });
  };

  render() {
    const { showAlertModal, message, messageType, isNotFilled } = this.state;
    const { updating } = this.props.feedbacks;

    return (
      <View
        style={{
          backgroundColor: colors.background,
          padding: 20,
          flex: 1,
          alignItems: 'center',
        }}>
        <Headline>{i18n.t('__rate_guest')}</Headline>
        <View style={{ width: 160, marginVertical: 20 }}>
          <StarRating
            starSize={25}
            disabled={false}
            maxStars={5}
            rating={this.state.rating}
            fullStarColor={colors.fullStar}
            selectedStar={rating => this.setRating(rating)}
          />
        </View>
        <TextInput
          label={i18n.t('__your_comment')}
          placeholder={i18n.t('__your_comment_placeholder_host')}
          onChangeText={description => this.setDescription(description)}
          multiline
          theme={{ colors: { primary: colors.blue } }}
          style={{ width: '100%' }}
        />

        <Button
          mode="contained"
          loading={updating}
          disabled={updating || isNotFilled}
          onPress={this.serverRequest}
          contentStyle={{ padding: 5 }}
          style={{ width: '100%', marginTop: 10 }}>
          {updating ? i18n.t('__sending') : i18n.t('__send')}
        </Button>
        <AlertDialog
          onClose={() => {
            console.log('navigateBack>>', this.state);
            console.log('this.props.navigation>>', this.props.navigation);
            if (this.state.navigateBack) {
              this.props.navigation.goBack();
            }
            this.setState({ showAlertModal: false, navigateBack: false });
          }}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
      </View>
    );
  }
}

const mapStateToProps = appState => {
  return {
    feedbacks: appState.feedbacks,
    username: getLogin(appState.account),
  };
};
function mapDispatchToProps(dispatch) {
  return {
    createFeedback: feedback =>
      dispatch(FeedbackActions.feedbackUpdateRequest(feedback)),
  };
}
export const Feedback = connect(
  mapStateToProps,
  mapDispatchToProps,
)(FeedbackComponent);
