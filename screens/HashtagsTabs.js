import { View, StyleSheet, Text } from 'react-native';
import React from 'react';
import { createBottomTabNavigator } from 'react-navigation-tabs';
import Ionicons from 'react-native-vector-icons/Ionicons';
import IconWithBadge from '@components/IconWithBadge';
import { colors } from '@constants';
import Hashtags from './Hashtags';

const HomeIconWithBadge = props => {
  return <IconWithBadge {...props} badgeCount={3} />;
};

class Active extends React.Component {
  render() {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text style={{ color: colors.black }}>Home</Text>
      </View>
    );
  }
}

// const HashtagsNavigator = createStackNavigator({ Hashtags: { screen: Hashtags, path: RoutesPath.DRAWER_SEARCH_ROUTE } })

const HashtagsTabs = createBottomTabNavigator(
  {
    Active: {
      screen: Active,
    },
    Hashtags: {
      screen: Hashtags,
      navigationOptions: {
        header: null,
      },
    },
    // Settings: {
    //   screen: SettingsScreen,
    //   navigationOptions: {
    //     header: null,
    //   },
    // },
  },
  {
    defaultNavigationOptions: ({ navigation }) => ({
      // title: 'Active',
      // headerTitle: 'headerTitle',
      // tabBarLabel: 'ciao',

      swipeEnabled: true,
      adaptive: true,
      tabBarIcon: ({ horizontal, tintColor }) => {
        const { routeName } = navigation.state;
        let IconComponent = Ionicons;
        let iconName;
        if (routeName === 'Active') {
          iconName = 'ios-home';
        } else if (routeName === 'Hashtags') {
          iconName = 'add-circle-sharp';
          IconComponent = HomeIconWithBadge;
        } else if (routeName === 'Settings') {
          iconName = 'ios-settings';
        }

        return (
          <IconComponent
            name={iconName}
            size={horizontal ? 20 : 25}
            color={tintColor}
          />
        );
      },
    }),
    tabBarOptions: {
      activeTintColor: 'white',
      inactiveTintColor: 'gray',
      style: {
        backgroundColor: colors.primary,
      },
    },
  },
);

// static navigationOptions = props => {
//   console.log('constructor navigatio', props.navigation.state.params.tag)
//   const title = 'Tag: ' + props.navigation.state.params && props.navigation.state.params.tag === undefined ? props.navigation.state.params.tag : '';
//   return {
//     title,
//     headerStyle: {
//       backgroundColor: colors.primary
//     },
//     headerTintColor: "white",
//     hasBack: true
//   }
// };

export default HashtagsTabs;

const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    backgroundColor: colors.secondary,
    color: 'white',
    zIndex: 100,
    elevation: 50,
    margin: 20,
    right: 0,
    bottom: 0,
  },
  label: {
    borderRadius: 10,
    borderWidth: 1,
    paddingLeft: 5,
    paddingRight: 5,
    backgroundColor: colors.whiteBackground,
    overflow: 'hidden',
  },
  surface: {
    backgroundColor: 'white',
    elevation: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
});
