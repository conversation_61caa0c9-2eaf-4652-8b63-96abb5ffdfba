import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  Image,
} from 'react-native';
import {
  colors,
  _ERROR,
  intollerances as intollerantsArray,
  getIconFromArray,
  globalStyles,
  PARAMETER_PREFERENCE_INTOLLERANCE,
  PARAMETER_PREFERENCE_BADGE,
  PARAMETER_PREFERENCE_USER_DESCRIPTION,
  PARAMETER_PREFERENCE_USER_GENDER,
  getAvatarOrDummy,
  defaultDateFormat,
  icon_size_chip,
} from '@constants';
import { Avatar, Chip, Button } from 'react-native-paper';
import { useDispatch, useSelector } from 'react-redux';

import AlertDialog from '@components/AlertDialog';
import moment from 'moment';
import i18n from '@translations/index';
import Field from './component/Field';
import CommonActions from '@reducers/common.reducer';
import FeedbackActions from '@reducers/feedbacks.reducer';
import Ratings from './component/Ratings';
import { MY_EVENT_ROUTE } from '@routes/route_constants';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';

const UserInfo = () => {
  const [alert, setAlert] = useState({
    visible: false,
    message: '',
    messageType: '',
  });
  const [initialLoading, setInitialLoading] = useState(true);

  const dispatch = useDispatch();
  const route = useRoute();
  const navigation = useNavigation();
  const common = useSelector(appState => appState.common);

  const userInfoData =
    common.userInfo && common.userInfo.user ? common.userInfo.user : {};
  const userPreferences =
    common.userInfo && common.userInfo.properties
      ? common.userInfo.properties
      : [];

  const intollerances = userPreferences.filter(item =>
    item.k.includes(PARAMETER_PREFERENCE_INTOLLERANCE),
  );
  const user_badges = userPreferences.filter(item =>
    item.k.includes(PARAMETER_PREFERENCE_BADGE),
  );
  const user_description = userPreferences.filter(
    item => item.k === PARAMETER_PREFERENCE_USER_DESCRIPTION,
  );
  const user_birthday = userInfoData.dob
    ? moment(userInfoData.dob).format(defaultDateFormat)
    : '';
  const user_since = userInfoData.createdIn
    ? moment(userInfoData.createdIn).format(defaultDateFormat)
    : '';
  const user_gender = userPreferences.filter(
    item => item.k === PARAMETER_PREFERENCE_USER_GENDER,
  );

  const chips = intollerances.map((prop, key) => (
    <Chip style={{ marginRight: 10, marginTop: 5 }} key={key}>
      <Image
        style={{ width: icon_size_chip, height: icon_size_chip }}
        source={getIconFromArray(intollerantsArray, prop.v)}
      />{' '}
      {i18n.t(prop.v)}
    </Chip>
  ));

  const badges = user_badges.map((badge, key) => (
    <Chip style={globalStyles.badge} key={key}>
      {i18n.t(badge.v)}
    </Chip>
  ));

  useFocusEffect(
    useCallback(() => {
      let isActive = true;
      // console.log('UserInfo useFocusEffect', route.params);
      // console.log('UserInfo common', common);
      const { userInfoId: currentUserId } = route.params;
      const getUserData = () => {
        const { userInfoId } = common;
        // console.log(
        //   'UserInfo getUserData ',
        //   currentUserId,
        //   ' userInfoId ',
        //   userInfoId,
        // );
        if (isActive) {
          if (userInfoId !== currentUserId) {
            dispatch(CommonActions.setUserInfoRequest(currentUserId));
            dispatch(CommonActions.userInfoRequest(currentUserId));
            dispatch(FeedbackActions.feedbackStatsRequest(currentUserId));
          }
          setInitialLoading(false);
        } else {
          console.log('UserInfo isActive false');
        }
      };
      getUserData();
      return () => {
        isActive = false;
      };
    }, [route.params, common, dispatch]),
  );

  useEffect(() => {
    if (common.fetchUserInfo) {
      if (common.errorUserInfo !== null) {
        setAlert({
          visible: true,
          messageType: _ERROR,
          message: '__server_error_get_user_info',
        });
      }
      setInitialLoading(false);
    }
  }, [common]);

  return (
    <View style={{ flex: 1 }}>
      {initialLoading || common.fetchUserInfo ? (
        <View style={globalStyles.container}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : (
        <ScrollView style={{ padding: 20 }}>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              marginBottom: 20,
            }}>
            <Avatar.Image
              key={userInfoData.imageUrl}
              source={getAvatarOrDummy(true, route.params.userInfoId)}
              size={100}
              style={{ marginRight: 20, backgroundColor: colors.info }}
            />
            <Ratings
              userId={route.params.userInfoId}
              direction="column"
              navigation={navigation}
            />
          </View>
          <View style={{ paddingLeft: 50, paddingRight: 50 }}>
            <Button
              mode="contained"
              onPress={() =>
                navigation.push(MY_EVENT_ROUTE, {
                  readOnly: true,
                  login: common.userInfoId,
                  pushedStack: true,
                })
              }>
              {i18n.t('__past_events')}
            </Button>
          </View>

          <Field
            label={i18n.t('__name')}
            value={`${userInfoData.firstName ?? ''} ${
              userInfoData.lastName ?? ''
            }`.trim()}
          />
          <Field label={i18n.t('__user_since')} value={user_since} />
          <Field label={i18n.t('__dob')} value={user_birthday} />
          <Field
            label={i18n.t('__gender')}
            value={
              user_gender !== undefined && user_gender.length > 0
                ? i18n.t(user_gender[0].v)
                : ''
            }
          />
          <Field
            label={i18n.t('__description')}
            value={
              user_description !== undefined && user_description.length > 0
                ? user_description[0].v
                : ''
            }
          />
          <Field
            label={i18n.t('__food_intol')}
            content={<View style={styles.field}>{chips}</View>}
          />
          <Field
            label={i18n.t('__badges')}
            content={<View style={styles.field}>{badges}</View>}
          />
          <AlertDialog
            onClose={() => {
              setAlert(prevAlert => ({ ...prevAlert, visible: false }));
            }}
            alert={alert}
          />
        </ScrollView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  field: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 40,
  },
});

export default UserInfo;
