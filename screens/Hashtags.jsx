import React, { useEffect, useState } from 'react';
import { View, FlatList, ActivityIndicator, Text } from 'react-native';
import SearchActions from '@reducers/search.reducer';
import * as Icon from '@expo/vector-icons';
import { useDispatch, useSelector } from 'react-redux';
import { colors, globalStyles } from '@constants';
import i18n from '@translations/index';
import AlertDialog from '@components/AlertDialog';
import { Subheading } from 'react-native-paper';
import { useNavigation, useRoute } from '@react-navigation/native';
import OfferTagEntry from '@components/OfferTagEntry';

// class Main extends Component {
//   constructor(props) {
//     super(props);
//     this.state = {
//       showAlertModal: false,
//       messageType: '',
//       message: '',
//       currentTag: '',
//     };
//   }

//   static navigationOptions = props => {
//     console.log('Hashtags title navigatio', props.route.params.tag);
//     return {
//       // tabBarLabel: 'tag',
//       // headerTitle: 'Tag: ' + props.route.params && props.route.params.tag === undefined ? props.route.params.tag : '',
//       title: 'Tag', // : ' + props.route.params && props.route.params.tag === undefined ? props.route.params.tag : '',
//       headerTintColor: 'white',
//       headerStyle: {
//         backgroundColor: colors.primary,
//       },
//       // hasBack: true
//     };
//   };

//   componentDidUpdate(prevProps) {
//     if (
//       prevProps.searchedEvents.searchingTag &&
//       !this.props.searchedEvents.searchingTag
//     ) {
//       if (this.props.searchedEvents.errorSearchingTag) {
//         this.setState({
//           showAlertModal: true,
//           messageType: _ERROR,
//           message: '__server_error_get_events',
//         });
//       }
//     }
//   }

//   loadTag() {
//     console.log('loadTag', this.props);

//     if (this.props.route.params) {
//       const tag = this.props.route.params.tag;
//       if (this.state.currentTag !== tag) {
//         this.setState({ currentTag: tag });
//         this.props.getEventsByTag(tag);
//       }
//     }
//   }

//   componentDidMount() {
//     this.focusSubscription = this.props.navigation.addListener(
//       'focus',
//       this.loadTag,
//     );
//   }
//   componentWillUnmount() {
//     this.focusSubscription.remove();
//   }

//   render() {
//     const { eventsTag, searchingTag } = this.props.searchedEvents;
//     const { message, messageType, showAlertModal } = this.state;
//     return (
//       <View style={{ flex: 1, backgroundColor: colors.whiteBackground }}>
//         {/* <NavigationEvents onDidFocus={() => this.loadTag()}></NavigationEvents> */}
//         {searchingTag === true ? (
//           <View style={globalStyles.container}>
//             <ActivityIndicator size="large" color={colors.primary} />
//           </View>
//         ) : eventsTag.length === 0 ? (
//           <View style={globalStyles.container}>
//             <Icon.FontAwesome5 name="hashtag" color={colors.black} size={80} />
//             <Text style={{ color: colors.black }}>
//               {i18n.t('__no_tags_yet')}
//             </Text>
//           </View>
//         ) : (
//           <View style={{ flex: 10, width: '100%', alignItems: 'center' }}>
//             <FlatList
//               ListHeaderComponent={
//                 <View
//                   style={{
//                     alignItems: 'center',
//                     flex: 1,
//                     marginTop: 5,
//                     marginBottom: 5,
//                   }}>
//                   <Subheading style={globalStyles.blueChip}>{`${
//                     eventsTag.length
//                   } ${i18n.t('__events')}`}</Subheading>
//                 </View>
//               }
//               style={{ width: '100%' }}
//               data={eventsTag}
//               keyExtractor={(item, index) => index.toString()}
//               renderItem={({ item }) => (
//                 <OfferTagEntry
//                   item={item}
//                   tag={this.state.currentTag}
//                   loggedIn={this.props.loggedIn}
//                 />
//               )}
//             />
//           </View>
//         )}
//         <AlertDialog
//           onClose={() => {
//             this.setState({ showAlertModal: false });
//           }}
//           alert={{
//             visible: showAlertModal,
//             message: message,
//             messageType: messageType,
//           }}
//         />
//       </View>
//     );
//   }
// }

// const mapStateToProps = appState => {
//   return {
//     searchedEvents: appState.searchedEvents,
//     loggedIn: isLoggedIn(appState.account),
//   };
// };
// function mapDispatchToProps(dispatch) {
//   return {
//     searchQueryString: tag =>
//       dispatch(SearchActions.eventTagSearchRequest(tag)),
//     getEventsByTag: tag => dispatch(SearchActions.eventTagSearchRequest(tag)),
//   };
// }
// export const Hashtags = connect(mapStateToProps, mapDispatchToProps)(Main);

const Hashtags = () => {
  const searchedEvents = useSelector(appState => appState.searchedEvents);
  const { eventsTag, searchingTag } = searchedEvents;
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  const [currentTag, setCurrentTag] = useState('');
  const [alert, setAlert] = useState({
    message: '',
    messageType: null,
    showAlertModal: false,
  });

  const loadTag = () => {
    console.log('loadTag', route);
    if (route.params) {
      const tag = route.params.tag;
      if (currentTag !== tag) {
        setCurrentTag(tag);
        dispatch(SearchActions.eventTagSearchRequest(tag));
      }
    }
  };

  useEffect(() => {
    const focusSubscription = navigation.addListener('focus', loadTag);

    return () => {
      focusSubscription.remove();
    };
  }, []);

  useEffect(() => {
    console.log('searchedEvents>>', searchedEvents);
    if (
      !searchedEvents.searchingTag &&
      searchedEvents.errorSearchingTag !== null
    ) {
      setAlert({ ...alert, showAlertModal: true });
    }
  }, [searchedEvents.searchingTag]);

  // You can create separate functions to update the state if needed

  return (
    <View style={{ flex: 1, backgroundColor: colors.whiteBackground }}>
      {/* <NavigationEvents onDidFocus={() => this.loadTag()}></NavigationEvents> */}
      {searchingTag === true ? (
        <View style={globalStyles.container}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : eventsTag.length === 0 ? (
        <View style={globalStyles.container}>
          <Icon.FontAwesome5 name="hashtag" color={colors.black} size={80} />
          <Text style={{ color: colors.black }}>{i18n.t('__no_tags_yet')}</Text>
        </View>
      ) : (
        <View style={{ flex: 10, width: '100%', alignItems: 'center' }}>
          <FlatList
            ListHeaderComponent={
              <View
                style={{
                  alignItems: 'center',
                  flex: 1,
                  marginTop: 5,
                  marginBottom: 5,
                }}>
                <Subheading style={globalStyles.blueChip}>{`${
                  eventsTag.length
                } ${i18n.t('__events')}`}</Subheading>
              </View>
            }
            style={{ width: '100%' }}
            data={eventsTag}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => (
              <OfferTagEntry item={item} tag={currentTag} />
            )}
          />
        </View>
      )}
      <AlertDialog
        onClose={() => {
          setAlert({ ...alert, showAlertModal: false });
        }}
        alert={{ ...alert }}
      />
    </View>
  );
};

export default Hashtags;
