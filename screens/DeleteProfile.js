import React, { Component } from 'react';
import { View } from 'react-native';
import { colors, _DELETE, getIcon, globalStyles } from '@constants';
import { connect } from 'react-redux';
import { Button, Divider, Paragraph } from 'react-native-paper';
import AlertDialog from '@components/AlertDialog';
import AccountActions from '@reducers/account.reducer';
import { DRAWER_SEARCH_ROUTE } from '@routes/route_constants';
import i18n from '@translations/index';

class DeleteProfile extends Component {
  constructor(props) {
    super(props);

    this.state = {
      cancelButtonVisible: false,
      confirmDisabled: true,
      messageType: null,
      showModal: false,
    };
  }

  dismissModal = () => {
    console.log('dismissModal this.state.navigateBack');
    this.setState({ showModal: false });
  };

  componentDidUpdate(prevProps) {
    // check to update image
    console.log('componentDidUpdate this.props.account', this.props.account);

    if (prevProps.account.deleting && !this.props.account.deleting) {
      if (this.props.account.errorDelete == null) {
      }
      this.setState({ showModal: false });
      this.props.navigation.navigate(DRAWER_SEARCH_ROUTE);
    }
  }

  render() {
    const { showModal } = this.state;

    return (
      <View
        style={{
          flex: 1,
          maring: 20,
          padding: 10,
          alignContent: 'center',
          justifyContent: 'center',
        }}>
        <Divider style={globalStyles.marginDivider} />
        <View
          style={{
            paddingTop: 20,
            paddingLeft: 20,
            paddingRight: 20,
            paddingBottom: 20,
          }}>
          <Paragraph
            style={{
              marginLeft: 0,
              color: colors.error,
              fontFamily: 'bold',
              marginBottom: 40,
            }}>
            {i18n.t('__delete_user_irreversibile')}
          </Paragraph>
          <Button
            onPress={() => this.setState({ showModal: true })}
            uppercase={false}
            mode="contained"
            style={{ elevation: 10 }}
            icon={getIcon('delete_profile')}>
            {i18n.t('__delete_profile')}
          </Button>
        </View>
        <Divider style={globalStyles.marginDivider} />
        <AlertDialog
          toCancel={() => this.dismissModal()}
          onClose={this.props.deleteProfile}
          alert={{
            visible: showModal,
            message: '__delete_user_irreversibile_final',
            messageType: _DELETE,
          }}
        />
      </View>
    );
  }
}

const mapStateToProps = appState => {
  return {
    account: appState.account,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    deleteProfile: () => dispatch(AccountActions.deleteAccountRequest()),
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(DeleteProfile);
