import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, Platform } from 'react-native';
import {
  colors,
  _ERROR,
  _SUCCESS,
  getDateTime,
  globalStyles,
} from '@constants';
import i18n from '@translations/index';
import * as Icon from '@expo/vector-icons';
import {
  Button,
  Divider,
  DataTable,
  ActivityIndicator,
} from 'react-native-paper';
import PaymentActions from '@reducers/payment.reducer';
import AlertDialog from '@components/AlertDialog';
import {
  usePaymentSheet,
  PlatformPayButton,
  usePlatformPay,
  confirmPlatformPayPayment,
  PlatformPay,
} from '@stripe/stripe-react-native';
import { useSelector, useDispatch } from 'react-redux';
import { appConfig } from '@config/app-config';
import {
  useNavigation,
  useFocusEffect,
  useRoute,
} from '@react-navigation/native';

const Payment = props => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();
  const [alertState, setAlertState] = useState({
    message: '',
    messageType: null,
    visible: false,
  });

  const [screenLoading, setScreenLoading] = useState(false);
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [paymentSheetEnabled, setPaymentSheetEnabled] = useState(false);

  const [confirmPaymentVisible, setConfirmPaymentVisible] = useState(false);
  const [navigateBack, setNavigateBack] = useState(false);
  const account = useSelector(state => state.account);
  const payment = useSelector(state => state.payment);
  const { initPaymentSheet, presentPaymentSheet } = usePaymentSheet();
  const { isPlatformPaySupported } = usePlatformPay();
  const [googlePayEnabled, setGooglePayEnabled] = useState(false);
  const [applePayEnabled, setApplePayEnabled] = useState(false);

  const item = route.params?.item ?? {};

  const isGooglePaySupported = useCallback(async () => {
    if (await isPlatformPaySupported({ googlePay: { testEnv: true } })) {
      setGooglePayEnabled(true);
      setApplePayEnabled(true);
    }
  }, [isPlatformPaySupported]);

  const initializePaymentSheet = useCallback(async () => {
    if (payment === null) {
      return;
    }
    setScreenLoading(true);
    console.log('payment.paymentIntent >>', payment.paymentIntent);
    const { paymentIntent, ephemeralKey, customerId } = payment.paymentIntent;

    const { error } = await initPaymentSheet({
      merchantDisplayName: appConfig.appBrandName,
      customerId: customerId,
      customerEphemeralKeySecret: ephemeralKey,
      paymentIntentClientSecret: paymentIntent,
      // Set `allowsDelayedPaymentMethods` to true if your business can handle payment
      //methods that complete payment after a delay, like SEPA Debit and Sofort.
      // allowsDelayedPaymentMethods: true,
      defaultBillingDetails: {
        name: `${account.account.nickname} ${account.account.email}`,
      },
      returnURL: 'selfinvite://paymentstack',
    });
    console.log('present resultInitPaymentSheet ', error);
    if (error) {
      setAlertState({
        message: `${error.message} - ${error.code}`,
        messageType: _ERROR,
        visible: true,
      });
    } else {
      setPaymentSheetEnabled(true);
    }
    setScreenLoading(false);
  }, [
    initPaymentSheet,
    payment,
    account.account.email,
    account.account.nickname,
  ]);

  useFocusEffect(
    useCallback(() => {
      dispatch(PaymentActions.paymentTokenRequest(item.eventofferkvid));
    }, [dispatch, item.eventofferkvid]),
  );

  useEffect(() => {
    console.log('componentDidUpdate initializePaymentSheet>>', payment);
    if (
      payment?.paymentIntent === null ||
      payment?.paymentIntent === undefined
    ) {
      return;
    }
    isGooglePaySupported();
    initializePaymentSheet();
  }, [
    initializePaymentSheet,
    payment,
    payment.paymentIntent,
    isGooglePaySupported,
  ]);

  const openPaySheet = async () => {
    setPaymentLoading(true);
    const { error } = await presentPaymentSheet();
    console.log('present presentPaymentSheet ', error);
    if (!error) {
      setAlertState({
        visible: true,
        message: i18n.t('__payment_success_event_request'),
        messageType: _SUCCESS,
      });
      setNavigateBack(true);
    } else {
      setAlertState({
        message: `${error.message} - ${error.code}`,
        messageType: _ERROR,
        visible: true,
      });
    }
    setPaymentSheetEnabled(false);
    setPaymentLoading(false);
  };

  const applePay = async () => {
    if (payment === null) {
      return;
    }
    setPaymentLoading(true);
    const { error } = await confirmPlatformPayPayment(
      payment.paymentIntent.paymentIntent,
      {
        applePay: {
          cartItems: [
            {
              label: appConfig.appBrandName,
              amount: (payment.paymentIntent.totalAmount / 100).toFixed(2),
              paymentType: PlatformPay.PaymentType.Immediate,
            },
          ],
          merchantCountryCode: 'IT',
          currencyCode: 'EUR',
          // requiredShippingAddressFields: [
          //   PlatformPay.ContactField.PostalAddress,
          // ],
          // requiredBillingContactFields: [PlatformPay.ContactField.PhoneNumber],
        },
      },
    );

    if (!error) {
      setAlertState({
        ...alertState,
        visible: true,
        messageType: _SUCCESS,
      });
      setNavigateBack(true);
    } else {
      setAlertState({
        message: `${error.message} - ${error.code}`,
        messageType: _ERROR,
        visible: true,
      });
    }
    setPaymentLoading(false);
  };

  const googlePay = async () => {
    if (payment === null) {
      return;
    }
    setPaymentLoading(true);
    const { error } = await confirmPlatformPayPayment(
      payment.paymentIntent.paymentIntent,
      {
        googlePay: {
          testEnv: appConfig.googlePayTestEnv,
          merchantName: appConfig.appBrandName,
          merchantCountryCode: 'IT',
          currencyCode: 'EUR',
          // billingAddressConfig: {
          //   format: PlatformPay.BillingAddressFormat.Full,
          //   isPhoneNumberRequired: true,
          //   isRequired: true,
          // },
        },
      },
    );

    if (!error) {
      setAlertState({
        ...alertState,
        visible: true,
        messageType: _SUCCESS,
      });
      setNavigateBack(true);
    } else {
      setAlertState({
        message: `${error.message} - ${error.code}`,
        messageType: _ERROR,
        visible: true,
      });
    }
    setPaymentLoading(false);
  };

  const dismissResultModal = () => {
    console.log('dispatch>> navigateBack ', navigateBack);
    setAlertState({ ...alertState, visible: false });
    if (navigateBack) {
      navigation.goBack();
    }
  };

  const normalizedFee = parseFloat(item.fee) * 100;
  const totalPrice = (parseInt(item.v, 10) * item.pricepp).toFixed(1);
  const totalFee = (
    (totalPrice / (1 + parseFloat(item.fee))) *
    parseFloat(item.fee)
  ).toFixed(1);

  return (
    <View
      style={{
        flex: 1,
        marginBottom: 20,
        paddingLeft: 10,
        paddingRight: 10,
      }}>
      <ScrollView>
        <View style={{ flex: 1, margin: 20 }}>
          <Divider key={1} style={globalStyles.marginDivider} />
          <DataTable key={2}>
            <DataTable.Row style={{ alignItems: 'center' }}>
              <Text
                style={{
                  fontSize: 28,
                  fontWeight: '800',
                  fontFamily: 'medium',
                  color: colors.black,
                }}>
                {item.name}
              </Text>
            </DataTable.Row>
            <DataTable.Row>
              <View
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}>
                <View style={{ flex: 0.4 }}>
                  <Text style={{ color: colors.black }}>
                    {i18n.t('__date_time')}
                  </Text>
                </View>
                <View style={{ flex: 0.5 }}>
                  <Text style={{ color: colors.black }}>
                    {getDateTime(item.date, false)}
                  </Text>
                </View>
                <View style={{ flex: 0.1 }}>
                  <Icon.Feather size={styles.iconSize} name="clock" />
                </View>
              </View>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>{`${item.v} x ${i18n.t(
                '__pricepp',
              )}`}</DataTable.Cell>
              <DataTable.Cell numeric>{`${item.pricepp} €`}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row>
              <DataTable.Cell>{`${i18n.t(
                '__participation_fee_desc',
              )} ${normalizedFee} %`}</DataTable.Cell>
              <DataTable.Cell numeric>{`${totalFee} €`}</DataTable.Cell>
            </DataTable.Row>
            <DataTable.Row
              style={{ ...styles.cell, color: colors.whiteBackground }}>
              <DataTable.Cell>
                <Text style={{ fontWeight: 'bold', color: colors.white }}>
                  {i18n.t('__total')}
                </Text>
              </DataTable.Cell>
              <DataTable.Cell numeric>
                <Text
                  style={{
                    fontWeight: 'bold',
                    color: colors.white,
                  }}>{`${totalPrice} €`}</Text>
              </DataTable.Cell>
            </DataTable.Row>
          </DataTable>
        </View>
        <Divider style={globalStyles.marginDivider} />
        <View
          style={{
            paddingTop: 20,
            paddingLeft: 20,
            paddingRight: 20,
            paddingBottom: 20,
          }}>
          {payment.fetchingToken === true ? (
            <View style={{ flex: 1 }}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : (
            <></>
          )}
          <View
            style={{
              flexDirection: 'column',
              marginTop: 10,
              marginBottom: 20,
              justifyContent: 'center',
            }}
            visible={confirmPaymentVisible}>
            <Button
              style={{ elevation: 2 }}
              mode="contained"
              icon="check"
              disabled={
                payment.updatingPayment ||
                screenLoading ||
                payment.fetchingToken
              }
              loading={
                payment.updatingPayment || screenLoading || paymentLoading
              }
              onPress={openPaySheet}>
              {i18n.t('__pay')}
            </Button>
            {Platform.OS === 'android' && googlePayEnabled ? (
              <PlatformPayButton
                type={PlatformPay.ButtonType.GooglePay}
                onPress={googlePay}
                disabled={
                  payment.updatingPayment ||
                  screenLoading ||
                  payment.fetchingToken
                }
                borderRadius={20}
                style={{
                  marginTop: 10,
                  alignSelf: 'center',
                  elevation: 2,
                  width: '100%',
                  height: 35,
                }}
              />
            ) : null}
            {Platform.OS === 'ios' && applePayEnabled ? (
              <PlatformPayButton
                disabled={
                  payment.updatingPayment ||
                  screenLoading ||
                  payment.fetchingToken
                }
                type={PlatformPay.ButtonType.Order}
                appearance={PlatformPay.ButtonStyle.WhiteOutline}
                onPress={applePay}
                borderRadius={20}
                style={{
                  marginTop: 10,
                  alignSelf: 'center',
                  elevation: 2,
                  width: '100%',
                  height: 35,
                }}
              />
            ) : null}
          </View>
          <Divider style={globalStyles.marginDivider} />
        </View>
      </ScrollView>

      <AlertDialog onClose={dismissResultModal} alert={alertState} />
    </View>
  );
};

export default Payment;

const styles = StyleSheet.create({
  iconSize: 20,
  element: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  paragraphText: {
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 3,
  },
  cell: {
    backgroundColor: colors.primary,
    borderRadius: 10,
  },
});
