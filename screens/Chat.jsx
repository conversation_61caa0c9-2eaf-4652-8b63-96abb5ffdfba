import React, { useCallback, useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { GiftedChat, Send, Composer, Bubble } from 'react-native-gifted-chat';
import { IconButton } from 'react-native-paper';
import {
  colors,
  getAvatar,
  MESSAGE_STATE_SYSTEM,
  MESSAGE_STATE_RX_READ,
  _ERROR,
  MESSAGE_STATE_STICKY,
} from '@constants';
import { getLogin } from '@reducers/account.reducer';
import { useDispatch, useSelector } from 'react-redux';
import i18n from '@translations/index';
import MessageActions from '@reducers/message.reducer';
import { launchImageLibrary } from 'react-native-image-picker';
import AlertDialog from '@components/AlertDialog';
import ProgressDialog from '@components/ProgressDialog';
import * as utilityAction from '@actions/utility.action';
import { EventRegister } from 'react-native-event-listeners';
import { useRoute } from '@react-navigation/native';
import { detectPhoneOrEmail, getCachedLogin } from '@constants/functions';
import useUserNickname from '@hooks/useUserNickname';
import useUserAvatar from '@hooks/useUserAvatar';

const Chat = props => {
  const [state, setState] = useState({
    showAlertModal: false,
    message: '',
    messageType: _ERROR,
    progressBarVisible: false,
    imageUploadProgress: 0,
    sanitizedConversation: [],
    isChatDisabled: false,
  });
  const dispatch = useDispatch();
  const route = useRoute();
  const messages = useSelector(nextState => nextState.messages);
  const username = useSelector(nextState => getLogin(nextState.account));
  const login = useSelector(nextState => nextState.account.account.login);
  const item = route.params.item;
  const userNickname = useUserNickname(login);
  const userAvatar = useUserAvatar(login);

  const appendMessage = useCallback(
    message => dispatch(MessageActions.appendConversationMessage(message)),
    [dispatch],
  );
  const getMessagesByThread = useCallback(
    threadId => dispatch(MessageActions.messageByThreadRequest(threadId)),
    [dispatch],
  );
  const createNewMessage = message =>
    dispatch(MessageActions.messageUpdateRequest(message));
  const uploadEventFile = file => dispatch(utilityAction.uploadEventFile(file));
  const deleteMessage = messageId =>
    dispatch(MessageActions.messageDeleteRequest(messageId));
  const markMessageRead = useCallback(
    threadId => dispatch(MessageActions.messageReadRequest(threadId)),
    [dispatch],
  );

  const onSend = (currentMessages = []) => {
    console.log('onSend message', currentMessages[0]);
    console.log('onSend username', username);
    console.log('onSend item.userTx', item.userTx);
    console.log('onSend item.userRx', item.userRx);
    let message = {
      thread: item.thread,
      body: {
        text: currentMessages[0].text,
      },
      userRx: username === item.userTx ? item.userRx : item.userTx,
    };
    if (currentMessages[0].image) {
      message.body.image = currentMessages[0].image;
    }

    if (detectPhoneOrEmail(message.body.text)) {
      message.stateTx = MESSAGE_STATE_STICKY;
    }
    createNewMessage(message);
  };

  const markAsRead = useCallback(() => {
    console.log('conversation this.props...', messages);
    console.log('conversation this.props.username', username);
    const lastUnreadMessage = messages.conversation.find(
      message =>
        message.stateRx !== MESSAGE_STATE_RX_READ &&
        message.userRx === username,
    );
    console.log('conversation lastUnreadMessage...', lastUnreadMessage);
    if (lastUnreadMessage !== undefined) {
      markMessageRead(lastUnreadMessage.thread);
    }
  }, [messages, username, markMessageRead]);

  const onUploadProgress = event => {
    console.debug('onUploadProgress e:', event); //, Math.round((100 * event.loaded) / event.total))
    // setState(prevState => ({ ...prevState, imageUploadProgress: Math.round((100 * event.loaded) / event.total) }))
  };

  const uploadFileForChat = async uri => {
    setState(prevState => ({ ...prevState, progressBarVisible: true }));
    try {
      const action = await uploadEventFile(uri, onUploadProgress);

      console.log('serverRequest 4 action', action);
      if (action.payload.status) {
        // otherwise the status is not present
        throw action.payload;
      }

      const message = {
        thread: item.thread,
        body: {
          text: '',
          image: action.payload.fileDownloadUri,
        },
        userRx: username === item.userTx ? item.userRx : item.userTx,
      };

      setState(prevState => ({
        ...prevState,
        latestMessage: message,
        progressBarVisible: false,
      }));
      createNewMessage(message);
    } catch (e) {
      console.log('serverRequest catch e', e);
      setState(prevState => ({
        ...prevState,
        showAlertModal: true,
        message: '__server_error_save_image',
        progressBarVisible: false,
      }));
    }
  };

  const showImageLibraryAndUpload = async () => {
    const resultPicker = await launchImageLibrary({
      mediaType: 'photo',
      quality: 0.7,
      selectionLimit: 1,
    });
    if (resultPicker.assets && resultPicker.assets.length > 0) {
      uploadFileForChat(resultPicker.assets[0].uri);
    }
  };

  const getCachedLoginCallback = useCallback(
    async user => getCachedLogin(user),
    [],
  );
  const getAvatarCallback = useCallback(user => getAvatar(user), []);

  const replaceUsersToNickNamesCallback = useCallback(
    (messageBody, userTx, userRx, userTxNickname, userRxNickname) => {
      const sanitisedBody = messageBody
        .replace(userTx, userTxNickname)
        .replace(userRx, userRxNickname);
      return sanitisedBody;
    },
    [],
  );

  useEffect(() => {
    getMessagesByThread(item.thread);
  }, [getMessagesByThread, item.thread]);

  useEffect(() => {
    const sanitizeThread = async () => {
      if (messages.conversation.length === 0) {
        return;
      }
      const userTx = messages.conversation[0].userTx;
      const userRx = messages.conversation[0].userRx;
      const userTxNickname = await getCachedLoginCallback(userTx);
      const userRxNickname = await getCachedLoginCallback(userRx);

      const cleanConversation = messages.conversation.map(
        ({ id, body, dateTx, state: messageState }) => {
          console.log('cleanConversation cycle', id, body.text);
          return {
            _id: id,
            text: replaceUsersToNickNamesCallback(
              body?.text,
              userTx,
              userRx,
              userTxNickname,
              userRxNickname,
            ),
            key: id,
            image: body?.image,
            createdAt: new Date(dateTx),
            system: messageState === MESSAGE_STATE_SYSTEM ? true : false,
            user: {
              _id: userTx,
              name: userTxNickname,
              avatar: getAvatarCallback(userTx),
            },
          };
        },
      );
      setState(prevState => ({
        ...prevState,
        sanitizedConversation: cleanConversation,
      }));
    };
    sanitizeThread();

    const isChatDisabled =
      messages.conversation.findIndex(e => e.state > 0) ===
      MESSAGE_STATE_SYSTEM;
    setState(prevState => ({
      ...prevState,
      isChatDisabled,
    }));
  }, [
    messages,
    replaceUsersToNickNamesCallback,
    getAvatarCallback,
    getCachedLoginCallback,
  ]);

  useEffect(() => {
    if (!messages.updating) {
      if (messages.errorUpdating) {
        console.debug('errorUpdating ', messages.errorUpdating);
      }
    }

    if (!messages.fetchingThread) {
      if (messages.errorThread) {
        console.debug('errorThread ', messages.errorThread);
      }
    }
  }, [messages]);

  useEffect(() => {
    console.log('EventRegister register eventlistener Chat');
    const messageReceivedListener = EventRegister.addEventListener(
      'messageReceived',
      data => {
        console.log(
          'EventRegister messageReceived',
          data,
          'username',
          username,
        );
        if (data.userRx === username && data.thread === item.thread) {
          appendMessage(data);
        }
      },
    );

    return () => {
      console.debug('componentWillUnmount Chat');
      markAsRead();
      EventRegister.removeEventListener(messageReceivedListener);
    };
  }, [appendMessage, getMessagesByThread, markAsRead, username, item.thread]);

  return (
    <View style={{ flex: 1 }}>
      <GiftedChat
        renderActions={() => (
          // <View style={{flex: 1, flexDirection: 'row'}}>
          <IconButton
            disabled={state.isChatDisbled}
            icon="attachment"
            onPress={showImageLibraryAndUpload}
            name="image"
            size={30}
            color="#000"
          />
        )}
        renderSystemMessage={({
          currentMessage,
          containerStyle,
          textStyle,
        }) => {
          console.log('renderSystemMessage', currentMessage);
          if (currentMessage) {
            return (
              <View style={[styles.container, containerStyle]}>
                <Text style={[styles.text, textStyle]}>
                  {currentMessage.text}
                </Text>
              </View>
            );
          }
          return null;
        }}
        onLongPress={(context, pressedMessage) => {
          const options = [
            `${i18n.t('__delete_message')}: ${pressedMessage.text.substring(
              0,
              10,
            )} ...`,
            i18n.t('__cancel'),
          ];
          const cancelButtonIndex = options.length - 1;
          if (pressedMessage.user._id !== this.props.username) {
            return;
          } else {
            context.actionSheet().showActionSheetWithOptions(
              {
                options,
                cancelButtonIndex,
              },
              async buttonIndex => {
                switch (buttonIndex) {
                  case 0:
                    // Your delete logic
                    await deleteMessage(state.message._id);
                    break;
                  default:
                    break;
                }
              },
            );
          }
        }}
        alwaysShowSend={true}
        messages={state.sanitizedConversation}
        onSend={thisConversation => onSend(thisConversation)}
        user={{
          _id: login,
          name: userNickname,
          avatar: userAvatar,
        }}
        // text={inputText}
        // onInputTextChanged={inputText => this.setState({ inputText })}
        renderBubble={props => {
          return (
            <Bubble
              {...props}
              textStyle={{
                left: {
                  color: colors.whiteBackground,
                },
              }}
              wrapperStyle={{
                left: {
                  backgroundColor: colors.info,
                },
                // right: {
                //   backgroundColor: colors.warning,
                // }
              }}
            />
          );
        }}
        renderComposer={props => {
          return (
            <Composer
              disableComposer={state.isChatDisbled}
              {...props}
              placeholder={
                state.isChatDisbled
                  ? i18n.t('__message_disabled')
                  : i18n.t('__type_message')
              }
              composerHeight={50}
              textInputStyle={styles.composerStyle}
            />
          );
        }}
        renderSend={props => {
          return (
            <Send
              disabled={state.isChatDisbled}
              {...props}
              textStyle={{
                color: colors.primary,
              }}
              containerStyle={styles.send}>
              <IconButton
                disabled={state.isChatDisbled}
                size={30}
                style={{ paddingTop: 15 }}
                color={colors.primary}
                icon="arrow-right-circle"
              />
            </Send>
          );
        }}
      />
      {/* ios needs to fixed */}
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'height' : 'height'}
      />
      <ProgressDialog
        visible={state.progressBarVisible}
        progress={state.imageUploadProgress}
      />
      <AlertDialog
        onClose={() => {
          this.setState({ showAlertModal: false });
        }}
        alert={{
          visible: state.showAlertModal,
          message: state.message,
          messageType: state.messageType,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  send: {
    borderWidth: 1,
    height: 50,
    paddingBottom: 3,
    borderColor: colors.primary,
  },
  composerStyle: {
    borderBottomWidth: 1,
    marginBottom: 0,
    marginLeft: 0,
    marginRight: 0,
    marginTop: 0,
    borderRightWidth: 0,
    paddingLeft: 10,
    paddingBottom: 10,
    paddingTop: 10,
    borderBottomColor: colors.primary,
  },
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    marginTop: 5,
    marginLeft: 25,
    marginRight: 25,
    marginBottom: 10,
    borderRadius: 25,
  },
  slackImage: {
    borderRadius: 3,
    marginLeft: 0,
    marginRight: 0,
  },
  text: {
    backgroundColor: colors.gray,
    color: colors.declined,
    fontSize: 16,
    borderRadius: 25,
    padding: 10,
  },
});

export default Chat;
