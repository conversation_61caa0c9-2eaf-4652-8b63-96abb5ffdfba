import React, { PureComponent } from 'react';
import {
  View,
  Text,
  Linking,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {
  Button,
  Checkbox,
  DataTable,
  Divider,
  Subheading,
} from 'react-native-paper';
import RBSheet from 'react-native-raw-bottom-sheet';
import i18n from '@translations/index';
import { connect } from 'react-redux';
import AccountActions, { isLoggedIn } from '@reducers/account.reducer';
import {
  colors,
  globalStyles,
  POLICY_CHECKED,
  PARAMETER_PREFIX_POLICY,
  images,
  _WARNING,
  _SUCCESS,
  icon_size_medium,
} from '@constants';
class InnerPolicyChecker extends PureComponent {
  state = {
    policies: new Set(),
    checked: [],
    buttonDisabled: true,
    policiesLeft: [],
    showAlertModal: false,
    message: '__error_server',
    messageType: null,
  };

  componentDidUpdate(prevProps, prevState) {
    if (
      prevProps.account.fetchingPreferences &&
      !this.props.account.fetchingPreferences
    ) {
      const preferences = this.props.account.preferences || [];
      const indexes = preferences.map(i => i.k);
      const currentConfiguration = (
        this.props.common.configurations || []
      ).filter(item => item.k.includes(PARAMETER_PREFIX_POLICY));
      // console.log('currentConfiguration ', currentConfiguration)
      let policiesLeft =
        currentConfiguration.filter(item => !indexes.includes(item.k)) || [];
      this.setState({ policiesLeft });

      if (policiesLeft && policiesLeft.length > 0 && this.RBSheet) {
        this.RBSheet.open();
      }
    }
  }

  packAndSendUserPrefs(prefs) {
    if (prefs.length === 0) {
      // this.setState({
      //   showAlertModal: true,
      //   message: '__no_policy_selected',
      //   messageType: _WARNING,
      // });
      return;
    }
    let preferences = [];
    prefs.forEach((e, i) => {
      preferences.push({
        k: e,
        v: POLICY_CHECKED,
      });
    });
    const { policiesLeft } = this.state;
    // console.log('packAndSendUserPrefs preferences', preferences);

    this.RBSheet.close();
    this.setState({
      policies: new Set(),
      checked: [],
      // showAlertModal: true,
      // message:
      //   policiesLeft.length === preferences.length
      //     ? '__all_policy_selected'
      //     : '__not_all_policy_selected',
      // messageType:
      // policiesLeft.length === preferences.length ? _SUCCESS : _WARNING,
    });
    this.props.addUserPreferences(preferences);
    this.props.closeDialog(
      true,
      policiesLeft.length === preferences.length
        ? '__all_policy_selected'
        : '__not_all_policy_selected',
      policiesLeft.length === preferences.length ? _SUCCESS : _WARNING,
    );
  }

  render() {
    const { policiesLeft, checked, policies } = this.state;

    return (
      <RBSheet
        ref={ref => {
          this.RBSheet = ref;
        }}
        closeOnDragDown={true}
        closeOnPressMask={true}
        customStyles={{
          wrapper: {
            backgroundColor: 'transparent',
          },
          draggableIcon: {
            backgroundColor: '#000',
          },

          container: {
            backgroundColor: colors.background,
            borderTopRightRadius: 25,
            borderTopLeftRadius: 25,
            borderTopColor: colors.primary,
            borderLeftColor: colors.primary,
            borderRightColor: colors.primary,
            borderTopWidth: 2,
            borderLeftWidth: 0.5,
            borderRightWidth: 0.5,
            overflow: 'hidden',
          },
        }}>
        <ScrollView>
          <TouchableOpacity activeOpacity={1}>
            <View
              style={{
                justifyContent: 'center',
                alignItems: 'center',
              }}>
              <Image
                source={images._policy}
                style={{
                  marginBottom: 10,
                  width: icon_size_medium,
                  height: icon_size_medium,
                }}
              />
              <DataTable>
                <Subheading style={{ marginLeft: 10 }}>
                  {i18n.t('__policies')}
                </Subheading>
                <View>
                  <Divider style={globalStyles.marginDivider} />
                  {policiesLeft &&
                    policiesLeft.map((e, i) => {
                      const elements = e.v.split(' ');
                      const url = elements[1];
                      const label = i18n.t(elements[0]);
                      const nextArray = [...checked];
                      const nextPolicies = policies;

                      return (
                        <View key={i}>
                          <DataTable.Row>
                            <View
                              key={i + i}
                              style={{
                                flex: 1,
                                flexDirection: 'row',
                                justifyContent: 'space-between',
                              }}>
                              <DataTable.Cell
                                style={{
                                  flex: 0.7,
                                }}>
                                <Checkbox.Item
                                  position="leading"
                                  label={label}
                                  color={colors.primary}
                                  status={
                                    nextArray[i] ? 'checked' : 'unchecked'
                                  }
                                  onPress={() => {
                                    nextArray[i] = !nextArray[i];
                                    if (nextArray[i]) {
                                      nextPolicies.add(e.k);
                                    } else {
                                      nextPolicies.delete(e.k);
                                    }
                                    this.setState({
                                      policies: nextPolicies,
                                      checked: nextArray,
                                      buttonDisabled:
                                        nextPolicies.size > 0 ? false : true,
                                    });
                                  }}
                                />
                              </DataTable.Cell>
                              <DataTable.Cell style={{ flex: 0.3 }}>
                                <TouchableOpacity
                                  onPress={() => Linking.openURL(url)}>
                                  <Text
                                    style={{
                                      marginLeft: 20,
                                      marginRight: 0,
                                      color: colors.blue,
                                    }}>
                                    {i18n.t('__use_condition')}
                                  </Text>
                                </TouchableOpacity>
                              </DataTable.Cell>
                            </View>
                          </DataTable.Row>
                          <Divider style={globalStyles.marginDivider} />
                        </View>
                      );
                    })}
                </View>
              </DataTable>
            </View>
            <View
              style={{
                flexDirection: 'row',
                justifyContent: 'center',
              }}>
              <Button
                mode="contained"
                style={{
                  marginLeft: 5,
                  marginRight: 5,
                  marginBottom: 10,
                  marginTop: 20,
                }}
                contentStyle={{ padding: 5 }}
                loading={false}
                disabled={this.state.buttonDisabled}
                onPress={() => this.packAndSendUserPrefs(policies)}>
                {i18n.t('__save')}
              </Button>
            </View>
          </TouchableOpacity>
        </ScrollView>
      </RBSheet>
    );
  }
}

const mapStateToProps = appState => {
  return {
    account: appState.account,
    common: appState.common,
    loggedIn: isLoggedIn(appState.account),
  };
};
function mapDispatchToProps(dispatch) {
  return {
    addUserPreferences: preferences =>
      dispatch(AccountActions.addAccountPreferencesRequest(preferences)),
  };
}

export const PolicyChecker = connect(
  mapStateToProps,
  mapDispatchToProps,
)(InnerPolicyChecker);
