import React, { Component } from 'react';
import {
  View,
  ScrollView,
  Image,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';
import {
  images,
  colors,
  _ERROR,
  getBic,
  _SUCCESS,
  checkAddress,
  checkPhoneNumber,
  globalStyles,
  checkAccountAvatar,
  checkBankAccount,
  maskValue,
  icon_size_xsmall,
} from '@constants';
import {
  Avatar,
  Button,
  Portal,
  Dialog,
  Surface,
  IconButton,
} from 'react-native-paper';
import { Form } from '@components';
import { connect } from 'react-redux';
import i18n from '@translations/index';
import Field from './component/Field';
import AlertDialog from '@components/AlertDialog';
import * as Icon from '@expo/vector-icons';
import PaymentActions from '@reducers/payment.reducer';
import Ratings from './component/Ratings';
import CommonActions from '@reducers/common.reducer';
import {
  DRAWER_USERS_BLOCKED_ROUTE,
  EDIT_AVATAR_ROUTE,
} from '@routes/route_constants';
import { ProfilePane } from './component/ProfilePane';
import FeedbackActions from '@reducers/feedbacks.reducer';
import KycManagedFlow from './component/KycManagedFlow';
import DeleteUser from './component/DeleteUser';

class Profile extends Component {
  constructor(props) {
    super(props);
    this.state = {
      imageHash: Date.now(),
      showIbanDialog: false,
      disableUploadIds: true,
      disableIbanSave: true,
      disableIbanView: true,
      disablePhoneView: true,
      disableKycView: true,
      showAlertModal: false,
      message: '__same_pic_inserted',
      messageType: _ERROR,
      ibanMessage: '__bank_onboard',
      profileButtonColor: colors.warning,
      phoneButtonColor: colors.warning,
      bankAccountButtonColor: colors.blue,
      bankForm: {
        iban: {
          label: '__iban',
          maxLength: 255,
          value: '',
        },
      },
      disableKyc: true,
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.account.fetching && !this.props.account.fetching) {
      const addressStatus = checkAddress(this.props.account.account);
      const phoneStatus = checkPhoneNumber(this.props.account.account);
      this.setState({
        imageHash: Date.now(),
        phoneButtonColor: phoneStatus ? colors.success : colors.warning,
        profileButtonColor: addressStatus ? colors.success : colors.warning,
        disablePhoneView: !addressStatus,
      });
    }

    if (
      prevProps.payment.updatingBankAccount &&
      !this.props.payment.updatingBankAccount
    ) {
      let message = '',
        messageType = '',
        disableIbanSave = true,
        showIbanDialog = true;
      if (this.props.payment.errorUpdatingBankAccount !== null) {
        messageType = _ERROR;
        message = this.props.payment.errorUpdatingBankAccount?.errorKey; //TODO: check if it is .title
        disableIbanSave = false;
      } else {
        messageType = _SUCCESS;
        message = '__iban_inserted';
        showIbanDialog = false;
        this.props.getUserWallet();
      }
      this.setState({
        messageType,
        message,
        showAlertModal: true,
        disableIbanSave,
        showIbanDialog,
      });
    }
  }

  bankAccountUpload = async () => {
    const { bankForm } = this.state;

    const response = await getBic(bankForm.iban.value);

    if (response.valid === true) {
      this.props.uploadBankAccount({
        iban: response.iban,
        country: bankForm.iban.value.substring(0, 2),
      });
    } else {
      this.setState({
        message: '__wrong_data iban',
        messageType: _ERROR,
        showAlertModal: true,
      });
    }
  };

  closeBankAccountDialog = () => this.setState({ showIbanDialog: false });

  getUserData = login => {
    this.props.getUserWallet();
    this.props.getUserFeedbackStats(login);
    this.props.getUserReviewStats(login);
  };

  screenChecks = () => {
    const { account } = this.props.account;
    if (account != null) {
      this.getUserData(account.login);

      const addressStatus = checkAddress(this.props.account.account);
      const phoneStatus = checkPhoneNumber(this.props.account.account);
      const checkUserInfo = addressStatus && phoneStatus;

      this.setState({
        disableKyc: !checkUserInfo,
        disableKycView: !checkUserInfo,
        disableIbanView: !checkUserInfo,
        disablePhoneView: !addressStatus,
      });

      this.props.setUserInfoRequest(account.login);
    }
  };

  changeBankFormHandler = (text, field) => {
    const { bankForm } = this.state;
    const cleanIban = text.replace(/ /g, '');
    bankForm[field].value = cleanIban;

    this.setState({ bankForm, disableIbanSave: cleanIban.length < 18 });
  };

  checkBankAccountState = (showData = false) => {
    const { userWallet } = this.props.payment;
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <Image
          style={{
            height: icon_size_xsmall,
            width: icon_size_xsmall,
            marginRight: 10,
          }}
          source={
            checkBankAccount(userWallet) ? images._success : images._warning
          }
        />
        {showData && checkBankAccount(userWallet) ? (
          <Text style={{ color: colors.black }}>
            {maskValue(userWallet.iban)}
          </Text>
        ) : (
          <></>
        )}
      </View>
    );
  };

  componentDidMount() {
    this.focusSubscription = this.props.navigation.addListener(
      'focus',
      this.screenChecks,
    );
  }
  componentWillUnmount() {
    console.log(
      'componentWillUnmount b4 focusSubscription',
      this.focusSubscription,
    );
    if (this.focusSubscription.remove !== undefined) {
      this.focusSubscription.remove();
    }
    console.log(
      'componentWillUnmount b4 focusSubscription',
      this.focusSubscription,
    );
  }

  render() {
    const { account } = this.props.account;
    const {
      imageHash,
      kycDetails,
      disableIbanView,
      disablePhoneView,
      disableKycView,
      message,
      messageType,
      showAlertModal,
    } = this.state;
    const blockUserRight = Math.ceil(wp('75%'));

    return (
      <View style={{ flex: 1 }}>
        {/* <NavigationEvents onDidFocus={this.screenChecks} /> */}
        <ScrollView style={{ padding: 20 }}>
          <View
            style={{
              alignItems: 'center',
              marginBottom: 20,
              ...globalStyles.contentCard,
            }}>
            <View style={{ flex: 1 }}>
              {checkAccountAvatar(account) ? (
                <Avatar.Image
                  key={account.imageUrl}
                  source={{ uri: `${account.imageUrl}?${imageHash}` }}
                  size={100}
                  style={{
                    marginRight: 20,
                    backgroundColor: colors.transparent,
                  }}
                />
              ) : (
                <Avatar.Image
                  source={images._profile}
                  size={100}
                  style={{ marginRight: 20, backgroundColor: colors.blue }}
                />
              )}
            </View>
            <Surface
              style={{
                position: 'absolute',
                top: 70,
                padding: 2,
                width: 35,
                height: 35,
                borderWidth: 2,
                backgroundColor: colors.white,
                borderColor: colors.black,
                borderRadius: 20,
                elevation: 10,
                right: 120,
              }}>
              <TouchableOpacity
                onPress={() =>
                  this.props.navigation.navigate(EDIT_AVATAR_ROUTE)
                }>
                <Icon.MaterialCommunityIcons
                  color={colors.black}
                  size={28}
                  name="camera"
                />
              </TouchableOpacity>
            </Surface>
            <Surface
              style={{
                position: 'absolute',
                top: 70,
                width: 35,
                height: 35,
                borderWidth: 2,
                backgroundColor: colors.white,
                borderColor: colors.black,
                borderRadius: 20,
                elevation: 10,
                right: blockUserRight,
              }}>
              <TouchableOpacity
                onPress={() =>
                  this.props.navigation.navigate(DRAWER_USERS_BLOCKED_ROUTE)
                }>
                <Icon.AntDesign
                  color={colors.black}
                  name="deleteuser"
                  size={28}
                />
              </TouchableOpacity>
            </Surface>
          </View>

          {account !== null ? (
            <View
              style={{
                ...globalStyles.contentCard,
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: 30,
              }}>
              <Ratings
                userId={account.login}
                direction="row"
                navigation={this.props.navigation}
              />
            </View>
          ) : null}

          <ProfilePane
            disablePhoneView={disablePhoneView}
            account={this.props.account}
            navigation={this.props.navigation}
          />

          <View
            style={{
              ...globalStyles.contentCard,
              marginBottom: 30,
              opacity: disableIbanView ? 0.4 : 1,
            }}>
            <TouchableOpacity
              style={{
                position: 'absolute',
                top: -15,
                width: 35,
                height: 35,
                borderWidth: 2,
                backgroundColor: colors.white,
                borderColor: colors.black,
                borderRadius: 25,
                elevation: 10,
                right: 10,
              }}>
              <IconButton
                icon={({ size, color }) => (
                  <Icon.Feather size={size} color={color} name="edit-3" />
                )}
                disabled={disableIbanView}
                size={25}
                style={{ marginTop: -5, marginLeft: -5, padding: 0 }}
                onPress={() => this.setState({ showIbanDialog: true })}
              />
            </TouchableOpacity>
            <Field
              label={i18n.t('__bank_account')}
              content={this.checkBankAccountState(true)}
            />
          </View>
          <KycManagedFlow
            disableKycView={disableKycView}
            disableKyc={this.state.disableKyc}
          />
          <DeleteUser />
        </ScrollView>

        <Portal>
          <Dialog visible={this.state.showIbanDialog}>
            <Dialog.Title>{i18n.t('__bank_account')}</Dialog.Title>
            <Dialog.Content>
              <Form
                change={this.changeBankFormHandler}
                form={this.state.bankForm}
              />
            </Dialog.Content>
            <Dialog.Actions>
              <Button
                icon="cancel"
                style={{ marginRight: 5 }}
                onPress={this.closeBankAccountDialog}>
                {i18n.t('__close')}
              </Button>
              <Button
                mode="contained"
                icon="upload-multiple"
                disabled={
                  this.state.disableIbanSave ||
                  this.props.payment.updatingBankAccount
                }
                loading={this.props.payment.updatingBankAccount}
                onPress={this.bankAccountUpload}>
                {i18n.t('__save')}
              </Button>
            </Dialog.Actions>
          </Dialog>
          <AlertDialog
            onClose={() => this.setState({ showAlertModal: false })}
            alert={{
              visible: showAlertModal,
              message: message,
              messageType: messageType,
            }}
          />
        </Portal>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.background,
    height: 150,
    flex: 1,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  text: {
    borderColor: colors.primary,
    borderWidth: 1,
    height: 175,
    borderRadius: 5,
    fontSize: 12,
    marginVertical: 10,
  },
  image: {
    alignContent: 'center',
    aspectRatio: 1.5,
    borderRadius: 5,
    marginBottom: 5,
  },
});
const mapStateToProps = appState => {
  return {
    account: appState.account,
    payment: appState.payment,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    getUserWallet: () => dispatch(PaymentActions.paymentKycStatusRequest()),

    getUserFeedbackStats: userId =>
      dispatch(FeedbackActions.feedbackStatsRequest(userId)),
    getUserReviewStats: userId =>
      dispatch(FeedbackActions.reviewStatsRequest(userId)),
    uploadKycDocuments: data =>
      dispatch(PaymentActions.paymentKycUploadRequest(data)),
    uploadBankAccount: bankData =>
      dispatch(PaymentActions.paymentBankRequest(bankData)),
    setUserInfoRequest: userInfoId =>
      dispatch(CommonActions.setUserInfoRequest(userInfoId)),
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(Profile);
