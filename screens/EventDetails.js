import React, { Component } from 'react';
import {
  ScrollView,
  Image,
  FlatList,
  View,
  StyleSheet,
  Share,
  Linking,
  TouchableOpacity,
  Platform,
} from 'react-native';
import MapView, { Circle } from 'react-native-maps';
import * as Icon from '@expo/vector-icons';
import AlertDialog from '@components/AlertDialog';
import EventsActions from '@reducers/events.reducer';
import { isLoggedIn, getLogin } from '@reducers/account.reducer';
import { getMaxGuestRequest } from '@reducers/common.reducer';
import {
  getTotalParticipants,
  getTotalParticipantsConfirmed,
} from '@reducers/eventKv.reducer';
import FeedbackActions from '@reducers/feedbacks.reducer';
import moment from 'moment';
import {
  Text,
  Button,
  Surface,
  Headline,
  Subheading,
  Paragraph,
  Portal,
  FAB,
  Divider,
  Modal,
  ActivityIndicator,
  Chip,
  IconButton,
} from 'react-native-paper';
import { connect } from 'react-redux';
import {
  colors,
  getIcon,
  DEFAULT_LAT_LON,
  showAvatar,
  _WARNING,
  _SUCCESS,
  _ERROR,
  getCleanLoginFunction,
  getCachedLogin,
  getShareableObject,
  globalStyles,
  typeEvents,
  getIconFromArray,
  EOKV_V_PARTICIPANTS,
  intollerances,
  typeKitchen,
  EVENT_OFFER_KV_STATE_GUEST_REQUEST,
  images,
  getFirstImage,
  typeBeverage,
  getReadableDuration,
  defaultDateTimeFormatClean,
  locations,
  EVENT_OFFER_TYPE_OFFER_PRIVATE,
  getDistanceColor,
  calculateFlyDistance,
  getLocationPermission,
  getDistanceString,
  icon_size_small,
  icon_size_chip,
} from '@constants';
import i18n from '@translations/index';
import EventKVActions from '@reducers/eventKv.reducer';
import { appConfig } from '@config/app-config';
import { USER_INFO_ROUTE } from '@routes/route_constants';
import TagText from '@components/TagText';
import { BottomDialog } from './BottomDialog';
import SwitchSelector from 'react-native-switch-selector';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import * as Location from 'expo-location';
import GridImageView from 'react-native-grid-image-viewer';
import { UserOptionBottomDialog } from './UserOptionBottomDialog';
import * as Calendar from 'expo-calendar';
import { getMapLink, isEventUnlocked } from '@constants/functions';
import Participant from './component/Participant';
import ReviewSummaryCard from './component/ReviewSummaryCard';

class EventDetails extends Component {
  constructor(props) {
    super(props);
    console.log('>>props.navigation', props);
    this.state = {
      nestedloading: false,
      nestedError: null,
      numberOfParticipants: 1,
      request: false,
      hasPermission: false,
      hasService: false,
      id: props.route.params.id,
      userId: props.route.params.userId,
      item: props.route?.params?.item ?? null, // might it be null
      userInfoEnable: props.route.params.userInfoEnable ?? true,
      showParticipationDialog: false,
      fabVisible: true,
      sendParticipantsDisabled: true,
      showAlertModal: false,
      messageType: '',
      message: '',
      feedbackOfUserRequested: false,
      showInfoBottomDialog: false,
      infoDialogText: null,
      infoDialogIcon: images._private,
      currentLocation: null,
      showUserOptionsBottomDialog: false,
      user_nickname: null,
    };
  }

  componentDidMount = async () => {
    const fabVisible = this.props.route.params.showFab ?? true;

    this.setState({ fabVisible });

    console.log('getEventDetail', this.state.id);
    this.props.getEventDetail(this.state.id);
    if (this.props.loggedIn) {
      this.props.getParticipants(this.state.id);
      this.props.getReviewsOfUser(this.state.userId);
      this.props.getUserReviewStats(this.state.userId);
    }

    if (await getLocationPermission()) {
      const { coords } = await Location.getCurrentPositionAsync({});
      console.log('currentLocation', coords);
      this.setState({ currentLocation: coords });
    }
  };

  showOtherInfo = () => {
    const { event } = this.props.events;
    const isPrivate = event && event.type === EVENT_OFFER_TYPE_OFFER_PRIVATE;
    let base = 210;
    const diff = 40;

    const _isPrivate = isPrivate ? (
      <Surface
        style={{
          ...styles.buttonAction,
          right: base,
        }}>
        <TouchableOpacity
          onPress={() =>
            this.showBottomDialog(
              '__event_private_explanation',
              images._private,
            )
          }>
          <Image source={images._private} style={styles.buttonActionIcon} />
        </TouchableOpacity>
      </Surface>
    ) : (
      <></>
    );
    if (isPrivate) {
      base -= diff;
    }

    return <View>{_isPrivate}</View>;
  };

  imageShowcase = () => {
    const { event } = this.props.events;
    if (event.pics && Object.keys(event.pics).length > 1) {
      const pics = Object.values(event.pics).map(uri => uri);
      return (
        <Surface
          style={{
            ...globalStyles.elementCard,
            paddingBottom: 0,
          }}>
          <Subheading>{i18n.t('__gallery')}</Subheading>
          <GridImageView data={pics} />
        </Surface>
      );
    }
    return null;
  };

  showUserProfile = (userId, avatar) => {
    if (this.props.loggedIn && this.state.userInfoEnable) {
      this.props.navigation.push(USER_INFO_ROUTE, { userInfoId: userId });
    }
  };

  showParticipationOrDialog = showParticipationDialog => {
    if (this.props.loggedIn) {
      this.setState({ showParticipationDialog });
    } else {
      this.setState({
        showAlertModal: true,
        messageType: _WARNING,
        message: '__user_not_logged_in',
      });
    }
  };

  _showModal = () => this.setState({ showParticipationDialog: true });
  _hideModal = () => this.setState({ showParticipationDialog: false });

  sendParticipantsRequest = () => {
    const eventKv = {
      k: EOKV_V_PARTICIPANTS,
      state: EVENT_OFFER_KV_STATE_GUEST_REQUEST,
      event: {
        id: this.state.id,
      },
      v: this.state.numberOfParticipants,
      userId: this.props.login,
    };
    this.props.createParticipant({ eventKv });
  };

  async componentDidUpdate(prevProps) {
    if (this.state.user_nickname === null && this.props.events.event !== null) {
      const user_nickname = await getCachedLogin(
        this.props.events.event.userId,
      );
      this.setState({ user_nickname });
    }

    if (prevProps.events.fetchingOne && !this.props.events.fetchingOne) {
      if (this.props.events.errorOne !== null) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__server_error_get_events',
        });
      }
    }

    if (prevProps.eventKv.updating && !this.props.eventKv.updating) {
      if (this.props.eventKv.errorUpdating) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: this.props.eventKv.errorUpdating.title, // '__server_error_get_events',
        });
      } else {
        this.setState({
          showAlertModal: true,
          message: '__req_partic_ok',
          messageType: _SUCCESS,
        });
      }
    }
  }

  getDefaultCalendarSource = async () => {
    const defaultCalendar = await Calendar.getDefaultCalendarAsync();
    return defaultCalendar.source;
  };

  addToCalendar = async item => {
    const { status } = await Calendar.requestCalendarPermissionsAsync();
    if (status === 'granted') {
      this.setState({ nestedloading: true });
      const calendars = await Calendar.getCalendarsAsync(
        Calendar.EntityTypes.EVENT,
      );
      const calendar = calendars.filter(
        c => c.title === appConfig.appBrandName,
      );
      let calendarId;
      if (calendar?.length > 0) {
        calendarId = calendar[0].id;
      } else {
        const defaultCalendarSource =
          Platform.OS === 'ios'
            ? await this.getDefaultCalendarSource()
            : { isLocalAccount: true, name: appConfig.appBrandName };
        calendarId = await Calendar.createCalendarAsync({
          title: appConfig.appBrandName,
          color: 'red',
          entityType: Calendar.EntityTypes.EVENT,
          sourceId: defaultCalendarSource.id,
          source: defaultCalendarSource,
          name: 'internalCalendarName',
          ownerAccount: 'personal',
          accessLevel: Calendar.CalendarAccessLevel.OWNER,
        });
      }

      const startEventDate = moment(item.date);
      const endEventDate = startEventDate.clone().add(item.duration, 'minutes');
      try {
        await Calendar.createEventAsync(calendarId, {
          organizer: getCleanLoginFunction(item.user_login),
          startDate: startEventDate.toDate(),
          endDate: endEventDate.toDate(),
          status: 'CONFIRMED',
          title: `${item.name}`,
          location: getMapLink({
            name: item.name,
            lat: item.lat,
            lon: item.lon,
          }),
          notes: `${i18n.t('__host')}: ${getCleanLoginFunction(
            item.user_login,
          )}\n${item.presentation}`,
        });
        this.setState({
          nestedloading: false,
          nestedError: null,
        });
      } catch (e) {
        this.setState({
          nestedloading: false,
          nestedError: '__calendar_event_added_error_desc',
        });
      }
    } else {
      this.setState({
        nestedloading: false,
        nestedError: '__calendar_permissions',
      });
    }
  };

  showOpenMap = () => {
    const { event } = this.props.events;
    const { item } = this.state;
    // const item = null;

    return isEventUnlocked(item) ? (
      <IconButton
        onPress={() =>
          Linking.openURL(
            getMapLink({
              name: event.name,
              lat: event.lat,
              lon: event.lon,
            }),
          )
        }
        size={24}
        color={colors.primary}
        icon={({ size, color }) => (
          <Icon.MaterialCommunityIcons
            name="map-check"
            size={size}
            color={color}
          />
        )}
      />
    ) : (
      <></>
    );
  };

  checkedParticipants = () => (
    <Surface style={globalStyles.elementCard}>
      <Subheading>
        {i18n.t('__participants')}:
        <Icon.Entypo name="dot-single" color={colors.primary} size={20} />{' '}
        {this.props.loggedIn && this.props.eventKv.eventKvs
          ? `${this.props.getTotalParticipantsConfirmed} ${i18n.t(
              '__confirmed',
            )} / ${this.props.getTotalParticipants} ${i18n.t('__requested')}`
          : 0}{' '}
        / {this.props.events.event.maxPartecipants} {i18n.t('__available')}
      </Subheading>
      {!this.props.loggedIn ? (
        <Text>{i18n.t('__login_needed')}</Text>
      ) : (
        <FlatList
          horizontal
          keyExtractor={(item, index) => index.toString()}
          showsHorizontalScrollIndicator={false}
          data={this.props.eventKv.eventKvs}
          renderItem={({ item }) => <Participant item={item} />}
        />
      )}
    </Surface>
  );

  shareHandle = async () => {
    try {
      const { event } = this.props.events;
      console.log('shareHandle ', event);
      const result = await Share.share(getShareableObject(event, true));
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
          console.log('shareHandle result with activity', result);
        } else {
          // shared
          console.log('shareHandle result', result);
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
        console.log('shareHandle result dismissedAction', result);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  showBottomDialogWithAction = (message, icon) => {
    this.setState({
      showUserOptionsBottomDialog: true,
      infoDialogText: message,
      infoDialogIcon: icon,
    });
    //to reset trigger for future time
    setTimeout(
      () => this.setState({ showUserOptionsBottomDialog: false }),
      1000,
    );
  };

  showBottomDialog = (message, icon) => {
    this.setState({
      showInfoBottomDialog: true,
      infoDialogText: message,
      infoDialogIcon: icon,
    });
    //to reset trigger for future time
    setTimeout(() => this.setState({ showInfoBottomDialog: false }), 1000);
  };

  render() {
    const {
      showParticipationDialog,
      showAlertModal,
      fabVisible,
      showInfoBottomDialog,
      showUserOptionsBottomDialog,
      infoDialogText,
      infoDialogIcon,
      currentLocation,
      item,
      message,
      messageType,
    } = this.state;
    const { event, errorOne, fetchingOne } = this.props.events;
    const { updating } = this.props.eventKv;
    const coordinate = {
      latitude: event?.lat ?? DEFAULT_LAT_LON,
      longitude: event?.lon ?? DEFAULT_LAT_LON,
    };
    const distance =
      currentLocation !== null && event
        ? calculateFlyDistance(
            event.lat,
            event.lon,
            currentLocation.latitude,
            currentLocation.longitude,
          )
        : null;

    if (fetchingOne) {
      return (
        <View style={globalStyles.container}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      );
    } else if (event === null || errorOne !== null) {
      return (
        <View style={globalStyles.container}>
          <Icon.MaterialIcons
            name="event-busy"
            color={colors.error}
            size={80}
          />
          <Text style={{ color: colors.black }}>
            {`${i18n.t('__no_event_data')}\n${i18n.t('__check_connection')}`}
          </Text>
        </View>
      );
    } else {
      // console.log('event is >>>', event);
      return (
        <View style={{ backgroundColor: colors.background, flex: 1 }}>
          <ScrollView
            style={{
              backgroundColor: colors.background,
              position: 'relative',
            }}>
            <Image
              source={getFirstImage(event)}
              style={{ width: '100%', height: 280, position: 'absolute' }}
            />
            <View style={{ marginTop: 200, paddingBottom: 80 }}>
              <View>
                <Surface style={styles.view}>
                  <Surface style={{ ...styles.pop, left: 20 }}>
                    {showAvatar(
                      event.userId,
                      this.props.loggedIn,
                      60,
                      this.showUserProfile,
                    )}
                  </Surface>
                  <Surface style={{ ...styles.pop, right: 20 }}>
                    <Text
                      style={{
                        color: colors.error,
                        fontSize: event.pricepp.toString().length > 3 ? 24 : 30,
                        fontStyle: 'normal',
                        fontWeight: 'bold',
                      }}>
                      {event.pricepp}
                    </Text>
                  </Surface>
                  <Surface
                    style={{
                      position: 'absolute',
                      top: 15,
                      width: 35,
                      height: 35,
                      borderWidth: 5,
                      borderColor: 'white',
                      borderRadius: 20,
                      elevation: 10,
                      right: 10,
                    }}>
                    <Icon.MaterialIcons
                      size={styles.iconSize}
                      color={colors.error}
                      name="euro-symbol"
                    />
                  </Surface>
                  {this.showOtherInfo()}
                  <Headline style={{ marginTop: 20 }}>
                    <TagText value={event ? event.name : ''} />
                  </Headline>

                  {this.props.loggedIn ? (
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'flex-end',
                      }}>
                      <IconButton
                        onPress={() =>
                          this.showBottomDialogWithAction(
                            '__covid_free_description',
                            images._covidfree,
                          )
                        }
                        size={24}
                        color={colors.declined}
                        icon={({ size, color }) => (
                          <Icon.MaterialCommunityIcons
                            name="dots-horizontal"
                            color={color}
                            size={size}
                          />
                        )}
                      />
                    </View>
                  ) : null}
                  <View style={globalStyles.rowProp}>
                    <Icon.MaterialIcons
                      size={styles.iconSize}
                      color={colors.black}
                      name="face"
                    />

                    <Text style={globalStyles.text}>
                      {this.props.loggedIn
                        ? this.state.user_nickname
                        : i18n.t('__login_needed')}
                    </Text>
                  </View>
                  <Divider style={globalStyles.marginDivider2} />
                  <View style={globalStyles.rowProp}>
                    <Icon.Feather
                      size={styles.iconSize}
                      color={colors.black}
                      name="clock"
                    />
                    <Text style={globalStyles.text}>
                      {getReadableDuration(event.duration)}
                    </Text>
                  </View>
                  <Divider style={globalStyles.marginDivider2} />
                  <View style={globalStyles.rowProp}>
                    <Icon.Feather
                      size={styles.iconSize}
                      color={colors.black}
                      name="calendar"
                    />
                    <Text style={globalStyles.text}>
                      {event
                        ? moment(event.date).format(defaultDateTimeFormatClean)
                        : ''}
                    </Text>
                  </View>
                  <Divider style={globalStyles.marginDivider2} />
                  <View style={globalStyles.rowProp}>
                    <Icon.MaterialCommunityIcons
                      size={styles.iconSize}
                      color={colors.black}
                      name="map-marker-distance"
                    />
                    <Text
                      style={{
                        ...globalStyles.text,
                        color: getDistanceColor(distance),
                      }}>
                      {distance ? getDistanceString(distance) : 'N/A'}
                    </Text>
                  </View>
                </Surface>
                <Surface style={globalStyles.elementCard}>
                  <Subheading>{i18n.t('__drink')}</Subheading>
                  <View
                    style={{ flex: 1, flexWrap: 'wrap', flexDirection: 'row' }}>
                    {event.typeBs.map((prop, key) => (
                      <Chip style={{ marginRight: 10, marginTop: 5 }} key={key}>
                        <Image
                          style={{
                            width: icon_size_chip,
                            height: icon_size_chip,
                          }}
                          source={getIconFromArray(typeBeverage, prop.name)}
                        />{' '}
                        {i18n.t(prop.name)}
                      </Chip>
                    ))}
                  </View>
                </Surface>
                <Surface style={globalStyles.elementCard}>
                  <Subheading>{i18n.t('__event')}</Subheading>
                  <View
                    style={{ flex: 1, flexWrap: 'wrap', flexDirection: 'row' }}>
                    {event.typeEs.map((prop, key) => (
                      <Chip style={{ marginRight: 10, marginTop: 5 }} key={key}>
                        <Image
                          style={{
                            width: icon_size_chip,
                            height: icon_size_chip,
                          }}
                          source={getIconFromArray(typeEvents, prop.name)}
                        />{' '}
                        {i18n.t(prop.name)}
                      </Chip>
                    ))}
                  </View>
                </Surface>
                <Surface style={globalStyles.elementCard}>
                  <Subheading>{i18n.t('__kitchen')}</Subheading>
                  <View
                    style={{ flex: 1, flexWrap: 'wrap', flexDirection: 'row' }}>
                    {event.typeKs.map((prop, key) => (
                      <Chip style={{ marginRight: 10, marginTop: 5 }} key={key}>
                        <Image
                          style={{
                            width: icon_size_chip,
                            height: icon_size_chip,
                          }}
                          source={getIconFromArray(typeKitchen, prop.name)}
                        />{' '}
                        {i18n.t(prop.name)}
                      </Chip>
                    ))}
                  </View>
                </Surface>
                <Surface style={globalStyles.elementCard}>
                  <Subheading>{i18n.t('__location')}</Subheading>
                  <View
                    style={{ flex: 1, flexWrap: 'wrap', flexDirection: 'row' }}>
                    {event.typeLs.map((prop, key) => (
                      <Chip style={{ marginRight: 10, marginTop: 5 }} key={key}>
                        <Image
                          style={{
                            width: icon_size_chip,
                            height: icon_size_chip,
                          }}
                          source={getIconFromArray(locations, prop.name)}
                        />{' '}
                        {i18n.t(prop.name)}
                      </Chip>
                    ))}
                  </View>
                </Surface>
                <Surface style={globalStyles.elementCard}>
                  <Subheading>{i18n.t('__allergens')}</Subheading>
                  <View
                    style={{ flex: 1, flexWrap: 'wrap', flexDirection: 'row' }}>
                    {event.intolerances && event.intolerances.length > 0 ? (
                      event.intolerances.map((prop, key) => (
                        <Chip
                          style={{ marginRight: 10, marginTop: 5 }}
                          key={key}>
                          <Image
                            style={{
                              width: icon_size_chip,
                              height: icon_size_chip,
                            }}
                            source={getIconFromArray(intollerances, prop.name)}
                          />{' '}
                          {i18n.t(prop.name)}
                        </Chip>
                      ))
                    ) : (
                      <Paragraph>{i18n.t('__no_food_intol')}</Paragraph>
                    )}
                  </View>
                </Surface>

                {this.imageShowcase()}

                <Surface
                  style={{ ...globalStyles.elementCard, height: hp('20%') }}>
                  <Subheading>{i18n.t('__description')}</Subheading>
                  <ScrollView>
                    <Paragraph>
                      <TagText value={event ? event.presentation : ''} />
                    </Paragraph>
                  </ScrollView>
                </Surface>
                {this.checkedParticipants()}
                <Surface style={globalStyles.elementCard}>
                  <View
                    style={{
                      flexDirection: 'row',
                      flex: 1,
                      alignItems: 'center',
                    }}>
                    <Subheading>{i18n.t('__location')}</Subheading>
                    {this.showOpenMap()}
                  </View>

                  {event ? (
                    <MapView
                      scrollEnabled={false}
                      zoomEnabled={true}
                      rotateEnabled={false}
                      pitchEnabled={true}
                      maxZoomLevel={14}
                      style={{
                        height: 200,
                        width: '100%',
                      }}
                      initialRegion={{
                        latitude: event?.lat ?? DEFAULT_LAT_LON,
                        longitude: event?.lon ?? DEFAULT_LAT_LON,
                        latitudeDelta: 0.02,
                        longitudeDelta: 0.02,
                      }}>
                      <Circle
                        center={coordinate}
                        radius={500}
                        zIndex={20}
                        fillColor="rgba(250,0,0,0.3)"
                        strokeColor="red"
                        strokeWidth={3}
                      />
                    </MapView>
                  ) : (
                    <View
                      style={{
                        flex: 1,
                        justifyContent: 'center',
                        alignItems: 'center',
                        height: 200,
                        width: '100%',
                      }}>
                      <ActivityIndicator size="small" />
                    </View>
                  )}
                </Surface>
                <ReviewSummaryCard
                  reviews={this.props.feedbacks.reviews}
                  reviewStats={this.props.feedbacks.reviewStats}
                />
              </View>
            </View>
          </ScrollView>
          <FAB
            visible={fabVisible && this.props.login !== event.userId}
            icon={getIcon('add')}
            style={styles.fab}
            onPress={() => this.showParticipationOrDialog(true)}
            label={i18n.t('__participate')}
          />
          <Portal>
            <Modal
              visible={showParticipationDialog}
              onDismiss={this._hideModal}>
              <Surface style={{ padding: 20, margin: 20, borderRadius: 10 }}>
                <Headline style={{ fontFamily: 'medium', marginBottom: 10 }}>
                  {i18n.t('__req_partic')}
                </Headline>
                <Paragraph style={{ fontFamily: 'medium' }}>{`${
                  event.pricepp
                } € x ${i18n.t('__person')}`}</Paragraph>
                <View style={{ margin: 20, alignItems: 'center' }}>
                  <SwitchSelector
                    initial={0}
                    onPress={value =>
                      this.setState({ numberOfParticipants: Number(value) })
                    }
                    textColor={colors.gray} //'#7a44cf'
                    selectedColor={colors.primary}
                    buttonColor={colors.success}
                    borderColor={colors.primary}
                    hasPadding
                    options={[
                      {
                        label: 'Alone',
                        value: '1',
                        testID: 'choice1',
                        customIcon: (
                          <Icon.MaterialIcons size={30} name="person" />
                        ),
                      },
                      {
                        label: '1 + 1',
                        value: '2',
                        testID: 'choice2',
                        customIcon: (
                          <Icon.MaterialIcons size={30} name="people" />
                        ),
                      },
                    ]}
                    testID="guest_participation_switch_selector"
                    accessibilityLabel="guest_participation"
                  />
                </View>
                <Subheading>
                  {i18n.t('__total') + ' '}
                  <Text style={{ color: colors.success }}>
                    {(this.state.numberOfParticipants * event.pricepp).toFixed(
                      1,
                    )}
                    €
                  </Text>
                </Subheading>
                <Button
                  mode="contained"
                  disabled={updating !== null && updating}
                  loading={updating}
                  onPress={this.sendParticipantsRequest}>
                  {i18n.t('__continue')}
                </Button>
                <View
                  style={{
                    marginTop: 10,
                    marginBottom: -10,
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                  }}>
                  <Text style={{ marginEnd: 10, color: colors.black }}>
                    {i18n.t('__participation_fee')}
                  </Text>
                  <Text
                    style={{ color: colors.blue }}
                    onPress={() =>
                      Linking.openURL(appConfig.conditionPolicyUrl)
                    }>
                    {i18n.t('__link')}
                  </Text>
                </View>
              </Surface>
            </Modal>
            <AlertDialog
              toShare={
                messageType === _SUCCESS ? () => this.shareHandle() : undefined
              }
              onClose={() => {
                this.setState({
                  showAlertModal: false,
                  showParticipationDialog: false,
                });
              }}
              alert={{
                visible: showAlertModal,
                message: message,
                messageType: messageType,
              }}
            />
          </Portal>
          <BottomDialog
            visible={showInfoBottomDialog}
            message={i18n.t(infoDialogText)}
            icon={infoDialogIcon}
          />
          {this.props.loggedIn ? (
            <UserOptionBottomDialog
              event={event}
              visible={showUserOptionsBottomDialog}
              message={i18n.t(infoDialogText)}
              icon={infoDialogIcon}
              externalAction={{
                loading: this.state.nestedloading,
                error: this.state.nestedError,
              }}
              customActions={
                isEventUnlocked(item)
                  ? [
                      {
                        text: '__add_calendar_event',
                        icon: images._calendar_big,
                        f: () => this.addToCalendar(item),
                      },
                    ]
                  : []
              }
            />
          ) : null}
        </View>
      );
    }
  }
}
const mapStateToProps = appState => {
  return {
    events: appState.events,
    feedbacks: appState.feedbacks,
    eventKv: appState.eventkv,
    getTotalParticipantsConfirmed: getTotalParticipantsConfirmed(
      appState.eventkv,
    ),
    getTotalParticipants: getTotalParticipants(appState.eventkv),
    account: appState.account,
    loggedIn: isLoggedIn(appState.account),
    login: getLogin(appState.account),
    common: appState.common,
    getMaxGuestRequest: getMaxGuestRequest(appState.common),
  };
};
function mapDispatchToProps(dispatch) {
  return {
    getEventDetail: eventId => dispatch(EventsActions.eventRequest(eventId)),
    getReviewsOfUser: userId =>
      dispatch(FeedbackActions.reviewsAllRequest(userId)),
    getUserReviewStats: userId =>
      dispatch(FeedbackActions.reviewStatsRequest(userId)),
    getParticipants: eventKvId =>
      dispatch(EventKVActions.eventKvAllByIdRequest(eventKvId)),
    createParticipant: eventKv =>
      dispatch(EventKVActions.eventKvUpdateRequest(eventKv)),
  };
}

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
  },
  iconPadding: {
    paddingTop: 10,
  },
  centerView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconSize: 24,
  pop: {
    position: 'absolute',
    top: -30,
    width: 70,
    height: 70,
    borderWidth: 5,
    borderColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 40,
    elevation: 10,
  },
  view: {
    marginHorizontal: 20,
    marginVertical: 10,
    elevation: 10,
    padding: 20,
    paddingTop: 40,
    borderRadius: 20,
    position: 'relative',
  },
  fab: {
    position: 'absolute',
    backgroundColor: colors.primary,
    color: 'white',
    zIndex: 100,
    elevation: 50,
    margin: 20,
    right: 70,
    left: 70,
    bottom: 0,
  },
  buttonAction: {
    position: 'absolute',
    top: -30,
    width: 35,
    height: 35,
    borderWidth: 5,
    borderColor: 'white',
    borderRadius: 20,
    elevation: 10,
  },
  buttonActionIcon: {
    marginBottom: 10,
    width: icon_size_small,
    height: icon_size_small,
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(EventDetails);
