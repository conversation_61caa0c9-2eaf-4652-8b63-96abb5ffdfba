import React, { Component } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Headline, TextInput, Button } from 'react-native-paper';
import StarRating from 'react-native-star-rating';

import { colors, _ERROR, _SUCCESS, REVIEW_HOST_2_GUEST } from '@constants';
import i18n from '@translations/index';
import FeedbackActions from '@reducers/feedbacks.reducer';
import AlertDialog from '@components/AlertDialog';
import { connect } from 'react-redux';
import { getLogin } from '@reducers/account.reducer';
import { Input } from '@components/Input';
import { INPUT_TYPE_MULTIIMAGE_PICKER } from '@constants/constants';
import { ScrollView } from 'react-native-gesture-handler';
import * as utilityAction from '@actions/utility.action';

class ReviewComponent extends Component {

  constructor(props) {
    super(props);
    this.state = {
      __hospitality_score: null,
      __fun_score: null,
      __location_score: null,
      __food_score: null,

      showAlertModal: false,
      messageType: '',
      message: '',

      isNotFilled: true,
      description: '',
      point: -1,
      item: props.route.params.item,
      type: props.route.params.type,
      value: [],
    };
  }

  componentDidUpdate = prevProps => {
    if (
      this.props.feedbacks.updating === false &&
      prevProps.feedbacks.updating === true
    ) {
      if (this.props.feedbacks.errorUpdating) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: this.props.feedbacks.errorUpdating.title,
        });
      } else {
        this.setState({
          showAlertModal: true,
          messageType: _SUCCESS,
          message: '__review_created_ok',
          navigateBack: true,
        });
      }
    }
  };

  serverRequest = async () => {
    const {
      __fun_score,
      __food_score,
      __location_score,
      __hospitality_score,
      item,
      description,
      value,
    } = this.state;
    // load images
    let picsObject = { isNsfw: false };
    if (value.length > 0) {
      const returnedImages = await Promise.all(
        value.map(picPath => this.props.uploadEventFile(picPath)),
      );
      returnedImages
        .map(p => p.payload)
        .forEach((e, i) => {
          picsObject['pic_' + i] = e.fileDownloadUri;
          if (e.properties?.race === true || e.properties?.adult === true) {
            picsObject.isNsfw = true;
          }
        });
    }

    console.log('createReview', item);
    const review = {
      description: description,
      eventOffer: {
        id: item.id,
      },
      type: REVIEW_HOST_2_GUEST,
      senderId: this.props.username,
      receiverId: item.user_id,
      point:
        ((__food_score + __fun_score + __location_score + __hospitality_score) *
          100) /
        4,
      foodScore: __food_score * 100,
      funScore: __fun_score * 100,
      locationScore: __location_score * 100,
      hospitalityScore: __hospitality_score * 100,
      content: {
        ...picsObject,
      },
    };
    // console.log('review >> ', review);
    this.props.createReview(review);
  };

  picsWidget = () => {
    const { value } = this.state;
    return (
      <Input
        type={INPUT_TYPE_MULTIIMAGE_PICKER}
        value={value}
        imageNumber={5}
        change={nextValue => {
          if (nextValue && nextValue.length >= 0) {
            this.setState({ value: nextValue });
          }
        }}
      />
    );
  };

  isAllFilled = () => {
    const {
      __hospitality_score,
      __fun_score,
      __food_score,
      __location_score,
      description,
    } = this.state;
    if (
      __hospitality_score !== null &&
      __fun_score !== null &&
      __food_score !== null &&
      __location_score !== null
    ) {
      this.setState({ isNotFilled: description.length < 5 });
    }
  };

  setRating = (type, rating) => {
    this.setState({ [type]: rating }, () => this.isAllFilled());
  };

  setDescription = description => {
    this.setState({ description }, () => this.isAllFilled());
  };

  render() {
    const { showAlertModal, message, messageType, isNotFilled } = this.state;
    const { updating } = this.props.feedbacks;

    return (
      <ScrollView>
        <View
          style={{
            backgroundColor: colors.background,
            padding: 20,
            flex: 1,
          }}>
          <View style={{ alignItems: 'center' }}>
            <Headline>{i18n.t('__rate_host')}</Headline>
          </View>
          <View style={{ flex: 1 }}>
            <View style={styles.reviewRow}>
              <Text style={{ color: colors.black, marginBottom: 20 }}>
                {i18n.t('__food_score')}
              </Text>
              <StarRating
                starSize={25}
                disabled={false}
                maxStars={5}
                rating={this.state.__food_score}
                fullStarColor={colors.fullStar}
                selectedStar={rating => this.setRating('__food_score', rating)}
              />
            </View>
            <View style={styles.reviewRow}>
              <Text
                style={{
                  color: colors.black,
                  fontFamily: 'medium',
                  marginBottom: 20,
                }}>
                {i18n.t('__fun_score')}
              </Text>
              <StarRating
                starSize={25}
                disabled={false}
                maxStars={5}
                rating={this.state.__fun_score}
                fullStarColor={colors.fullStar}
                selectedStar={rating => this.setRating('__fun_score', rating)}
              />
            </View>
            <View style={styles.reviewRow}>
              <Text style={{ color: colors.black, marginBottom: 20 }}>
                {i18n.t('__hospitality_score')}
              </Text>
              <StarRating
                starSize={25}
                disabled={false}
                maxStars={5}
                rating={this.state.__hospitality_score}
                fullStarColor={colors.fullStar}
                selectedStar={rating =>
                  this.setRating('__hospitality_score', rating)
                }
              />
            </View>
            <View style={styles.reviewRow}>
              <Text style={{ color: colors.black, marginBottom: 20 }}>
                {i18n.t('__location_score')}
              </Text>
              <StarRating
                starSize={25}
                disabled={false}
                maxStars={5}
                rating={this.state.__location_score}
                fullStarColor={colors.fullStar}
                selectedStar={rating =>
                  this.setRating('__location_score', rating)
                }
              />
            </View>
          </View>
          <TextInput
            label={i18n.t('__your_comment')}
            placeholder={i18n.t('__your_comment_placeholder_host')}
            onChangeText={description => this.setDescription(description)}
            // multiline
            maxLength={255}
            theme={{ colors: { primary: colors.blue } }}
            style={{ width: '100%', marginTop: 10 }}
          />
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
            }}>
            {/* <HelperText
              visible
              style={{
                textAlign: 'right',
              }}>
              {this.state.description.length} / {255}
            </HelperText> */}
          </View>
          {/* {this.picsWidget()} */}
          <Button
            mode="contained"
            loading={updating}
            disabled={updating || isNotFilled}
            onPress={this.serverRequest}
            contentStyle={{ padding: 5 }}
            style={{ width: '100%', marginTop: 10 }}>
            {updating ? i18n.t('__sending') : i18n.t('__send')}
          </Button>

          <AlertDialog
            onClose={() => {
              if (this.state.navigateBack) {
                this.props.navigation.goBack();
              }
              this.setState({ showAlertModal: false, navigateBack: false });
            }}
            alert={{
              visible: showAlertModal,
              message: message,
              messageType: messageType,
            }}
          />
        </View>
      </ScrollView>
    );
  }
}

const mapStateToProps = appState => {
  return {
    feedbacks: appState.feedbacks,
    username: getLogin(appState.account),
    authorization: appState.authorization,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    createReview: review =>
      dispatch(FeedbackActions.reviewUpdateRequest(review)),
    uploadEventFile: file => dispatch(utilityAction.uploadEventFile(file)),
  };
}
export const Review = connect(
  mapStateToProps,
  mapDispatchToProps,
)(ReviewComponent);

const styles = StyleSheet.create({
  reviewRow: {
    borderWidth: 1,
    backgroundColor: colors.white,
    borderColor: colors.black,
    borderRadius: 20,
    elevation: 10,
    padding: 10,
    paddingLeft: 10,
    paddingRight: 10,
    marginVertical: 10,
  },
});
