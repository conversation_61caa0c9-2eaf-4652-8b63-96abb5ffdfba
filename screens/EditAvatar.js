import React, { Component } from 'react';
import { <PERSON><PERSON>View } from 'react-native';
import { Button } from 'react-native-paper';
import { colors, editAvatarFormSchema, _ERROR } from '@constants';
import { Input } from '@components';
import validate from '@helpers/validation';
import AlertDialog from '@components/AlertDialog';
import * as utilityAction from '@actions/utility.action';
import { connect } from 'react-redux';
import i18n from '@translations/index';
import AccountActions from '@reducers/account.reducer';
import { DRAWER_PROFILE_ROUTE, PROFILE_ROUTE } from '@routes/route_constants';

const constraints = {
  image: {
    presence: { allowEmpty: false },
  },
};

class Main extends Component {
  constructor(props) {
    super(props);
    const form = {
      ...editAvatarFormSchema,
    };
    form.avatar.pickImageHandlerError = e =>
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: i18n.t(e),
      });
    form.avatar.value = this.props.account.account
      ? this.props.account.account.imageUrl
      : undefined;
    this.state = {
      request: false,
      form,
      showAlertModal: false,
      unselected: true,
    };
  }

  generateForm = (structure, change, state) => {
    const fields = Object.keys(structure).map(key => ({
      ...structure[key],
      key,
    }));

    return fields.map(field => (
      <Input {...field} _key={field.key} change={change} />
    ));
  };

  componentDidUpdate(prevProps) {
    if (prevProps.account.fetching && !this.props.account.fetching) {
      if (this.props.account.error) {
        this.setState({
          message: this.props.account.error,
          messageType: _ERROR,
          showAlertModal: true,
          request: false,
        });
      } else {
        this.setState({ request: false });
        this.props.navigation.navigate(PROFILE_ROUTE);
      }
    }
  }

  serverRequest = async () => {
    console.log('serverRequest ', this.state.form.avatar);
    const obj = {
      image: this.state.form.avatar.value,
    };
    const errors = Object.values(validate(obj, constraints) || {});

    if (errors.length > 0) {
      console.log('err...', errors[0][0]);
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: errors[0][0],
      });
      return;
    }

    this.setState({ request: true });
    try {
      const action = await this.props.uploadFile(obj.image);
      if (!action.payload.ok) {
        throw action.payload;
      }
      const userDataPayload = {
        ...this.props.account.account,
        imageUrl: action.payload.data.blobUrl,
      };

      /*
       if fails maybe you are testing with user that misses properties, like ones that got failed
       */
      this.props.saveUserData(userDataPayload);
    } catch (e) {
      console.log('Action e -- ', e);
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: '__server_error_save_image',
        request: false,
      });
    }
  };

  changeHandler = (text, field) => {
    const { form } = this.state;
    form[field].value = text[0];

    this.setState({ form, unselected: false });
  };

  storeInputRef = ref => (this.input = ref);

  render() {
    const { form, showAlertModal, request, message, messageType, unselected } =
      this.state;

    return (
      <ScrollView style={{ padding: 20, backgroundColor: colors.background }}>
        {this.generateForm(form, this.changeHandler)}
        <Button
          mode="contained"
          style={{ marginBottom: 40 }}
          contentStyle={{ padding: 5 }}
          loading={request}
          disabled={unselected || request}
          onPress={this.serverRequest}>
          {request ? i18n.t('__saving') : i18n.t('__save')}
        </Button>

        <AlertDialog
          onClose={() => {
            this.setState({ showAlertModal: false });
          }}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
      </ScrollView>
    );
  }
}
const mapStateToProps = appState => {
  return {
    common: appState.common,
    account: appState.account,
  };
};

function mapDispatchToProps(dispatch) {
  return {
    uploadFile: file => dispatch(utilityAction.uploadFile(file)),
    saveUserData: data => dispatch(AccountActions.accountUpdateRequest(data)),
  };
}

export const EditAvatar = connect(mapStateToProps, mapDispatchToProps)(Main);
