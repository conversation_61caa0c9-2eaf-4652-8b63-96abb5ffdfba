import React, { Component } from 'react';
import { ScrollView } from 'react-native';
import { Helper<PERSON>ex<PERSON>, Button } from 'react-native-paper';
import { connect } from 'react-redux';
import validate from '@helpers/validation';
import AlertDialog from '@components/AlertDialog';

import {
  colors,
  PARAMETER_PREFERENCE_PREFIX,
  _ERROR,
  PARAMETER_PREFERENCE_INTOLLERANCE,
  PARAMETER_PREFERENCE_USER_DESCRIPTION,
  PARAMETER_PREFERENCE_USER_GENDER,
  userDataConstraints,
} from '@constants';
import { Input } from '@components';
import i18n from '@translations/index';
import AccountActions from '@reducers/account.reducer';
import { intollerances } from '@constants/intollerances';
import { PROFILE_ROUTE } from '@routes/route_constants';
import userPreferencesConstraints, {
  editProfileFormSchema,
} from '@constants/schemas';
import DateDialog from '@components/DateDialog';

class EditProfile extends Component {
  constructor(props) {
    super(props);
    const userData = props.account.account || {};
    const form = editProfileFormSchema;

    form.nickname.value = userData.nickname || userData.login;
    form.firstName.value = userData.firstName || '';
    form.lastName.value = userData.lastName || '';
    form.address.value = userData.address || '';
    form.city.value = userData.city || '';
    form.country.value = userData.country || '';
    form.postcode.value = userData.postcode || '';
    form.dob.value = userData.dob || '';
    form.gender.value = this.getUserPreference('user_gender');
    form.description.value = this.getUserPreference('user_description');
    form.intollerances.value = this.getUserIntolerence();

    this.state = {
      showPermissionDialog: false,
      showErrorDialog: false,
      form,
      showDate: false,
    };
  }

  /**
   * makes the key using the prefix pattern and find it in the array
   * @param {string} key
   */
  getUserPreference(key) {
    const patternKey = PARAMETER_PREFERENCE_PREFIX + key;
    const preference = (this.props.account.preferences || []).find(
      item => item.k === patternKey,
    );
    if (preference) {
      return preference.v;
    }
    return '';
  }

  getUserIntolerence() {
    const preference = (this.props.account.preferences || []).filter(item =>
      item.k.includes(PARAMETER_PREFERENCE_INTOLLERANCE),
    );
    return preference
      .map(item => item.v)
      .filter(value => {
        return intollerances.map(item => item.id).indexOf(value) !== -1;
      });
  }

  processForm() {
    let account = {
      ...this.props.account.account,
      nickname: this.state.form.nickname.value,
      firstName: this.state.form.firstName.value,
      lastName: this.state.form.lastName.value,
      address: this.state.form.address.value,
      postcode: this.state.form.postcode.value,
      city: this.state.form.city.value,
      country: this.state.form.country.value,
      dob: this.state.form.dob.value,
    };

    let preferences = {};
    preferences[PARAMETER_PREFERENCE_USER_GENDER] =
      this.state.form.gender.value;
    preferences[PARAMETER_PREFERENCE_USER_DESCRIPTION] =
      this.state.form.description.value;

    let errors = Object.values(validate(account, userDataConstraints) || {});
    errors = errors.concat(
      Object.values(validate(preferences, userPreferencesConstraints) || {}),
    );
    if (errors.length > 0) {
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: errors[0][0],
      });
      return;
    }

    (this.state.form.intollerances.value || []).forEach((item, index) => {
      preferences[PARAMETER_PREFERENCE_INTOLLERANCE + index] = item;
    });
    return {
      account,
      preferences,
    };
  }

  serverRequest = () => {
    const results = this.processForm();
    if (!results) {
      return;
    }
    const { account, preferences } = results;
    this.props.saveUserData(account);

    this.props.saveUserPreferences(preferences);
  };

  componentDidUpdate(prevProps) {
    if (
      !this.props.account.fetchingPreferences &&
      prevProps.account.fetchingPreferences
    ) {
      if (this.props.account.error) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__server_error_save_user_data',
        });
      }
    }
    if (!this.props.account.fetching && prevProps.account.fetching) {
      if (this.props.account.error) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__server_error_save_user_data',
        });
      } else {
        this.props.navigation.navigate(PROFILE_ROUTE);
      }
    }
  }

  changeHandler = (text, field) => {
    const { form } = this.state;

    if (field === 'country') {
      form[field].value = text.name;
    } else if (field === 'intollerances') {
      form[field].value = text;
    } else {
      form[field].value = text;
    }
    this.setState({ form });
  };

  dismissErrorModal = () => {
    this.setState({ showErrorDialog: false });
  };

  dateCancelHandler = () => {
    this.setState({ showDate: false });
  };

  showDateHandler = () => {
    this.setState({ showDate: true });
  };

  dateConfirmHandler = e => {
    const { form } = this.state;
    form.dob.value = e;
    this.setState({
      showDate: false,
      form,
    });
  };

  generateForm = (structure, change, state) => {
    const fields = Object.keys(structure).map(key => ({
      ...structure[key],
      key,
    }));
    return fields.map(field => (
      <Input
        {...field}
        _key={field.key}
        change={change}
        showDateHandler={field.key === 'dob' ? this.showDateHandler : undefined}
      />
    ));
  };

  render() {
    const { form, message, messageType, showAlertModal } = this.state;
    const { updating, fetchingPreferences } = this.props.account;
    return (
      <ScrollView style={{ padding: 20, backgroundColor: colors.background }}>
        <HelperText type="info" style={{ marginBottom: 20 }}>
          {i18n.t('__mandatory_values')}
        </HelperText>
        {this.generateForm(form, this.changeHandler)}

        <Button
          mode="contained"
          style={{ marginBottom: 40 }}
          contentStyle={{ padding: 5 }}
          loading={updating || fetchingPreferences}
          disabled={updating || fetchingPreferences}
          onPress={this.serverRequest}>
          {updating || fetchingPreferences
            ? i18n.t('__saving')
            : i18n.t('__save')}
        </Button>
        <DateDialog
          showDate={this.state.showDate}
          onConfirm={this.dateConfirmHandler}
          onCancel={this.dateCancelHandler}
        />
        <AlertDialog
          onClose={() => {
            this.setState({ showAlertModal: false });
          }}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
      </ScrollView>
    );
  }
}

const mapStateToProps = appState => {
  return {
    account: appState.account,
  };
};

function mapDispatchToProps(dispatch) {
  return {
    saveUserData: data => dispatch(AccountActions.accountUpdateRequest(data)),
    saveUserPreferences: preferences =>
      dispatch(AccountActions.accountPreferencesUpdateRequest(preferences)),
  };
}
export default connect(mapStateToProps, mapDispatchToProps)(EditProfile);
