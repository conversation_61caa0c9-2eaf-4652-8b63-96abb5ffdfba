import React, { Component } from 'react';
import {
  View,
  FlatList,
  ActivityIndicator,
  Text,
  RefreshControl,
} from 'react-native';
import { Caption, Headline } from 'react-native-paper';
import StarRating from 'react-native-star-rating';

import { colors, globalStyles, _ERROR } from '@constants';
import { MainHeader } from '@components';
import FeedbackActions from '@reducers/feedbacks.reducer';
import * as Icon from '@expo/vector-icons';
import { connect } from 'react-redux';
import { ReviewExpandableEntry } from '@components/ReviewExpandableEntry';
import i18n from '@translations/index';
import { getLogin } from '@reducers/account.reducer';
import AlertDialog from '@components/AlertDialog';
import { EVENT_DETAILS_ROUTE } from '@routes/route_constants';
import ReviewRatings from './ReviewRatings';

class ReviewList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAlertModal: false,
      messageType: '',
      message: '',
    };
  }

  componentDidUpdate = (prevProps, prevState, snapshot) => {
    // console.log('Reviews List userid componentDidUpdate ', this.state);
    if (
      this.props.feedbacks.fetchingAllReviews === false &&
      prevProps.feedbacks.fetchingAllReviews === true
    ) {
      if (this.props.feedbacks.error) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__server_error_get_reviews',
        });
      }
    }
  };

  refreshReviews = () => {
    const { userInfoId, loadReviews } = this.props.common;
    if (loadReviews === true) {
      this.props.getReviewsOfUser(userInfoId);
    }
  };

  showRatings = () => {
    const { reviewStats } = this.props?.feedbacks;
    let {
      avgReviewRx = 0,
      reviewRxTotal = 0,
      avgFoodScore = 0,
      avgFunScore = 0,
      avgLocationScore = 0,
      avgHospitalityScore = 0,
    } = reviewStats || {};
    console.log('props reviewStats', reviewStats);

    avgReviewRx /= 100;
    return (
      <View
        style={{
          width: 200,
          // TODO: this should be 10 & 50 up to where it is shown
          marginTop: 10,
          marginBottom: 10,
          alignItems: 'center',
        }}>
        <Headline>{avgReviewRx.toFixed(1)}</Headline>
        <View style={{ width: 200 }}>
          <StarRating
            disabled
            rating={avgReviewRx}
            starSize={30}
            fullStarColor={colors.fullStar}
          />
        </View>
        <Caption>{`${reviewRxTotal} ${i18n.t('__reviews')}`}</Caption>
        <ReviewRatings
          ratings={{
            funScore: avgFunScore,
            foodScore: avgFoodScore,
            locationScore: avgLocationScore,
            hospitalityScore: avgHospitalityScore,
          }}
        />
      </View>
    );
    // }
  };

  componentDidMount() {
    this.focusSubscription = this.props.navigation.addListener(
      'focus',
      this.refreshReviews,
    );
  }
  componentWillUnmount() {
    if (this.focusSubscription.remove !== undefined) {
      this.focusSubscription.remove();
    }
  }

  render() {
    const { userInfoId } = this.props.common;
    const { reviews, fetchingAllReviews } = this.props.feedbacks;
    const { showAlertModal, message, messageType } = this.state;
    return (
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        {/* <NavigationEvents onWillFocus={() => this.refreshReviews()} /> */}
        {fetchingAllReviews === true ? (
          <View style={globalStyles.container}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : reviews.length === 0 ? (
          <View style={globalStyles.container}>
            <Icon.MaterialIcons
              name="rate-review"
              size={80}
              color={colors.black}
            />
            <Text style={{ color: colors.black }}>
              {i18n.t('__no_reviews_yet')}
            </Text>
          </View>
        ) : (
          <View style={{ width: '100%', alignItems: 'center' }}>
            <FlatList
              ListHeaderComponent={
                <View style={{ alignItems: 'center', flex: 1 }}>
                  {this.showRatings()}
                </View>
              }
              refreshControl={
                <RefreshControl
                  refreshing={fetchingAllReviews}
                  onRefresh={() => this.props.getReviewsOfUser(userInfoId)}
                />
              }
              style={{ width: '100%' }}
              data={reviews}
              keyExtractor={(item, index) => index.toString()}
              renderItem={({ item }) => (
                <ReviewExpandableEntry
                  item={item}
                  isReview={true}
                  showDetails={() =>
                    this.props.navigation.navigate(EVENT_DETAILS_ROUTE, {
                      id: item.eventOffer.id,
                      showFab: false,
                    })
                  }
                />
              )}
            />
          </View>
        )}
        <AlertDialog
          onClose={() => {
            this.setState({ showAlertModal: false });
          }}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    feedbacks: appState.feedbacks,
    username: getLogin(appState.account),
    common: appState.common,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    getReviewsOfUser: userId =>
      dispatch(FeedbackActions.reviewsAllRequest(userId)),
  };
}
export default connect(mapStateToProps, mapDispatchToProps)(ReviewList);
