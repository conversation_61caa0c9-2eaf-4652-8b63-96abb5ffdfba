import React, { useEffect, useState } from 'react';
import { Avatar, IconButton, List, Paragraph, Text } from 'react-native-paper';
import { View, StyleSheet } from 'react-native';
import * as Icon from '@expo/vector-icons';
import StarRating from 'react-native-star-rating';
import { USER_INFO_ROUTE, EVENT_DETAILS_ROUTE } from '@routes/route_constants';
import { EVENT_STATE_FEEDBACK_OPEN_AND_MANAGE_DISPUTE } from '@constants/constants';
import i18n from '@translations/index';
import {
  getCancelledChip,
  getUnknownChip,
  getRefundDeniedChip,
  getExpiredChip,
  getWaitingPaymentChip,
  getFinishedChip,
  getPaidChip,
  getGuestCancelledChip,
  getRefundDoneChip,
  getRefundAcceptedChip,
} from '@components/shared';
import { getCachedLogin } from '@constants/functions';
import {
  colors,
  getAvatar,
  EVENT_OFFER_KV_STATE_GUEST_REQUEST,
  EVENT_OFFER_KV_STATE_HOST_CONFIRM,
  EVENT_OFFER_KV_STATE_HOST_CANCEL,
  EVENT_OFFER_KV_STATE_GUEST_CANCEL,
  EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL,
  EVENT_OFFER_KV_STATE_GUEST_PAID,
  EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL,
  getDateTime,
  _SUCCESS,
  _ERROR,
  getIcon,
  EVENT_OFFER_KV_HOST_DENIES_REFUND,
  EVENT_OFFER_KV_HOST_CONFIRMS_REFUND,
  EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT,
  EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT,
  _CONFIRM,
  EVENT_STATE_CLOSE,
  EVENT_STATE_CLOSING,
  EVENT_STATE_OPEN,
  EVENT_STATE_FEEDBACK_CLOSE,
  EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN,
  EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND,
  globalStyles,
  _WARNING,
  EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE,
  EVENT_OFFER_KV_STATE_DELETED,
  EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT,
  _DELETE,
  _CANCEL,
  EVENT_STATE_DELETED,
  EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST,
  EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST_REFUNDED_DONE,
} from '@constants';
import { cancel } from 'redux-saga/effects';

const viewIconButtonComponent = ({ size, color }) => (
  <Icon.MaterialCommunityIcons name="eye" color={color} size={size} />
);

const refundIconButtonComponent = ({ size, color }) => (
  <Icon.MaterialCommunityIcons
    name={getIcon('__refund_verify')}
    size={size}
    color={color}
  />
);
const deleteIconButtonComponent = ({ size, color }) => (
  <Icon.MaterialCommunityIcons name="delete" size={size} color={color} />
);
const cancelIconButtonComponent = ({ size, color }) => (
  <Icon.MaterialCommunityIcons name="cancel" size={size} color={color} />
);
const rateIconButtonComponent = ({ size, color }) => (
  <Icon.MaterialIcons name="rate-review" size={size} color={color} />
);

const LeftComponent = ({ item, push }) => {
  return (
    <View>
      <Avatar.Image
        source={{ uri: getAvatar(item.eokvuserid) }}
        size={50}
        style={{ marginTop: 10, backgroundColor: colors.whiteBackground }}
      />
      <IconButton
        onPress={() =>
          push(EVENT_DETAILS_ROUTE, { id: item.id, userId: item.eokvuserid })
        }
        color={colors.blue}
        size={24}
        icon={viewIconButtonComponent}
      />
    </View>
  );
};

const DescriptionComponent = ({ item }) => (
  <View>
    <Text style={{ color: colors.black }}>{`${i18n.t('__requested_for')} ${
      item.v
    } ${i18n.t('__seats_in')}:`}</Text>
    <Text style={{ fontWeight: 'bold', color: colors.black }}>{item.name}</Text>
    <Paragraph>
      <Icon.Feather name="calendar" />
      {item.change_time
        ? getDateTime(item.change_time) // TODO: remove change_time in future
        : getDateTime(item.last_modified_date)}
    </Paragraph>

    <Paragraph>
      <StarRating
        starSize={15}
        disabled={true}
        maxStars={5}
        rating={item.avg_feedback_rx}
        fullStarColor={colors.fullStar}
      />
    </Paragraph>
  </View>
);

const RightComponent = ({
  item,
  deleteDialog,
  refundDialog,
  showDialog,
  feedback,
}) => {
  const getRefundStateForHost = () => {
    switch (item.refund_state) {
      case EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND:
        /**
         * the refund option is available within the time window X <> X2
         * EVENT_STATE_FEEDBACK_OPEN_AND_MANAGE_DISPUTE
         * EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN
         */
        if (
          item.eventofferstate ===
            EVENT_STATE_FEEDBACK_OPEN_AND_MANAGE_DISPUTE ||
          item.eventofferstate === EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN
        ) {
          return refundBtn;
        }
        return null;

      case EVENT_OFFER_KV_HOST_CONFIRMS_REFUND:
      case EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST:
        return getRefundAcceptedChip();

      case EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST_REFUNDED_DONE:
        return getRefundDoneChip();

      case EVENT_OFFER_KV_HOST_DENIES_REFUND:
        return getRefundDeniedChip();

      default:
        return null;
    }
  };
  const refundBtn = (
    <IconButton
      onPress={() => refundDialog('__refund', item)}
      size={24}
      color={colors.declined}
      icon={refundIconButtonComponent}
    />
  );
  const deleteBtn = (
    <IconButton
      icon={deleteIconButtonComponent}
      size={20}
      color={colors.declined}
      style={styles.rightButton}
      onPress={() => deleteDialog(_DELETE, item, EVENT_OFFER_KV_STATE_DELETED)}
    />
  );

  const cancelBtn = (
    <IconButton
      icon={cancelIconButtonComponent}
      size={20}
      color={colors.declined}
      style={styles.rightButton}
      onPress={() =>
        showDialog(_CANCEL, item, EVENT_OFFER_KV_STATE_HOST_CANCEL)
      }
    />
  );

  const cancelPaymentBtn = (
    <IconButton
      icon={cancelIconButtonComponent}
      size={20}
      color={colors.declined}
      style={styles.rightButton}
      onPress={() =>
        showDialog(_CANCEL, item, EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL)
      }
    />
  );

  const feedbackBtn = (
    <IconButton
      onPress={() => feedback(item)}
      size={24}
      color={colors.declined}
      icon={rateIconButtonComponent}
    />
  );

  const acceptBtn = (
    <IconButton
      icon="check"
      size={20}
      color={colors.success}
      style={styles.rightButton}
      onPress={() => {
        console.log('acceptBtn');
        showDialog('__accept', item, EVENT_OFFER_KV_STATE_HOST_CONFIRM);
      }}
    />
  );

  /** Event state*/
  let refundElement = getRefundStateForHost();
  let eokvElement = <View style={styles.element}>{getUnknownChip()}</View>;
  switch (item.eventofferstate) {
    case EVENT_STATE_CLOSING: //2
    case EVENT_STATE_OPEN: //3
      // console.log('eventofferstate EVENT_STATE_CLOSING EVENT_STATE_OPEN', item.eventofferstate)
      switch (item.state) {
        case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
          eokvElement = (
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-around',
              }}>
              {acceptBtn}
              {cancelBtn}
            </View>
          );
          break;

        case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
          eokvElement = (
            <View style={styles.element}>{getWaitingPaymentChip()}</View>
          );
          break;
        case EVENT_OFFER_KV_STATE_HOST_CANCEL:
        case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
          eokvElement = (
            <View>
              {getCancelledChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;
        case EVENT_OFFER_KV_STATE_GUEST_CANCEL:
        case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
          eokvElement = (
            <View>
              {getGuestCancelledChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;

        case EVENT_OFFER_KV_STATE_GUEST_PAID:
          eokvElement = (
            <View>
              {getPaidChip()}
              <View style={styles.element}>{cancelPaymentBtn}</View>
            </View>
          );
          break;
        default:
          console.log(
            'case not valid ',
            EVENT_STATE_CLOSING,
            EVENT_STATE_OPEN,
            'state',
            item.state,
          );
      }
      break;

    case EVENT_STATE_CLOSE: //1
      // console.log('eventofferstate EVENT_STATE_CLOSE', item.eventofferstate)
      switch (item.state) {
        case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
        case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
          eokvElement = (
            <View>
              {getCancelledChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;

        case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
        case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
          eokvElement = (
            <View>
              {getExpiredChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;
        case EVENT_OFFER_KV_STATE_GUEST_PAID:
          eokvElement = <View style={styles.element}>{getPaidChip()}</View>;
          break;
        default:
          console.log('case not valid ', EVENT_STATE_CLOSE);
      }
      break;

    case EVENT_STATE_FEEDBACK_OPEN_AND_MANAGE_DISPUTE: //5
    case EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN: //4
      // console.log('eventofferstate EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN ', item.eventofferstate)
      switch (item.state) {
        case EVENT_OFFER_KV_STATE_GUEST_PAID:
        case EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT:
          eokvElement = <View style={styles.element}>{feedbackBtn}</View>;
          break;

        case EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT:
        case EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT:
          eokvElement = <View style={styles.element}>{getFinishedChip()}</View>;
          break;

        case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
        case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
          eokvElement = (
            <View>
              {getCancelledChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;

        case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
        case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
          eokvElement = (
            <View>
              {getExpiredChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
        default:
          console.log(
            'case not valid ',
            EVENT_STATE_FEEDBACK_OPEN_AND_MANAGE_DISPUTE,
            EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN,
            'state',
            item.state,
          );
      }
      break;

    case EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE: //6
      // console.log('eventofferstate EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE', item.eventofferstate)
      switch (item.state) {
        case EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT:
        case EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT:
          eokvElement = (
            <View>
              {getFinishedChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;

        case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
        case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
          eokvElement = (
            <View>
              {getCancelledChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;

        case EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT:
        case EVENT_OFFER_KV_STATE_GUEST_PAID:
          eokvElement = feedbackBtn;
          break;

        case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
        case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
          eokvElement = (
            <View>
              {getExpiredChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
        default:
          console.log(
            'case not valid ',
            EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE,
          );
      }
      break;

    case EVENT_STATE_DELETED: //-1
    case EVENT_STATE_FEEDBACK_CLOSE: //7
      // console.log('eventofferstate EVENT_STATE_FEEDBACK_CLOSE', item.eventofferstate)
      switch (item.state) {
        case EVENT_OFFER_KV_STATE_GUEST_PAID:
        case EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT:
        case EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT:
        case EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT:
          eokvElement = (
            <View>
              {getFinishedChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;

        case EVENT_OFFER_KV_STATE_HOST_CANCEL:
        case EVENT_OFFER_KV_STATE_GUEST_CANCEL:
        case EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL:
        case EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL:
          eokvElement = (
            <View>
              {getCancelledChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;

        case EVENT_OFFER_KV_STATE_HOST_CONFIRM:
        case EVENT_OFFER_KV_STATE_GUEST_REQUEST:
          eokvElement = (
            <View>
              {getExpiredChip()}
              <View style={styles.element}>{deleteBtn}</View>
            </View>
          );
          break;
        default:
          console.log(
            'case not valid ',
            EVENT_STATE_DELETED,
            EVENT_STATE_FEEDBACK_CLOSE,
          );
      }
      break;

    default:
      console.log('eventofferstate default', item.eventofferstate);
      eokvElement = getUnknownChip();
  }
  return refundElement ? (
    <View style={globalStyles.flatlistColumnElement}>
      <View style={globalStyles.flatlistElement}>{eokvElement}</View>
      <View style={globalStyles.flatlistElement}>{refundElement}</View>
    </View>
  ) : (
    <View style={globalStyles.flatlistElement}>{eokvElement}</View>
  );
};

export const Offer = ({
  showDialog,
  feedback,
  item,
  navigate,
  refundDialog,
  push,
  deleteDialog,
  selected,
}) => {
  const [userNickname, setUserNickname] = useState(null);

  useEffect(() => {
    const getUserNickName = async () => {
      const login = await getCachedLogin(item.eokvuserid);
      setUserNickname(login);
    };
    getUserNickName();
  }, [item]);

  // console.log('THIS ITEM', item);

  const leftComponent = () => <LeftComponent item={item} push={push} />;
  const descriptionComponent = () => <DescriptionComponent item={item} />;
  const rightComponent = () => (
    <RightComponent
      item={item}
      deleteDialog={deleteDialog}
      refundDialog={refundDialog}
      showDialog={showDialog}
      feedback={feedback}
    />
  );

  return (
    <List.Item
      onPress={() => push(USER_INFO_ROUTE, { userInfoId: item.eokvuserid })}
      style={[
        globalStyles.flatlistEntry,
        selected === item.id
          ? { backgroundColor: colors.gray }
          : { backgroundColor: colors.whiteBackground },
      ]}
      left={leftComponent}
      title={userNickname ? `@${userNickname}` : `${i18n.t('__user_deleted')}`}
      titleStyle={{
        fontFamily: 'medium',
        color: userNickname ? colors.black : '#FF0000',
      }}
      description={descriptionComponent}
      right={rightComponent}
    />
  );
};

const styles = StyleSheet.create({
  menuButton: {
    backgroundColor: '#fafafa',
    borderColor: '#e9e9e9',
    borderWidth: 1,
  },
  element: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textButton: {
    fontSize: 12,
    color: colors.black,
  },
  rightButton: { margin: 0, marginLeft: 10 },
});
