import React, { Component } from 'react';
import { View, ScrollView, ActivityIndicator, Text } from 'react-native';
import { colors, globalStyles, getAvatar, getDateTime } from '@constants';
import AccountActions from '@reducers/account.reducer';
import { Paragraph, List, Checkbox, Avatar, FAB } from 'react-native-paper';
import { connect } from 'react-redux';
import * as Icon from '@expo/vector-icons';
import i18n from '@translations/index';

import AlertDialog from '@components/AlertDialog';
import { getCachedLogin } from '@constants/functions';

class UsersBlocked extends Component {
  constructor(props) {
    super(props);

    const selectedUsers = new Map();
    const userBlocked = this.props.account?.account?.userBlocked ?? [];
    userBlocked.forEach(user => {
      selectedUsers.set(user.login, 'unchecked');
    });

    this.state = {
      showAlertModal: false,
      message: '',
      messageType: '',
      selectedUsers,
      fabVisible: false,
      userBlocked,
      cleanedUsernames: false,
    };
  }

  async addUserNames() {
    return await Promise.all(
      this.props.account?.account?.userBlocked.map(async user => {
        const userNickname = await getCachedLogin(user.login);
        return {
          ...user,
          userNickname,
        };
      }),
    );
  }

  async componentDidUpdate(prevProps) {
    if (
      prevProps.account.usersBlockRequest &&
      !this.props.account.usersBlockRequest
    ) {
      this.setState({
        cleanedUsernames: true,
        userBlocked: await this.addUserNames(),
      });
      // const { error } = this.props.account;
      // if (error) {
      //   this.setState({
      //     showAlertModal: true,
      //     message: error,
      //     messageType: 'error',
      //   });
      // } else {
      //   this.setState({
      //     showAlertModal: true,
      //     message: i18n.t('__users_unblocked'),
      //     messageType: 'success',
      //   });
      // }
    }
  }
  async componentDidMount(prevProps) {
    const { cleanedUsernames } = this.state;
    if (
      !cleanedUsernames &&
      this.props.account?.account?.userBlocked.length > 0
    ) {
      this.setState({
        cleanedUsernames: true,
        userBlocked: await this.addUserNames(),
      });
    }
  }

  setChecked = login => {
    const { selectedUsers } = this.state;
    const currentStatus = selectedUsers.get(login);
    selectedUsers.set(
      login,
      currentStatus === 'checked' ? 'unchecked' : 'checked',
    );

    const fabVisible = Array.from(selectedUsers.values()).some(
      value => value === 'checked',
    );
    this.setState({ selectedUsers, fabVisible });
  };

  callAction = () => {
    const { selectedUsers, userBlocked } = this.state;
    if (userBlocked) {
      let users = JSON.parse(JSON.stringify(userBlocked));

      for (const [key, value] of selectedUsers.entries()) {
        if (value === 'checked') {
          users = users.filter(el => el.login !== key);
        }
      }

      this.props.updateUserBlock(users);
    }
  };

  render() {
    const {
      showAlertModal,
      message,
      messageType,
      selectedUsers,
      fabVisible,
      cleanedUsernames,
      userBlocked,
    } = this.state;
    const { usersBlockRequest } = this.props.account;
    return (
      <View style={{ flex: 1 }}>
        {usersBlockRequest ? (
          <View style={globalStyles.container}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : userBlocked.length === 0 ? (
          <View style={globalStyles.container}>
            <Icon.AntDesign name="deleteuser" size={80} color={colors.black} />
            <Text style={{ color: colors.black }}>
              {i18n.t('__no_blocked_users')}
            </Text>
          </View>
        ) : (
          <ScrollView style={{ padding: 20 }}>
            <View
              style={{
                flexDirection: 'column',
                alignItems: 'center',
                marginBottom: 20,
              }}>
              {cleanedUsernames &&
                userBlocked?.map(user => (
                  <List.Item
                    key={user.login}
                    onPress={() => this.setChecked(user.login)}
                    style={{ ...globalStyles.flatlistEntry, width: '100%' }}
                    left={() => (
                      <View
                        style={{
                          justifyContent: 'center',
                        }}>
                        <Checkbox
                          color={colors.primary}
                          uncheckedColor={colors.gray}
                          status={selectedUsers.get(user.login) ?? 'unchecked'}
                        />
                      </View>
                    )}
                    title={user.userNickname}
                    titleStyle={{ fontWeight: 'bold' }}
                    description={() => (
                      <View>
                        <Paragraph>
                          <Icon.Feather name="calendar" />
                          {getDateTime(user.time)}
                        </Paragraph>
                      </View>
                    )}
                    right={() => (
                      <View>
                        <Avatar.Image
                          source={{ uri: getAvatar(user.login) }}
                          size={50}
                          style={{
                            backgroundColor: colors.gray,
                          }}
                        />
                      </View>
                    )}
                  />
                ))}
            </View>
            <AlertDialog
              onClose={() => {
                this.setState({ showAlertModal: false });
              }}
              alert={{
                visible: showAlertModal,
                message: message,
                messageType: messageType,
              }}
            />
          </ScrollView>
        )}

        <FAB
          visible={fabVisible}
          icon={() => (
            <Icon.AntDesign name="adduser" size={25} color={'white'} />
          )}
          style={globalStyles.fab}
          onPress={() => this.callAction()}
          label={i18n.t('__enable_blocked_users')}
        />
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    account: appState.account,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    updateUserBlock: users => dispatch(AccountActions.usersBlockRequest(users)),
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(UsersBlocked);
