import React, { PureComponent } from 'react';
import { View, Text, ScrollView, Image } from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import { connect } from 'react-redux';
import AccountActions, { isLoggedIn } from '@reducers/account.reducer';
import { colors, icon_size_medium } from '@constants';
class Main extends PureComponent {
  componentDidUpdate(prevProps) {
    if (prevProps.visible === false && this.props.visible === true) {
      this.RBSheet.open();
    }
  }

  render() {
    return (
      <RBSheet
        ref={ref => {
          this.RBSheet = ref;
        }}
        // animationType="slide"
        closeOnDragDown={true}
        closeOnPressMask={true}
        customStyles={{
          wrapper: {
            backgroundColor: 'transparent',
          },
          draggableIcon: {
            backgroundColor: '#000',
          },

          container: {
            backgroundColor: colors.background,
            borderTopRightRadius: 25,
            borderTopLeftRadius: 25,
            borderTopColor: colors.primary,
            borderLeftColor: colors.primary,
            borderRightColor: colors.primary,
            borderTopWidth: 2,
            borderLeftWidth: 0.5,
            borderRightWidth: 0.5,
            overflow: 'hidden',
          },
        }}>
        <ScrollView>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: 5,
              marginRight: 5,
            }}>
            <Image
              source={this.props.icon}
              style={{
                marginBottom: 10,
                height: icon_size_medium,
                width: icon_size_medium,
              }}
            />
            <Text
              style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: colors.black,
              }}>
              {this.props.message}
            </Text>
          </View>
        </ScrollView>
      </RBSheet>
    );
  }
}

// PolicyChecker.propTypes = propTypes;

const mapStateToProps = appState => {
  return {
    account: appState.account,
    common: appState.common,
    loggedIn: isLoggedIn(appState.account),
  };
};
function mapDispatchToProps(dispatch) {
  return {
    addUserPreferences: preferences =>
      dispatch(AccountActions.addAccountPreferencesRequest(preferences)),
  };
}

export const BottomDialog = connect(mapStateToProps, mapDispatchToProps)(Main);
