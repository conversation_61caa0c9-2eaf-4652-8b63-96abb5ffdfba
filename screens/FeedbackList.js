import React, { Component } from 'react';
import {
  View,
  FlatList,
  ActivityIndicator,
  Text,
  RefreshControl,
} from 'react-native';
import { Caption, Headline } from 'react-native-paper';
import StarRating from 'react-native-star-rating';

import { colors, globalStyles, _ERROR } from '@constants';
import FeedbackActions from '@reducers/feedbacks.reducer';
import * as Icon from '@expo/vector-icons';
import { connect } from 'react-redux';
import i18n from '@translations/index';
import { getLogin } from '@reducers/account.reducer';
import AlertDialog from '@components/AlertDialog';
import { EVENT_DETAILS_ROUTE } from '@routes/route_constants';
import { FeedbackEntry } from '@components/FeedbackEntry';

class FeedbackList extends Component {
  constructor(props) {
    super(props);
    this.state = {
      showAlertModal: false,
      messageType: '',
      message: '',
    };
    console.log('FeedbackList constructor ', props);
  }

  componentDidMount() {
    this.focusSubscription = this.props.navigation.addListener(
      'focus',
      this.refreshFeedbacks,
    );
  }
  componentWillUnmount() {
    if (this.focusSubscription.remove !== undefined) {
      this.focusSubscription.remove();
    }
  }

  componentDidUpdate = prevProps => {
    if (
      this.props.feedbacks.fetchingAllFeedbacks === false &&
      prevProps.feedbacks.fetchingAllFeedbacks === true
    ) {
      if (this.props.feedbacks.error) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__server_error_get_feedbacks',
        });
      }
    }
  };

  refreshFeedbacks = () => {
    const { userInfoId, loadFeedbacks } = this.props.common;
    if (loadFeedbacks === true) this.props.getAllFeedbacksOfUser(userInfoId);
  };

  calcTotalRating = feedbacks => {
    if (feedbacks.length > 0) {
      let avgFeedback = 0.0;

      feedbacks.forEach(e => {
        avgFeedback += e.point;
      });
      avgFeedback /= feedbacks.length;
      avgFeedback /= 100;
      return (
        <View
          style={{
            width: 200,
            marginTop: 10,
            marginBottom: 10,
            alignItems: 'center',
          }}>
          <Headline>{avgFeedback.toFixed(1)}</Headline>
          <View style={{ width: 200 }}>
            <StarRating
              disabled
              rating={avgFeedback}
              starSize={30}
              fullStarColor={colors.fullStar}
            />
          </View>
          <Caption>
            ({feedbacks !== undefined ? feedbacks.length : 0}{' '}
            {i18n.t('__reviews')})
          </Caption>
        </View>
      );
    }
  };

  render() {
    const { userInfoId } = this.props.common;
    const { feedbacks, fetchingAllFeedbacks } = this.props.feedbacks;
    const { showAlertModal, message, messageType } = this.state;

    return (
      <View style={{ flex: 1, backgroundColor: colors.background }}>
        {/* <NavigationEvents onWillFocus={() => this.refreshFeedbacks()} /> */}
        {fetchingAllFeedbacks === true ? (
          <View style={globalStyles.container}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : feedbacks.length === 0 ? (
          <View style={globalStyles.container}>
            <Icon.MaterialIcons
              name="feedback"
              color={colors.black}
              size={80}
            />
            <Text style={{ color: colors.black }}>
              {i18n.t('__no_feedbacks_yet')}
            </Text>
          </View>
        ) : (
          <View style={{ flex: 10, width: '100%', alignItems: 'center' }}>
            <FlatList
              ListHeaderComponent={
                <View style={{ alignItems: 'center', flex: 1 }}>
                  {this.calcTotalRating(feedbacks)}
                </View>
              }
              refreshControl={
                <RefreshControl
                  refreshing={fetchingAllFeedbacks}
                  onRefresh={() => this.props.getAllFeedbacksOfUser(userInfoId)}
                />
              }
              style={{ width: '100%' }}
              data={feedbacks}
              keyExtractor={(item, index) => index.toString()}
              renderItem={({ item }) => (
                <FeedbackEntry
                  item={item}
                  isReview={false}
                  showDetails={() =>
                    this.props.navigation.navigate(EVENT_DETAILS_ROUTE, {
                      id: item.eventOffer.id,
                      showFab: false,
                    })
                  }
                />
              )}
            />
          </View>
        )}
        <AlertDialog
          onClose={() => {
            this.setState({ showAlertModal: false });
          }}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    feedbacks: appState.feedbacks,
    username: getLogin(appState.account),
    common: appState.common,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    getAllFeedbacksOfUser: userId =>
      dispatch(FeedbackActions.feedbacksAllRequest(userId)),
  };
}
export default connect(mapStateToProps, mapDispatchToProps)(FeedbackList);
