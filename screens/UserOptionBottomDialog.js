import React, { PureComponent } from 'react';
import { View, ScrollView, Image } from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import { connect } from 'react-redux';
import AccountActions, {
  getLogin,
  isLoggedIn,
} from '@reducers/account.reducer';
import {
  colors,
  globalStyles,
  icon_size,
  icon_size_large,
  images,
  parseMessage,
} from '@constants';
import i18n from '@translations/index';
import EventKVActions from '@reducers/eventKv.reducer';

import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import * as Icon from '@expo/vector-icons';

import { ActivityIndicator, List, Paragraph } from 'react-native-paper';

class Main extends PureComponent {
  actionsInit = [
    {
      text: '__block_user',
      icon: images._block_user,
      f: () => this.blockUser(),
    },
    {
      text: '__report_event',
      icon: images._red_flag,
      children: [
        {
          text: '__report_event_spam',
          icon: images._spam,
          f: args => this.reportEventOffer(args),
        },
        {
          text: '__report_event_abusive',
          icon: images._gte18,
          f: args => this.reportEventOffer(args),
        },
      ],
    },
  ];

  constructor(props) {
    super(props);
    let customActions = [];
    if (props.customActions) {
      customActions = props.customActions;
    }
    this.state = {
      value: null,
      page: 0,
      error: null,
      actionsDefault: [...customActions, ...this.actionsInit],
      actions: [...customActions, ...this.actionsInit],
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.visible === false && this.props.visible === true) {
      this.resetDialog();
      this.RBSheet.open();
    }
    if (prevProps.eventKv.report && !this.props.eventKv.report) {
      if (this.props.eventKv.errorReporting) {
        this.setState({
          page: 1,
          error: this.props.eventKv.errorReporting.title,
        });
      } else {
        this.setState({ page: 1 });
      }
    }
    if (
      prevProps.account.usersBlockRequest &&
      !this.props.account.usersBlockRequest
    ) {
      if (this.props.account.error) {
        this.setState({
          page: 1,
          error: this.props.account.error?.title,
        });
      } else {
        this.setState({ page: 1 });
      }
    }

    if (
      prevProps.externalAction.loading &&
      !this.props.externalAction.loading
    ) {
      if (this.props.externalAction.nestedError) {
        this.setState({
          page: 1,
          error: this.props.externalAction.nestedError,
        });
      } else {
        this.setState({ page: 1, error: null });
      }
    }
  }

  resetDialog = () => {
    const { actionsDefault } = this.state;
    this.setState({ page: 0, actions: actionsDefault });
  };

  callAction = value => {
    if (value?.children) {
      this.setState({ actions: value.children });
      return;
    }

    value.f(value.text);
  };

  blockUser = () => {
    let users = JSON.parse(
      JSON.stringify(this.props.account.account.userBlocked),
    );
    users.push({
      login: this.props.event.userId,
      time: new Date().toISOString(),
    });
    this.props.updateUserBlock(users);
  };

  reportEventOffer = args => {
    this.props.reportEventOffer({
      reportType: args,
      eventId: this.props.event.id,
      reportDetails: '',
    });
  };

  render() {
    const { page, actions, error } = this.state;
    const { userLogin } = this.props.event;
    const { userBlocked } = this.props.account?.account;
    let filteredActions = actions;
    // if (userBlocked) {
    //   let index = userBlocked.findIndex(el => el.login === userLogin);
    //   if (index !== -1) {
    //     filteredActions = [{ text: '__report_event', icon: images._red_flag }];
    //   }
    // }

    return (
      <RBSheet
        ref={ref => {
          this.RBSheet = ref;
        }}
        height={hp('35%')}
        // animationType="slide"
        closeOnDragDown={true}
        closeOnPressMask={true}
        customStyles={{
          wrapper: {
            backgroundColor: 'transparent',
          },
          draggableIcon: {
            backgroundColor: '#000',
          },

          container: {
            backgroundColor: colors.background,
            borderTopRightRadius: 25,
            borderTopLeftRadius: 25,
            borderTopColor: colors.primary,
            borderLeftColor: colors.primary,
            borderRightColor: colors.primary,
            borderTopWidth: 2,
            borderLeftWidth: 0.5,
            borderRightWidth: 0.5,
            overflow: 'hidden',
          },
        }}>
        <ScrollView>
          <View
            style={{
              flex: 1,
              alignItems: 'center',
              justifyContent: 'center',
              marginLeft: 5,
              marginRight: 5,
            }}>
            <List.Section
              style={{
                width: '80%',
              }}
              titleStyle={{ fontWeight: 'bold', fontSize: 24 }}
              title={i18n.t('__actions')}>
              {page === 0 ? (
                filteredActions.length > 0 ? (
                  filteredActions.map(action => (
                    <List.Item
                      style={{ flex: 1, alignItems: 'center' }}
                      key={action.text}
                      titleStyle={{}}
                      title={i18n.t(action.text)}
                      left={props => (
                        <Image
                          style={{ height: icon_size, width: icon_size }}
                          source={action.icon}
                        />
                      )}
                      onPress={e => {
                        console.log('what action', action);
                        this.callAction(action);
                      }}
                    />
                  ))
                ) : (
                  <View style={globalStyles.centerContainer}>
                    <Icon.MaterialCommunityIcons size={80} name="snowman" />
                    <Paragraph>{parseMessage('__no_actions')}</Paragraph>
                  </View>
                )
              ) : (
                <>
                  {this.props.account.usersBlockRequest ? (
                    <View style={globalStyles.centerContainer}>
                      <ActivityIndicator size="large" color={colors.primary} />
                    </View>
                  ) : (
                    <View style={globalStyles.centerContainer}>
                      <Image
                        source={error ? images._warning : images._success}
                        style={{
                          height: icon_size_large,
                          width: icon_size_large,
                        }}
                      />
                      <Paragraph>
                        {error
                          ? parseMessage(
                              error === '__idexists'
                                ? '__report_error_idexists'
                                : '__error',
                            )
                          : parseMessage('__success')}
                      </Paragraph>
                    </View>
                  )}
                </>
              )}
            </List.Section>
          </View>
        </ScrollView>
      </RBSheet>
    );
  }
}

const mapStateToProps = appState => {
  return {
    eventKv: appState.eventkv,
    account: appState.account,
    loggedIn: isLoggedIn(appState.account),
    login: getLogin(appState.account),
  };
};
function mapDispatchToProps(dispatch) {
  return {
    updateUserBlock: users => dispatch(AccountActions.usersBlockRequest(users)),

    reportEventOffer: eventKv =>
      dispatch(EventKVActions.eventKvReportRequest(eventKv)),
  };
}

export const UserOptionBottomDialog = connect(
  mapStateToProps,
  mapDispatchToProps,
)(Main);
