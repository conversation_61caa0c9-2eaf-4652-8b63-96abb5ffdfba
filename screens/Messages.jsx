import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  View,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { List, Badge, Avatar, Caption, Text } from 'react-native-paper';
import {
  colors,
  defaultDateTimeFormatShort,
  getAvatar,
  globalStyles,
  MESSAGE_STATE_RX_READ,
  _ERROR,
} from '@constants';
import { getLogin } from '@reducers/account.reducer';
import * as Icon from '@expo/vector-icons';
import i18n from '@translations/index';
import MessageActions from '@reducers/message.reducer';
import { CHAT_ROUTE } from '@routes/route_constants';
import { EventRegister } from 'react-native-event-listeners';
import moment from 'moment';
import { useNavigation } from '@react-navigation/native';
import { getCachedLogin } from '@constants/functions';

// Define a separate component for the right section of the List.Item
const RightSection = ({ item, username }) => (
  <View style={styles.rightSection}>
    <Caption>{moment(item.dateTx).format(defaultDateTimeFormatShort)}</Caption>
    {(item.badge ||
      (item.stateRx < MESSAGE_STATE_RX_READ && item.userRx === username)) && (
      <Badge>*</Badge>
    )}
  </View>
);

const MessageAvatar = ({ username, item }) => (
  <Avatar.Image
    source={{
      uri: getAvatar(username !== item.userTx ? item.userTx : item.userRx),
    }}
    size={40}
    style={{
      marginTop: 10,
      backgroundColor: colors.whiteBackground,
    }}
  />
);

// CSS styles
const styles = {
  rightSection: {
    justifyContent: 'space-between',
    paddingVertical: 5,
  },
};

const Messages = props => {
  const [state, setState] = useState({
    showModal: false,
    modalText: 'accept',
    objectVal: '',
    bodyVal: '',
    userRx: '',
    submitted: false,
    createRequest: false,
    createRequestError: false,
    createRequestMessage: '',
    showResultModal: false,
    alertMessage: '',
    alertMessageType: '',
    sanitizedMessages: [],
    processingSanitizedMessages: false,
  });
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const messages = useSelector(nextState => nextState.messages);
  const username = useSelector(nextState => getLogin(nextState.account));

  const appendMessageMessages = useCallback(
    message => dispatch(MessageActions.appendMessageMessages(message)),
    [dispatch],
  );
  const getMessages = useCallback(
    () => dispatch(MessageActions.messageAllRequest()),
    [dispatch],
  );

  useEffect(() => {
    const listener = EventRegister.addEventListener('messageReceived', data => {
      appendMessageMessages(data);
    });
    const focusSubscription = navigation.addListener('focus', getMessages);

    return () => {
      EventRegister.removeEventListener(listener);
      focusSubscription.remove();
    };
  }, [getMessages, appendMessageMessages, navigation]);

  const getCachedLoginCallback = useCallback(
    async user => getCachedLogin(user),
    [],
  );

  useEffect(() => {
    const fetchCleanedMessages = async () => {
      setState(prevState => ({
        ...prevState,
        processingSanitizedMessages: true,
      }));
      const cleanedMessagesPromises = messages.messages.map(async message => {
        const userTxNickname = await getCachedLoginCallback(message.userTx);
        const userRxNickname = await getCachedLoginCallback(message.userRx);
        return {
          ...message,
          body: {
            text: message.body.text
              .replace(message.userTx, userTxNickname)
              .replace(message.userRx, userRxNickname),
          },
          nicknameUserTx: userTxNickname,
          nicknameUserRx: userRxNickname,
        };
      });

      const cleanedMessages = await Promise.all(cleanedMessagesPromises);
      setState(prevState => ({
        ...prevState,
        sanitizedMessages: cleanedMessages,
        processingSanitizedMessages: false,
      }));
    };
    fetchCleanedMessages();

    if (!messages.fetchingAll) {
      if (messages.errorAll) {
        setState(prevState => ({
          ...prevState,
          showResultModal: true,
          alertMessage: messages.errorAll,
          alertMessageType: _ERROR,
        }));
      }
    }

    if (!messages.updating) {
      if (messages.errorUpdating) {
        setState(prevState => ({
          ...prevState,
          showResultModal: true,
          alertMessage: messages.errorUpdating,
          alertMessageType: _ERROR,
        }));
      }
    }
  }, [messages, getCachedLoginCallback]);

  useEffect(() => {
    if (
      messages.createLoading !== undefined &&
      messages.createLoading !== state.createRequest
    ) {
      let finalState = {
        createRequest: messages.createLoading,
      };
      if (
        state.createRequest &&
        messages.createLoading === false &&
        messages.createResponse
      ) {
        if (
          messages.createResponse.status &&
          messages.createResponse.status !== 200
        ) {
          finalState.createRequestError = true;
          finalState.createRequestMessage =
            messages.createResponse.status === 400
              ? i18n.t('__message_user_not_found')
              : i18n.t('__message_error');
        } else {
          finalState.createRequestError = false;
          finalState.createRequestMessage = i18n.t('__message_success');
          finalState.showModal = false;
          finalState.submitted = false;
          finalState.objVal = false;
          finalState.bodyVal = false;
          finalState.userRx = false;
          getMessages();
        }
      }
      setState(prevState => ({ ...prevState, ...finalState }));
    }
  }, [
    getMessages,
    messages.createLoading,
    messages.createResponse,
    state.createRequest,
  ]);

  return (
    <View
      style={{
        flex: 1,
        backgroundColor: colors.background,
      }}>
      <View>
        {messages.fetchingAll === true ||
        state.processingSanitizedMessages === true ? (
          <View style={globalStyles.container}>
            <ActivityIndicator size="large" color={colors.primary} />
          </View>
        ) : state.sanitizedMessages.length === 0 ? (
          <View style={globalStyles.container}>
            <Icon.MaterialCommunityIcons
              name="message-text-outline"
              size={80}
              color={colors.black}
            />
            <Text style={{ color: colors.black }}>
              {i18n.t('__no_messages')}
            </Text>
          </View>
        ) : (
          <FlatList
            style={{ padding: 10 }}
            refreshControl={
              <RefreshControl
                refreshing={messages.fetchingAll}
                onRefresh={getMessages}
              />
            }
            data={state.sanitizedMessages}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => {
              return item ? (
                <List.Item
                  onPress={() =>
                    navigation.navigate(CHAT_ROUTE, {
                      id: 'chat1',
                      item: item,
                      login: username,
                    })
                  }
                  left={() => <MessageAvatar username={username} item={item} />}
                  titleStyle={{ fontFamily: 'medium' }}
                  title={
                    username !== item.userTx
                      ? item.nicknameUserTx
                      : item.nicknameUserRx
                  }
                  description={item.body?.text}
                  right={() => <RightSection item={item} username={username} />}
                />
              ) : null;
            }}
          />
        )}
      </View>
    </View>
  );
};

export default Messages;
