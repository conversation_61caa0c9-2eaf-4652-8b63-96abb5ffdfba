import React, { Component } from 'react';
import { ScrollView, View, Share } from 'react-native';
import {
  HelperText,
  Button,
  Portal,
  Dialog,
  Paragraph,
  TextInput,
  List,
} from 'react-native-paper';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import { connect } from 'react-redux';
import {
  colors,
  _ERROR,
  _SUCCESS,
  EVENT_OFFER_TYPE_OFFER_PUBLIC,
  getShareableObject,
  getIcon,
  createEventFormSchema,
  createEventFormLocationSchema,
  createEventFormSchemaConstraints,
  EVENT_OFFER_TYPE_OFFER_PRIVATE,
} from '@constants';
import { Input } from '@components';
import * as utilityAction from '@actions/utility.action';
import { appConfig } from '@config/app-config';
import validate from '@helpers/validation';
import AlertDialog from '@components/AlertDialog';
import RadioForm from 'react-native-simple-radio-button';
import i18n from '@translations/index';
import EventsActions from '@reducers/events.reducer';
import { DRAWER_MY_EVENTS_ROUTE } from '@routes/route_constants';
import * as Location from 'expo-location';
import { getLocationPermission } from '@constants/functions';
import * as Icon from '@expo/vector-icons';

const multiselect = ['typeKs', 'typeBs', 'typeEs', 'typeLs', 'intolerances'];

class CreateEvent extends Component {
  constructor(props) {
    super(props);
    const { event } = this.props.route?.params || {};
    const form = createEventFormSchema;
    form.pics.pickImageHandlerError = e =>
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: i18n.t(e),
      });
    const formLocation = createEventFormLocationSchema;
    console.log('constructor state.params', this.props.route.params);
    if (event) {
      this.props.getEventEdit(event.id);
    } else {
      Object.keys(form).forEach(key => {
        form[key].value = form[key].default;
      });

      Object.keys(formLocation).forEach(key => {
        if (typeof formLocation[key] !== 'number') {
          formLocation[key].value = '';
        }
      });
    }

    const addressSelected = event && event.id ? true : false;
    // console.log('status', event && event.id);
    this.state = {
      eventId: event && event.id,
      requestNominatum: false,
      addressEvaluated: false,
      showAlertModal: false,
      warningText: null,
      checkOffer: event && event.type,
      showDate: false,
      showAddress: false,
      confirmAddressText: i18n.t('__search'),
      content: '',
      enableConfirmAddress: true,
      searchAddress: '',
      searchResult: [],
      form,
      formLocation,
      value: 0,
      navigateToProfile: false,
      addressSelected: addressSelected,
      generalRequest: false,
      currentLocation: null,
      loadingAddress: false,
    };
  }

  loadEventForm(event) {
    const { form, formLocation, eventId } = this.state;
    let nextEventId = eventId;
    Object.keys(form).forEach(key => {
      // console.log('1 key', key, event[key]);
      if (key === 'pics' && event[key]) {
        // console.log('load pic key', key, event[key], Object.values(event[key]));
        form[key].value = Object.values(event[key]);
        // console.log('loadEventForm pics is', form[key].value);
      } else if (key === 'type') {
        form[key].value = event[key] === EVENT_OFFER_TYPE_OFFER_PRIVATE;
      } else if (multiselect.includes(key)) {
        if (
          key === 'typeKs' ||
          key === 'typeEs' ||
          key === 'typeBs' ||
          key === 'typeLs' ||
          key === 'intolerances'
        ) {
          //inner array
          // console.log('2a loadEventForm key', key);
          // console.log('2b loadEventForm [event[key]]', event[key]);
          // form[key].value = event[key].length > 0 ? event[key][0] : []
          form[key].value = event[key];
        } else {
          form[key].value = [event[key]];
        }
      } else {
        form[key].value = event[key];
      }
    });
    //Special properties managed as object

    // console.log(' loadEventForm form is ', form);
    Object.keys(formLocation)
      .filter(key => key !== 'lat' && key !== 'lon')
      .forEach(key => {
        console.log('>>>>', formLocation, key, event[key]);
        formLocation[key].value = event[key];
      });
    form.date.value = form.date ? new Date(form.date.value) : form.date.value;
    formLocation.lat = event.lat;
    formLocation.lon = event.lon;

    if (this.props.route.params && this.props.route.params.clone) {
      nextEventId = false;
    }

    this.setState({ eventId: nextEventId, form, formLocation });
    // console.log('loaded event', form);
  }

  generateForm = (structure, change, state) => {
    const { updating } = this.props.events;
    const fields = Object.keys(structure).map(key => ({
      ...structure[key],
      key,
    }));

    return fields
      .filter(e => e.label !== undefined)
      .map(field =>
        field.picker || field.date || field.uiStepper ? (
          <View key={field.label} style={{ flexDirection: 'row' }}>
            <View style={{ flex: 0.2 }}>
              <List.Icon
                style={{ alignContent: 'stretch' }}
                icon={getIcon(field.label)}
                color={colors.primary}
              />
            </View>
            <View style={{ flex: 0.8 }}>
              <Input
                disabled={updating}
                {...field}
                _key={field.key}
                format={field.format}
                change={change}
                showDateHandler={this.showDateHandler}
              />
            </View>
          </View>
        ) : (
          <Input
            disabled={updating}
            {...field}
            _key={field.key}
            format={field.format}
            change={change}
            showDateHandler={this.showDateHandler}
          />
        ),
      );
  };

  changeHandler = (formKey, text, field) => {
    const form = this.state[formKey];

    form[field].value = text;
    let obj = {};
    obj[formKey] = form;
    this.setState(obj);
  };

  dateCancelHandler = () => {
    this.setState({ showDate: false });
  };

  showDateHandler = () => {
    this.setState({ showDate: true });
  };

  dateConfirmHandler = e => {
    const { form } = this.state;
    form.date.value = e;
    this.setState({
      showDate: false,
      form,
    });
  };

  useLocalAddress = async () => {
    const permissions = await getLocationPermission();
    this.setState({ loadingAddress: true });
    if (permissions) {
      const { coords } = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = coords;
      const response = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      const formLocation = { ...this.state.formLocation };
      if (response.length === 0) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: i18n.t('__user_not_position'),
        });
        return;
      }

      formLocation.city.value = response[0].city;
      formLocation.postalcode.value = response[0].postalCode;
      formLocation.street.value = `${response[0].street} ${response[0]?.streetNumber}`;
      formLocation.country.value = response[0].country;
      formLocation.lat = latitude;
      formLocation.lon = longitude;

      this.setState({
        formLocation,
        showAddress: false,
        addressSelected: true,
        loadingAddress: false,
      });
    } else {
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: i18n.t('__user_location_permission_denied'),
        loadingAddress: false,
      });
    }
  };

  getNominatum = async address => {
    const { searchAddress } = this.state;

    console.log('getNominatum ', address);
    console.log('searchAddress ', searchAddress);
    if (address === null) {
      address = searchAddress;
    }
    const formattedString = address.split(' ').join('+');

    this.setState({ requestNominatum: true, addressEvaluated: true });
    const url =
      appConfig.nomunatumUrl + formattedString + '&format=json&limit=10';

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      });
      const r = await response.json();
      console.log('result to nominatum', r);
      this.setState({
        confirmAddressText: i18n.t('__select'),
        searchResult: r,
      });
    } catch (e) {
      console.log(e);
    }

    this.setState({ requestNominatum: false });
  };

  hideAddressHelper = () => {
    this.setState({ showAddress: false });
  };
  selectAddress = () => {
    if (
      this.state.value === undefined ||
      !this.state.searchResult[this.state.value]
    ) {
      return;
    }

    const item = this.state.searchResult[this.state.value];
    console.log('selected item', item);
    const formLocation = { ...this.state.formLocation };

    formLocation.city.value =
      item.address.city ||
      item.address.town ||
      item.address.county ||
      item.address.state_district ||
      item.address.state ||
      '';

    if (item.address.construction) {
      formLocation.street.value = item.address.construction || '';
    }
    if (item.address.cycleway) {
      formLocation.street.value = item.address.cycleway;
    }
    if (item.address.street) {
      formLocation.street.value = item.address.street;
    }
    if (item.address.address29) {
      formLocation.street.value = item.address.address29;
    }
    if (item.address.address26) {
      formLocation.street.value = item.address.address26;
    }
    if (item.address.pedestrian) {
      formLocation.street.value = item.address.pedestrian;
    }
    if (item.address.road) {
      formLocation.street.value = item.address.road;
    }
    if (item.address.locality) {
      formLocation.street.value = item.address.locality;
    }
    if (item.address.residential) {
      formLocation.street.value = item.address.residential;
    }

    if (item.address.house_number) {
      formLocation.street.value += ' ' + item.address.house_number;
    }

    formLocation.postalcode.value = item.address.postcode;
    formLocation.country.value = item.address.country;
    formLocation.lat = item.lat;
    formLocation.lon = item.lon;

    this.setState({ formLocation, showAddress: false, addressSelected: true });
  };

  processForm = () => {
    // const typeK = Array.isArray(this.state.form.typeK.value)
    //   ? this.state.form.typeK.value.map(x => ({ name: x }))
    //   : [];
    // const intolerances = Array.isArray(this.state.form.intolerances.value)
    //   ? this.state.form.intolerances.value.map(x => ({ name: x }))
    //   : [];
    const properties = {};
    const obj = {
      maxPartecipants: this.state.form.maxPartecipants.value,
      typeKs: this.state.form.typeKs.value,
      typeBs: this.state.form.typeBs.value,
      typeEs: this.state.form.typeEs.value,
      intolerances: this.state.form.intolerances.value,
      typeLs: this.state.form.typeLs.value,
      date:
        this.state.form.date.value !== ''
          ? new Date(this.state.form.date.value).toISOString()
          : '',
      pics: this.state.form.pics.value,
      presentation: this.state.form.presentation.value,
      pricepp: this.state.form.pricepp.value,
      name: this.state.form.name.value,
      duration: this.state.form.duration.value,
      type:
        this.state.form.type.value === true
          ? EVENT_OFFER_TYPE_OFFER_PRIVATE
          : EVENT_OFFER_TYPE_OFFER_PUBLIC,
      street: this.state.formLocation.street.value,
      city: this.state.formLocation.city.value,
      country: this.state.formLocation.country.value,
      postalcode: this.state.formLocation.postalcode.value,
      lon: this.state.formLocation.lon,
      lat: this.state.formLocation.lat,
      properties,
    };

    if (this.props.route.params.event && !this.props.route.params?.clone) {
      obj.id = this.props.route.params.event.id;
    }

    //to debug
    const errors = Object.values(
      validate(obj, createEventFormSchemaConstraints) || {},
    );
    if (errors.length > 0) {
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: errors[0][0],
      });
      return;
    }
    return obj;
  };

  serverRequest = async () => {
    const event = this.processForm();
    console.log('serverRequest 2 event', event);
    if (!event) {
      return;
    }
    try {
      this.setState({ generalRequest: true, content: 'images' });
      let isNsfw = false;

      if (event.pics !== undefined && Array.isArray(event.pics)) {
        const returnedImages = await Promise.all(
          event.pics.map(e =>
            e.includes(appConfig.baseAssetUrl)
              ? { payload: { fileDownloadUri: e } }
              : this.props.uploadEventFile(e),
          ),
        );

        const uploadChecker = data =>
          data.payload.status && data.payload.status !== 200;
        if (returnedImages.some(uploadChecker)) {
          throw 'error';
        }

        const picsObject = {};
        returnedImages
          .map(p => p.payload)
          .forEach((e, i) => {
            picsObject['pic_' + i] = e.fileDownloadUri;
            if (e.properties?.race === true || e.properties?.adult === true) {
              isNsfw = true;
            }
          });
        event.pics = picsObject;
      } else if (typeof event.pics !== 'string') {
        let action = await this.props.uploadEventFile(event.pics);

        console.log('serverRequest 4 action', action);

        if (!action.payload.ok) {
          throw action.payload;
        }
        event.pics = action.payload.data.fileDownloadUri;
        isNsfw =
          action.payload.data.properties?.race === true ||
          action.payload.data.properties?.adult === true;
      }
      console.log('serverRequest 5', event);
      this.setState({ content: 'event' });
      event.properties.nsfw = isNsfw;

      this.props.updateUserEvent(event);
    } catch (e) {
      console.log('serverRequest catch e', e);
      this.setState({
        generalRequest: false,
        showAlertModal: true,
        messageType: _ERROR,
        content: '',
        message: '__server_error_save_image',
      });
    }
  };

  componentDidUpdate(prevProps) {
    //retrieve the event data in editing mode
    if (prevProps.events.fetchingEdit && !this.props.events.fetchingEdit) {
      if (this.props.events.errorEdit) {
        this.setState({
          message: '__error_server',
          messageType: _ERROR,
          showAlertModal: true,
          navigateToProfile: false,
          generalRequest: false,
        });
      } else {
        this.loadEventForm(this.props.events.eventEdit);
      }
    }

    if (prevProps.events.updating && !this.props.events.updating) {
      if (this.props.events.errorUpdating) {
        const errorKey =
          '__error_' + this.props.events.errorUpdating.title.replace('__', '');
        this.setState({
          message: errorKey ?? '__error_server',
          messageType: _ERROR,
          showAlertModal: true,
          navigateToProfile: false,
          generalRequest: false,
        });
      } else {
        this.setState({
          message: '__event_save_ok',
          messageType: _SUCCESS,
          showAlertModal: true,
          navigateToProfile: true,
          generalRequest: false,
        });
      }
    }
  }

  shareHandle = async () => {
    try {
      const { event } = this.props.events;
      console.log('shareHandle ', event);
      const result = await Share.share(getShareableObject(event));
      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
          console.log('shareHandle result with activity', result);
        } else {
          // shared
          console.log('shareHandle result', result);
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
        console.log('shareHandle result dismissedAction', result);
      }
    } catch (error) {
      console.log(error.message);
    }
  };

  render() {
    const {
      showDate,
      form,
      showAlertModal,
      message,
      messageType,
      formLocation,
      confirmAddressText,
      searchAddress,
      searchResult,
      addressSelected,
      generalRequest,
      content,
      loadingAddress,
    } = this.state;

    const { updating } = this.props.events;

    let curDate = new Date();
    let prevDate = new Date();
    prevDate.setDate(curDate.getDate() - 1);

    return (
      <View style={{ flex: 1 }}>
        <ScrollView
          key="myEventScrollView"
          style={{ padding: 20, backgroundColor: colors.background }}>
          <HelperText type="info" style={{ marginBottom: 20 }}>
            {i18n.t('__mandatory_values')}
          </HelperText>
          {this.generateForm(form, (text, field) => {
            // console.log('generateForm text ', text, field);
            return this.changeHandler('form', text, field);
          })}

          <View style={{ flexDirection: 'column', flex: 1, marginTop: 30 }}>
            <Button
              mode="outlined"
              icon={({ size, color }) => (
                <Icon.MaterialIcons
                  name="gps-fixed"
                  color={color}
                  size={size}
                />
              )}
              style={{ marginBottom: 10 }}
              contentStyle={{ padding: 5 }}
              onPress={this.useLocalAddress}
              loading={loadingAddress}
              disabled={updating}>
              {i18n.t('__use_local_address')}
            </Button>

            <Button
              mode="outlined"
              icon={({ size, color }) => (
                <Icon.MaterialCommunityIcons
                  name="home-search-outline"
                  color={color}
                  size={size}
                />
              )}
              style={{ marginBottom: 30 }}
              contentStyle={{ padding: 5 }}
              onPress={() => this.setState({ showAddress: true })}
              disabled={updating}>
              {i18n.t('__pick_address')}
            </Button>
          </View>
          {addressSelected === true ? (
            this.generateForm(formLocation, (text, field) => {
              return this.changeHandler('formLocation', text, field);
            })
          ) : (
            <View />
          )}

          <Button
            mode="contained"
            style={{ marginBottom: 40 }}
            contentStyle={{ padding: 5 }}
            loading={generalRequest || updating}
            disabled={generalRequest}
            onPress={this.serverRequest}>
            {updating ? `${i18n.t('__saving')} ${content}` : i18n.t('__save')}
          </Button>

          <DateTimePickerModal
            mode="datetime"
            style={{ tintColor: colors.primary }}
            date={curDate}
            minuteInterval={15}
            isVisible={showDate}
            minimumDate={new Date()}
            onConfirm={this.dateConfirmHandler}
            onCancel={this.dateCancelHandler}
          />

          <Portal>
            <Dialog
              visible={this.state.showAddress}
              onDismiss={this.hideAddressHelper}>
              <Dialog.Title>{i18n.t('__your_address')}</Dialog.Title>
              <Dialog.Content>
                <Paragraph>{i18n.t('__your_address_tip')}</Paragraph>
                <TextInput
                  value={searchAddress}
                  multiline={false}
                  onChangeText={address =>
                    this.setState({
                      searchAddress: address,
                      addressEvaluated: false,
                      enableConfirmAddress: address.length < 6,
                    })
                  }
                  onSubmitEditing={event =>
                    this.getNominatum(event.nativeEvent.text)
                  }
                  style={{ marginBottom: 20, padding: 0 }}
                />
                <View>
                  <ScrollView
                    style={{ flexGrow: 0, padding: 0, maxHeight: 350 }}>
                    <RadioForm
                      ref="radioForm"
                      radio_props={(searchResult || []).map((item, index) => {
                        return {
                          label: item.display_name,
                          value: index,
                        };
                      })}
                      initial={this.state.value}
                      labelHorizontal={true}
                      formHorizontal={false}
                      buttonColor={'#474747'}
                      selectedButtonColor={'#f36874'}
                      selectedLabelColor={'#f36874'}
                      buttonInnerColor={'red'}
                      buttonOuterColor={'red'}
                      labelColor={'#474747'}
                      labelStyle={{ marginRight: 15 }}
                      buttonSize={15}
                      animation={false}
                      onPress={value => {
                        this.setState({ value, addressSelected: true });
                      }}
                    />
                  </ScrollView>
                </View>
              </Dialog.Content>
              <Dialog.Actions>
                <Button onPress={this.hideAddressHelper}>
                  {i18n.t('__cancel')}
                </Button>
                <Button
                  mode="contained"
                  disabled={this.state.enableConfirmAddress}
                  loading={this.state.requestNominatum}
                  onPress={
                    this.state.addressEvaluated
                      ? () => this.selectAddress()
                      : () => this.getNominatum(null)
                  }>
                  {confirmAddressText}
                </Button>
              </Dialog.Actions>
            </Dialog>
          </Portal>
          <AlertDialog
            toShare={messageType === _SUCCESS ? this.shareHandle : undefined}
            onClose={() => {
              if (this.state.navigateToProfile) {
                this.props.navigation.navigate(DRAWER_MY_EVENTS_ROUTE);
              }
              this.setState({
                showAlertModal: false,
                navigateToProfile: false,
              });
            }}
            alert={{
              visible: showAlertModal,
              message: message,
              messageType: messageType,
            }}
          />
        </ScrollView>
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    events: appState.events,
    account: appState.account,
    authorization: appState.authorization,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    updateUserEvent: data => dispatch(EventsActions.eventUpdateRequest(data)),
    uploadEventFile: file => dispatch(utilityAction.uploadEventFile(file)),
    getEventEdit: eventId => dispatch(EventsActions.eventEditRequest(eventId)),
  };
}

export default connect(mapStateToProps, mapDispatchToProps)(CreateEvent);
