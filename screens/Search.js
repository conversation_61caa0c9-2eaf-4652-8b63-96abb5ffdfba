import React, { Component } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  ScrollView,
  Image,
  Animated,
  RefreshControl,
} from 'react-native';
import {
  Searchbar,
  Button,
  Surface,
  Portal,
  FAB,
  Text,
  IconButton,
  Modal,
  List,
  Subheading,
  Chip,
} from 'react-native-paper';
import * as Icon from '@expo/vector-icons';
import CalendarPicker from 'react-native-calendar-picker';
import SIRadioButton from '@components/RadioButton';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import DefaultMarker from '@ptomasroos/react-native-multi-slider/DefaultMarker';
import Constants from 'expo-constants';
import UIStepper from 'react-native-ui-stepper';
import { connect } from 'react-redux';
import { Input } from '@components';
import {
  colors,
  typeEvents,
  typeKitchen,
  typeBeverage,
  locations,
  MIN_PRICE,
  MAX_PRICE,
  MAX_DURATION_MIN,
  getIcon,
  globalStyles,
  defaultDateFormat,
  intollerances,
  images,
  getLocationPermission,
  calculateFlyDistance,
  INPUT_TYPE_SWITCHBISTATE,
} from '@constants';
import i18n from '@translations/index';
import SearchActions from '@reducers/search.reducer';
import { PolicyChecker } from './PolicyChecker';
import AccountActions, { isLoggedIn } from '@reducers/account.reducer';
import { MAP_ROUTE } from '@routes/route_constants';
import { PacmanIndicator } from 'react-native-indicators';
import AlertDialog from '@components/AlertDialog';
import * as Location from 'expo-location';
import EventCard from './component/EventCard';
import {
  INF_SYMBOL,
  INPUT_TYPE_SLIDER,
  MAX_DISTANCE_FILTER,
  MIN_DISTANCE_FILTER,
} from '@constants/constants';
import { getDistanceUnit } from '@constants/functions';

const initialFilters = {
  name: '',
  location: [],
  kitchen: [],
  drink: [],
  adults: 0,
  eventTypes: [],
  price: [MIN_PRICE, MAX_PRICE],
  duration: [0, MAX_DURATION_MIN],
  isFilterAdded: false,
  properties: [
    { label: '__nsfw', name: 'nsfw', value: false, icon: images._gte18 },
  ],
  distanceFilter: [MAX_DISTANCE_FILTER + 1], // 0 - 500 : inf
  selectedStartDate: null,
  selectedEndDate: null,
  intolerances: [],
};

class SearchScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      visible: false,
      searchOptions: [
        { label: i18n.t('__dates'), key: 'Dates' },
        { label: i18n.t('__guests'), key: 'guests' },
        { label: i18n.t('__City'), key: 'city' },
        { label: i18n.t('__kitchen'), key: 'kitchen' },
        { label: i18n.t('__drink'), key: 'drink' },
      ],
      ...initialFilters,
      filtersApplied: [],
      apply: false,
      clearSearch: false,
      showPolicyChecker: true,
      currentQuery: null,
      page: 0,
      showAlertModal: false,
      message: '',
      messageType: '',
      currentLocation: null,
      // eventsWithDistance: null,
    };
  }

  packUserBlockedInQuery(query = null) {
    const userBlocked = this.props.account?.account?.userBlocked || [];
    console.log('userBlocked >>', userBlocked);
    console.log('query >>', query);
    if (!userBlocked) {
      return;
    }
    if (query) {
      query.users_blocked = userBlocked?.map(user => user.login);
      return query;
    }
    return {
      users_blocked: userBlocked?.map(user => user.login),
    };
  }

  composeFilterQuery = (inputFilters, query = null, page = 0) => {
    const initialCleanedFilters = {
      ...inputFilters,
      selectedStartDate: inputFilters.selectedStartDate?.toISOString() || null,
      selectedEndDate: inputFilters.selectedEndDate?.toISOString() || null,
    };
    const userBlocked = this.props.account?.account?.userBlocked || [];
    if (query === null) {
      return {
        filters: {
          users_blocked: userBlocked?.map(user => user.login),
        },
        initialFilters: initialCleanedFilters,
        page,
      };
    }

    return {
      filters: {
        ...query,
        selectedStartDate: query?.selectedStartDate?.toISOString() || null,
        selectedEndDate: query.selectedEndDate?.toISOString() || null,
        users_blocked: userBlocked?.map(user => user.login),
      },
      initialFilters: initialCleanedFilters,
      page,
    };
  };

  onSearchInputChange = (page = 0) => {
    this.props.searchQueryString(
      this.composeFilterQuery(initialFilters, this.state.currentQuery, page),
    );
  };

  async componentDidMount() {
    if (await getLocationPermission()) {
      const { coords } = await Location.getCurrentPositionAsync();
      this.setState({ currentLocation: coords });
    }
    this.onSearchInputChange();
  }

  componentDidUpdate(prevProps, prevState) {
    if (!prevState.apply && this.state.apply) {
      this.filterSearch(null, this.state.clearSearch);
      this.setState({ apply: false, clearSearch: false });
    }
    // console.log('prevProps.searchedEvents', prevProps.searchedEvents);
    // console.log('this.props.searchedEvents', this.props.searchedEvents);
    // NOT WORKING
    // if (
    //   prevProps.searchedEvents.searching &&
    //   !this.props.searchedEvents.searching
    // ) {
    //   const eventsWithDistance = this.getEventsWithCalculateEventDistance();
    //   console.log('eventsWithDistance>>>', eventsWithDistance);
    //   this.setState({ eventsWithDistance });
    // }
  }

  removeFilter(key, value) {
    const { filtersApplied } = this.state;
    const indexFilterApplied =
      Array.isArray(value) || key === '__nsfw'
        ? filtersApplied.findIndex(e => Object.keys(e)[0] === key)
        : filtersApplied.findIndex(e => Object.values(e)[0] === value);

    if (indexFilterApplied !== -1) {
      filtersApplied.splice(indexFilterApplied, 1);
    }

    this.setState({
      filtersApplied,
      isFilterAdded: filtersApplied.length === 0 ? false : true,
    });

    let state = {
      apply: true,
    };
    if (filtersApplied.length === 0) {
      state.clearSearch = true;
    }

    if (Array.isArray(value) || key === 'adults') {
      console.log('removing filter ', value, key);
      switch (key) {
        case 'price':
          state[key] = Array.of(0, MAX_PRICE);
          break;
        case 'duration':
          state[key] = Array.of(0, MAX_DURATION_MIN);
          break;
        case 'distanceFilter':
          state[key] = [MAX_DISTANCE_FILTER];
          break;

        default:
          state[key] = 0;
          break;
      }
    } else {
      if (this.state[key]) {
        const items = this.state[key];
        const index = items.indexOf(value);
        if (index !== -1) {
          state[key] = [...items];
          state[key].splice(index, 1);
        }
      } else {
        let properties = [...this.state.properties];
        const currPropIndex = properties.findIndex(e => e.label === key);

        properties[currPropIndex] = {
          ...properties[currPropIndex],
          value: false,
        };

        this.setState({ properties });
      }
    }
    this.setState(state);
  }

  onRadioPress(objKey, value) {
    const items = this.state[objKey];
    let state = {};
    const index = items.indexOf(value);
    if (index === -1) {
      state[objKey] = (items || []).concat(value);
    } else {
      state[objKey] = [...items];
      state[objKey].splice(index, 1);
    }

    this.filtersArray(objKey, value);
    this.setState(state);
  }

  filtersArray(objKey, value) {
    const { filtersApplied } = this.state;
    // TODO: verify that keeps working

    if (Array.isArray(value) || objKey === 'adults' || objKey === '__nsfw') {
      const indexFilterApplied = filtersApplied.findIndex(
        e => Object.keys(e)[0] === objKey,
      );

      if (indexFilterApplied === -1) {
        filtersApplied.push({ [objKey]: value });
      } else {
        filtersApplied[indexFilterApplied] = { [objKey]: value };
      }
      // TODO: verify that keeps working
      // return;
    }

    // if (Array.isArray(value)) {
    //   const indexFilterApplied = filtersApplied.findIndex(
    //     e => Object.keys(e)[0] === objKey,
    //   );
    //   if (indexFilterApplied === -1) {
    //     filtersApplied.push({ [objKey]: value });
    //   } else {
    //     filtersApplied[indexFilterApplied] = { [objKey]: value };
    //   }
    // }
    else {
      const indexFilterApplied = filtersApplied.findIndex(
        e => Object.values(e)[0] === value,
      );
      if (indexFilterApplied === -1) {
        filtersApplied.push({ [objKey]: value });
      } else {
        filtersApplied.splice(indexFilterApplied, 1);
      }
    }
    // TODO: verify that keeps working
    // this.setState({ filtersApplied });
  }

  /**
   *
   * @param {*} e
   * @param {*} clearSearch
   * @param {*} name shortcut for clear string search when state hasn't set empty value yet
   */
  filterSearch(e, clearSearch) {
    const newState = {
      visible: false,
      isFilterAdded: clearSearch ? false : true,
      currentQuery: clearSearch ? null : this._copyFilterQuery(),
    };
    if (clearSearch === true) {
      newState.filtersApplied = [];
    }

    this.setState(newState);
    this.props.searchQueryString(
      this.composeFilterQuery(initialFilters, newState.currentQuery),
    );
  }

  _copyFilterQuery() {
    return {
      name: this.state.name,
      location: this.state.location,
      kitchen: this.state.kitchen,
      drink: this.state.drink,
      adults: this.state.adults,
      eventTypes: this.state.eventTypes,
      price: this.state.price,
      duration: this.state.duration,
      selectedStartDate: this.state.selectedStartDate,
      selectedEndDate: this.state.selectedEndDate,
      intolerances: this.state.intolerances,
      properties: this.state.properties,
    };
  }

  renderFilterChips() {
    const { filtersApplied } = this.state;
    if (filtersApplied.length === 0) {
      return;
    }

    return (
      <List.Section
        style={{ marginTop: -15, marginBottom: 0, paddingTop: 0 }}
        titleStyle={{ color: colors.whiteBackground }}
        title={i18n.t('__filters')}>
        <View style={{ ...styles.row, marginTop: -10 }}>
          {filtersApplied.map((prop, key) => {
            const value = Object.values(prop)[0];
            const keyValue = Object.keys(prop)[0];
            // console.log('...filtersApplied', prop);
            // console.log('Object.values(prop)[0]', value);
            // console.log('Object.keys(prop)[0]', Object.keys(prop)[0]);

            let filterValue = '';

            if (Array.isArray(value)) {
              if (keyValue === 'distanceFilter') {
                filterValue = `${
                  Object.values(prop)[0][0]
                } ${getDistanceUnit()}`;
              } else {
                filterValue =
                  Object.values(prop)[0][0] +
                  ' < ' +
                  (Object.values(prop)[0][1] ? Object.values(prop)[0][1] : '');
              }
            } else {
              filterValue =
                typeof value === 'number'
                  ? value
                  : typeof value === 'boolean'
                  ? i18n.t(keyValue)
                  : i18n.t(value);
            }

            return (
              <Chip
                icon={getIcon(keyValue)}
                key={key}
                onPress={() => {}}
                onClose={() => this.removeFilter(keyValue, value)}
                style={styles.chip}
                textStyle={styles.tiny}>
                {filterValue}
              </Chip>
            );
          })}
        </View>
      </List.Section>
    );
  }

  _handleLoadMore = () => {
    let { page, last } = this.props.searchedEvents;
    if (isNaN(page)) {
      page = 0;
    }
    console.log('_handleLoadMore page', page);
    let nextPage = page + 1;
    if (nextPage <= last) {
      // this.setState({ page: nextPage })
      this.onSearchInputChange(nextPage);
    }
  };

  _renderFooter = () => {
    if (!this.props.searchedEvents.searchingPage) {
      return null;
    }

    return (
      <View
        style={{
          position: 'relative',
          width: '100%',
          height: 50,
          paddingVertical: 20,
          borderTopWidth: 1,
          marginTop: 10,
          marginBottom: 10,
          borderColor: colors.primary,
        }}>
        <PacmanIndicator color={colors.primary} />
      </View>
    );
  };
  _hideModal = () => this.setState({ visible: false });

  getEventsWithCalculateEventDistance() {
    const eventWithDistance = [];
    const { events } = this.props.searchedEvents;
    const { currentLocation, distanceFilter } = this.state;
    const limitDistance = distanceFilter[0];
    // console.log('currentLocation>>>', currentLocation)
    // console.log('limitDistance>>>', limitDistance)
    // console.log('MAX_DISTANCE_FILTER>>>', MAX_DISTANCE_FILTER)
    if (currentLocation) {
      if (limitDistance < MAX_DISTANCE_FILTER) {
        events.forEach(event => {
          const distance = calculateFlyDistance(
            currentLocation.latitude,
            currentLocation.longitude,
            event.lat,
            event.lon,
          );
          if (distance > limitDistance) {
            return;
          }
          eventWithDistance.push({ ...event, distance });
        });
      } else {
        // no distance filter
        events.forEach(event => {
          const distance = calculateFlyDistance(
            currentLocation.latitude,
            currentLocation.longitude,
            event.lat,
            event.lon,
          );
          eventWithDistance.push({ ...event, distance });
        });
      }
      return eventWithDistance;
    }
    console.log(
      'getEventsWithCalculateEventDistance',
      events,
      eventWithDistance,
    );
    return events;
  }

  render() {
    const {
      visible,
      name,
      message,
      messageType,
      showAlertModal,
      // eventsWithDistance,
    } = this.state;
    const { searching, totalCount } = this.props.searchedEvents;
    const eventsWithDistance = this.getEventsWithCalculateEventDistance();

    return (
      <View style={{ flex: 1, backgroundColor: colors.primary }}>
        <View
          style={{
            padding: 10,
            paddingTop: 5,
            paddingBottom: 5,
            backgroundColor: colors.primary,
          }}>
          <Surface style={{ flexDirection: 'row', borderRadius: 5 }}>
            <Searchbar
              testID="Searchbar"
              accessibilityRole={'search'}
              style={{
                elevation: 1,
                flex: 1,
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
              }}
              inputStyle={{
                borderWidth: 0,
                borderColor: 'white',
              }}
              onChangeText={name => {
                const newState = {
                  name,
                  isFilterAdded: name !== '' ? true : this.state.isFilterAdded,
                };
                if (name === '') {
                  newState.apply = true;
                }
                this.setState(newState);
              }}
              clearIcon={null}
              value={name}
              placeholder={i18n.t('__search_placeholder')}
              onSubmitEditing={() => this.setState({ apply: true })}
            />
            <IconButton
              testID="SearchbarButton"
              onPress={() => this.setState({ visible: true })}
              icon="filter-variant"
              style={styles.iconButton}
            />
            {this.state.isFilterAdded && (
              <IconButton
                onPress={e =>
                  this.setState({
                    ...initialFilters,
                    apply: true,
                    clearSearch: true,
                  })
                }
                icon="close"
                style={styles.iconButton}
              />
            )}
          </Surface>
        </View>
        {this.renderFilterChips()}
        <Surface
          style={{
            flex: 1,
            elevation: 30,
            borderTopRightRadius: 15,
            borderTopLeftRadius: 15,
            paddingTop: 5,
          }}>
          {searching === true ? (
            <View style={globalStyles.container}>
              <PacmanIndicator size={128} color={colors.primary} />
            </View>
          ) : eventsWithDistance === null || totalCount === 0 ? (
            <View style={globalStyles.container}>
              <Icon.MaterialCommunityIcons
                name="home"
                size={80}
                color={colors.black}
              />
              <Text style={{ color: colors.black }}>
                {i18n.t('__no_events')}
              </Text>
            </View>
          ) : (
            <FlatList
              testID="SearchFlatList"
              ListHeaderComponent={
                <View
                  style={{
                    alignItems: 'center',
                    flex: 1,
                    marginTop: 0,
                    marginBottom: 0,
                  }}>
                  <Subheading
                    style={globalStyles.blueChip}>{`${totalCount} ${i18n.t(
                    '__events',
                  )}`}</Subheading>
                </View>
              }
              data={eventsWithDistance}
              refreshControl={
                <RefreshControl
                  refreshing={searching}
                  onRefresh={() => this.onSearchInputChange()}
                />
              }
              keyExtractor={(item, index) => index.toString()}
              initialNumToRender={10}
              renderItem={item => (
                <EventCard
                  {...item}
                  navigation={this.props.navigation}
                  push={this.props.navigation.push}
                />
              )}
              ListFooterComponent={this._renderFooter}
              onEndReached={this._handleLoadMore}
              onEndReachedThreshold={0.2}
            />
          )}

          <PolicyChecker
            closeDialog={(visibleDialog, messageDialog, messageTypeDialog) =>
              this.setState({
                showAlertModal: visibleDialog,
                message: messageDialog,
                messageType: messageTypeDialog,
              })
            }
          />
        </Surface>
        <FAB
          key="Search"
          onPress={() =>
            this.props.navigation.navigate(MAP_ROUTE, {
              events: eventsWithDistance,
            })
          }
          style={styles.fab}
          icon="map"
        />
        <AlertDialog
          onClose={() => {
            this.setState({
              showAlertModal: false,
            });
          }}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
        <Portal>
          <Modal visible={visible} onDismiss={this._hideModal}>
            <View
              testID="SearchDialog"
              style={{
                backgroundColor: 'white',
                minHeight: '100%',
                paddingTop: Constants.statusBarHeight,
              }}>
              <View>
                <IconButton
                  testID="SearchDialogButtonClose"
                  icon="close"
                  size={30}
                  color={'#777'}
                  style={{ marginLeft: 20 }}
                  onPress={() => this.setState({ visible: false })}
                />
              </View>
              <ScrollView style={{ flex: 1 }}>
                <List.Section>
                  <List.Accordion
                    testID="SearchDialogNSFW"
                    title={i18n.t('__general')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={images._settings}
                      />
                    )}>
                    <View
                      style={{
                        alignContent: 'center',
                        justifyContent: 'center',
                        paddingLeft: 20,
                        paddingRight: 20,
                      }}>
                      {this.state.properties.map(prop => (
                        <Input
                          type={INPUT_TYPE_SWITCHBISTATE}
                          label={prop.label}
                          key={prop.name}
                          value={prop.value}
                          explanationIcon={prop.icon}
                          change={value => {
                            let properties = [...this.state.properties];
                            const currPropIndex = properties.findIndex(
                              e => e.name === prop.name,
                            );
                            properties[currPropIndex] = {
                              ...properties[currPropIndex],
                              value,
                            };
                            this.setState({ properties });
                            this.filtersArray(prop.label, value);
                          }}
                        />
                      ))}
                      <View style={{}}>
                        <Text style={{ color: colors.black }}>{`${i18n.t(
                          '__distance',
                        )} (${getDistanceUnit()})`}</Text>
                        <MultiSlider
                          testID="SearchDialogDistanceSlider"
                          min={MIN_DISTANCE_FILTER}
                          max={MAX_DISTANCE_FILTER}
                          label={i18n.t('__distance')}
                          step={5}
                          values={this.state.distanceFilter}
                          selectedStyle={{
                            backgroundColor: '#f24858',
                          }}
                          unselectedStyle={{
                            backgroundColor: 'silver',
                          }}
                          containerStyle={{
                            justifyContent: 'center',
                            alignItems: 'center',
                          }}
                          allowOverlap
                          snapped
                          isMarkersSeparated={true}
                          onValuesChangeFinish={values => {
                            this.setState({ distanceFilter: values });
                            this.filtersArray('distanceFilter', values);
                            if (values[0] > MAX_DISTANCE_FILTER) {
                              this.removeFilter('distanceFilter', values);
                            }
                          }}
                          customMarkerLeft={e => {
                            const value =
                              e.currentValue > MAX_DISTANCE_FILTER - 1
                                ? INF_SYMBOL
                                : e.currentValue;
                            return (
                              <View>
                                <Text style={{ color: colors.black }}>
                                  {value}
                                </Text>
                                <DefaultMarker currentValue={value} />
                              </View>
                            );
                          }}
                        />
                      </View>
                    </View>
                  </List.Accordion>
                </List.Section>
                <List.Section style={{ paddingBottom: 5 }}>
                  <List.Accordion
                    testID="SearchDialogPrice"
                    title={i18n.t('__price') + ' (€)'}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={images._money}
                      />
                    )}>
                    <View style={{ paddingLeft: 20 }}>
                      <MultiSlider
                        testID="SearchDialogPriceSlider"
                        min={MIN_PRICE}
                        max={MAX_PRICE}
                        step={1}
                        values={this.state.price}
                        selectedStyle={{
                          backgroundColor: '#f24858',
                        }}
                        unselectedStyle={{
                          backgroundColor: 'silver',
                        }}
                        containerStyle={{
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                        allowOverlap
                        snapped
                        isMarkersSeparated={true}
                        onValuesChangeFinish={values => {
                          this.setState({ price: values });
                          this.filtersArray('price', values);
                        }}
                        customMarkerLeft={e => {
                          return (
                            <View>
                              <Text style={{ color: colors.black }}>
                                {e.currentValue}
                              </Text>
                              <DefaultMarker currentValue={e.currentValue} />
                            </View>
                          );
                        }}
                        customMarkerRight={e => {
                          return (
                            <View>
                              <Text style={{ color: colors.black }}>
                                {e.currentValue}
                              </Text>
                              <DefaultMarker currentValue={e.currentValue} />
                            </View>
                          );
                        }}
                      />
                    </View>
                  </List.Accordion>
                  <List.Accordion
                    testID="SearchDialogLocation"
                    title={i18n.t('__location')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={locations[0].icon}
                      />
                    )}>
                    <View style={{ paddingLeft: 20 }}>
                      {locations.map((item, index) => {
                        return (
                          <View
                            testID="SearchDialogLocationElements"
                            key={index}
                            style={{ flexDirection: 'row', marginBottom: 5 }}>
                            <View style={globalStyles.radio}>
                              <SIRadioButton
                                status={
                                  this.state.location.indexOf(item.value) !== -1
                                    ? 'checked'
                                    : 'unchecked'
                                }
                                icon={item.icon}
                                color={colors.primary}
                                onPress={this.onRadioPress.bind(
                                  this,
                                  'location',
                                  item.value,
                                )}
                              />
                            </View>
                            <Text style={styles.sliderText}>
                              {i18n.t(item.label)}
                            </Text>
                          </View>
                        );
                      })}
                    </View>
                  </List.Accordion>
                  <List.Accordion
                    title={i18n.t('__date')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={images._calendar}
                      />
                    )}>
                    <CalendarPicker
                      textStyle={{ fontFamily: 'regular' }}
                      allowRangeSelection={true}
                      selectedDayColor={colors.primary}
                      selectedDayTextColor="white"
                      selectedStartDate={this.state.selectedStartDate}
                      selectedEndDate={this.state.selectedEndDate}
                      onDateChange={(date, type) => {
                        if (date === null) {
                          return;
                        }
                        if (type === 'END_DATE') {
                          this.setState({
                            selectedEndDate: date,
                          });
                          this.filtersArray('dates', [
                            this.state.selectedStartDate.format(
                              defaultDateFormat,
                            ),
                            date.format(defaultDateFormat),
                          ]);
                        } else {
                          this.setState({
                            selectedStartDate: date,
                            selectedEndDate: null,
                          });
                        }
                      }}
                    />
                  </List.Accordion>
                  <List.Accordion
                    title={i18n.t('__kitchen')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={typeKitchen[0].icon}
                      />
                    )}>
                    <View style={{ paddingLeft: 20 }}>
                      {typeKitchen.map((item, index) => {
                        return (
                          <View
                            key={index}
                            style={{ flexDirection: 'row', marginBottom: 5 }}>
                            <View style={globalStyles.radio}>
                              <SIRadioButton
                                status={
                                  this.state.kitchen.indexOf(item.value) !== -1
                                    ? 'checked'
                                    : 'unchecked'
                                }
                                icon={item.icon}
                                color={colors.primary}
                                onPress={this.onRadioPress.bind(
                                  this,
                                  'kitchen',
                                  item.value,
                                )}
                              />
                            </View>
                            <Text style={styles.sliderText}>
                              {i18n.t(item.label)}
                            </Text>
                          </View>
                        );
                      })}
                    </View>
                  </List.Accordion>

                  <List.Accordion
                    title={i18n.t('__guests')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={images._people_icon}
                      />
                    )}>
                    {['adults'].map(e => (
                      <View
                        key={e}
                        style={{
                          flexDirection: 'row',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          paddingRight: 20,
                          marginTop: 20,
                          paddingLeft: 30,
                        }}>
                        <Subheading style={{ fontFamily: 'medium' }}>
                          {e}
                        </Subheading>
                        <UIStepper
                          height={40}
                          width={120}
                          displayValue
                          value={this.state[e]}
                          initialValue={this.state[e]}
                          borderColor={colors.primary}
                          textColor={colors.primary}
                          tintColor={colors.primary}
                          onValueChange={value => {
                            this.setState({ [e]: value });
                            this.filtersArray('adults', value);
                          }}
                        />
                      </View>
                    ))}
                  </List.Accordion>

                  <List.Accordion
                    title={i18n.t('__duration')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={images._time}
                      />
                    )}>
                    <View style={{ paddingLeft: 20 }}>
                      <MultiSlider
                        min={10}
                        max={MAX_DURATION_MIN}
                        step={15}
                        allowOverlap
                        snapped
                        values={this.state.duration}
                        onValuesChangeFinish={values => {
                          this.setState({ duration: values });
                          this.filtersArray('duration', values);
                        }}
                        selectedStyle={{
                          backgroundColor: '#f24858',
                        }}
                        unselectedStyle={{
                          backgroundColor: 'silver',
                        }}
                        containerStyle={{
                          justifyContent: 'center',
                          alignItems: 'center',
                        }}
                        customMarker={e => {
                          return (
                            <View>
                              <Text style={{ color: colors.black }}>
                                {e.currentValue}
                              </Text>
                              <DefaultMarker />
                            </View>
                          );
                        }}
                      />
                    </View>
                  </List.Accordion>

                  <List.Accordion
                    title={i18n.t('__drink')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={typeBeverage[0].icon}
                      />
                    )}>
                    <View style={{ paddingLeft: 20 }}>
                      {typeBeverage.map((item, index) => {
                        return (
                          <View
                            key={index}
                            style={{ flexDirection: 'row', marginBottom: 5 }}>
                            <View style={globalStyles.radio}>
                              <SIRadioButton
                                status={
                                  this.state.drink.indexOf(item.value) !== -1
                                    ? 'checked'
                                    : 'unchecked'
                                }
                                icon={item.icon}
                                color={colors.primary}
                                onPress={this.onRadioPress.bind(
                                  this,
                                  'drink',
                                  item.value,
                                )}
                              />
                            </View>
                            <Text style={styles.sliderText}>
                              {i18n.t(item.label)}
                            </Text>
                          </View>
                        );
                      })}
                    </View>
                  </List.Accordion>
                  <List.Accordion
                    title={i18n.t('__events')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={typeEvents[0].icon}
                      />
                    )}>
                    <View style={{ paddingLeft: 20 }}>
                      {typeEvents.map((item, index) => {
                        return (
                          <View
                            key={index}
                            style={{ flexDirection: 'row', marginBottom: 5 }}>
                            <View style={globalStyles.radio}>
                              <SIRadioButton
                                status={
                                  this.state.eventTypes.indexOf(item.value) !==
                                  -1
                                    ? 'checked'
                                    : 'unchecked'
                                }
                                icon={item.icon}
                                color={colors.primary}
                                onPress={this.onRadioPress.bind(
                                  this,
                                  'eventTypes',
                                  item.value,
                                )}
                              />
                            </View>
                            <Text style={styles.sliderText}>
                              {i18n.t(item.label)}
                            </Text>
                          </View>
                        );
                      })}
                    </View>
                  </List.Accordion>
                  <List.Accordion
                    title={i18n.t('__food_intol')}
                    left={props => (
                      <Image
                        {...props}
                        style={globalStyles.searchImage}
                        source={intollerances[0].icon}
                      />
                    )}>
                    <View style={{ paddingLeft: 20, marginTop: 5 }}>
                      {intollerances.map((item, index) => {
                        return (
                          <View
                            key={index}
                            style={{ flexDirection: 'row', marginBottom: 5 }}>
                            <View style={globalStyles.radio}>
                              <SIRadioButton
                                status={
                                  this.state.intolerances.indexOf(
                                    item.value,
                                  ) !== -1
                                    ? 'checked'
                                    : 'unchecked'
                                }
                                icon={item.icon}
                                color={colors.primary}
                                onPress={this.onRadioPress.bind(
                                  this,
                                  'intolerances',
                                  item.value,
                                )}
                              />
                            </View>
                            <Text style={styles.sliderText}>
                              {i18n.t(item.label)}
                            </Text>
                          </View>
                        );
                      })}
                    </View>
                  </List.Accordion>
                </List.Section>
              </ScrollView>
              <Button
                onPress={() => this.setState({ apply: true })}
                mode="contained"
                style={{ margin: 20, marginBottom: 40 }}
                contentStyle={{ padding: 5 }}>
                {i18n.t('__apply')}
              </Button>
            </View>
          </Modal>
        </Portal>
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    authorization: appState.authorization,
    searchedEvents: appState.searchedEvents,
    payment: appState.payment,
    account: appState.account,
    common: appState.common,
    loggedIn: isLoggedIn(appState.account),
  };
};
function mapDispatchToProps(dispatch) {
  return {
    searchQueryString: data => dispatch(SearchActions.eventSearchRequest(data)),
    addUserPreferences: preferences =>
      dispatch(AccountActions.addAccountPreferencesRequest(preferences)),
  };
}
export default connect(mapStateToProps, mapDispatchToProps)(SearchScreen);
const styles = StyleSheet.create({
  fab: {
    position: 'absolute',
    backgroundColor: colors.secondary,
    color: colors.white,
    zIndex: 100,
    elevation: 50,
    margin: 20,
    right: 0,
    bottom: 0,
  },
  row: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 10,
  },
  chip: {
    margin: 4,
  },
  tiny: {
    marginVertical: 2,
    marginRight: 2,
    marginLeft: 2,
    minHeight: 19,
    lineHeight: 19,
  },
  sliderText: { color: colors.black, marginTop: 10, fontWeight: 'bold' },
  iconButton: { borderRadius: 0, margin: 0, height: 48, width: 48 },
});
