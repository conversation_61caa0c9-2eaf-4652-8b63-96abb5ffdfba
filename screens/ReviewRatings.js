import React, { Component } from 'react';
import { View, StyleSheet } from 'react-native';
import { Caption } from 'react-native-paper';
import StarRating from 'react-native-star-rating';

import { colors } from '@constants';
import FeedbackActions from '@reducers/feedbacks.reducer';
import { connect } from 'react-redux';
import i18n from '@translations/index';
import { getLogin } from '@reducers/account.reducer';
import { widthPercentageToDP as wp } from 'react-native-responsive-screen';

class ReviewRatings extends Component {
  render() {
    const {
      foodScore = 0,
      funScore = 0,
      locationScore = 0,
      hospitalityScore = 0,
    } = this.props.ratings || {};

    return (
      <View style={styles.ratingContainer}>
        <View style={styles.ratingRow}>
          <Caption style={styles.captionStyle}>
            {i18n.t('__food_score')}
          </Caption>
          <StarRating
            disabled
            rating={foodScore / 100}
            starSize={15}
            fullStarColor={colors.fullStar}
          />
        </View>
        <View style={styles.ratingRow}>
          <Caption style={styles.captionStyle}>{i18n.t('__fun_score')}</Caption>
          <StarRating
            disabled
            rating={funScore / 100}
            starSize={15}
            fullStarColor={colors.fullStar}
          />
        </View>

        <View style={styles.ratingRow}>
          <Caption style={styles.captionStyle}>
            {i18n.t('__location_score')}
          </Caption>

          <StarRating
            disabled
            rating={locationScore / 100}
            starSize={15}
            fullStarColor={colors.fullStar}
          />
        </View>

        <View style={styles.ratingRow}>
          <Caption style={styles.captionStyle}>
            {i18n.t('__hospitality_score')}
          </Caption>
          <StarRating
            disabled
            rating={hospitalityScore / 100}
            starSize={15}
            fullStarColor={colors.fullStar}
          />
        </View>
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    feedbacks: appState.feedbacks,
    username: getLogin(appState.account),
    common: appState.common,
  };
};
function mapDispatchToProps(dispatch) {
  return {
    getReviewsOfUser: userId =>
      dispatch(FeedbackActions.reviewsAllRequest(userId)),
  };
}
export default connect(mapStateToProps, mapDispatchToProps)(ReviewRatings);

const styles = StyleSheet.create({
  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  captionStyle: {
    fontFamily: 'medium',
    color: 'black',
    marginRight: 40,
  },
  ratingContainer: {
    width: wp('90%'),
    marginTop: 10,
    marginBottom: 10,
    backgroundColor: colors.whiteBackground,
    padding: 10,
    borderRadius: 20,
    flex: 1,
    flexDirection: 'column',
  },
});
