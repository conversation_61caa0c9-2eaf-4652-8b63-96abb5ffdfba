import React, { Component } from 'react';
import { View, FlatList } from 'react-native';
import { colors } from '@constants';
import { connect } from 'react-redux';
import i18n from '@translations/index';
import { ReviewExpandableEntry } from '@components/ReviewExpandableEntry';

class ReviewListStack extends Component {
  static navigationOptions = props => {
    return {
      title: i18n.t('__review_list'),
      headerTintColor: 'white',
      headerStyle: {
        backgroundColor: colors.primary,
      },
      hasBack: true,
    };
  };

  render() {
    const { reviews } = this.props.feedbacks;
    return (
      <View
        style={{ flex: 1, backgroundColor: colors.background, paddingTop: 5 }}>
        <FlatList
          data={reviews}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({ item }) => (
            <ReviewExpandableEntry
              item={item}
              isReview={true}
              showDetails={() => {}}
            />
          )}
        />
      </View>
    );
  }
}
const mapStateToProps = appState => {
  return {
    feedbacks: appState.feedbacks,
  };
};
function mapDispatchToProps(dispatch) {
  return {};
}
export default connect(mapStateToProps, mapDispatchToProps)(ReviewListStack);
