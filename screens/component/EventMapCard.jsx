import React from 'react';
import { <PERSON><PERSON>, <PERSON>, IconButton } from 'react-native-paper';
import { View, Text } from 'react-native';
import {
  colors,
  defaultDateTimeFormatClean,
  getDistanceColor,
  getDistanceString,
  getUsernameOrPlaceholder,
  showAvatar,
} from '@constants';
import i18n from '@translations/index';
import moment from 'moment';
import * as Icon from '@expo/vector-icons';
import EventCardItem from './EventCardItem';
import { USER_INFO_ROUTE } from '@routes/route_constants';
import { useNavigation } from '@react-navigation/native';
import useUserNickname from '@hooks/useUserNickname';

const EventMapCard = ({
  item,
  loggedIn,
  showEventDetail,
  goPrevious,
  goNext,
  currentIndex,
  totalCount,
}) => {
  const navigation = useNavigation();

  const userNickname = useUserNickname(item.userId, loggedIn, 20);
  const showUserProfile = (userId, avatar) => {
    if (loggedIn) {
      navigation.push(USER_INFO_ROUTE, { userInfoId: userId });
    }
  };

  console.log('EventMapCard', item);
  return (
    <Card
      style={{
        marginLeft: 5,
        marginRight: 5,
        marginTop: -5,
        marginBottom: 0,
        flex: 1,
      }}>
      <Card.Title
        titleStyle={{
          fontFamily: 'medium',
          fontSize: 20,
          fontWeight: '700',
          marginBottom: 0,
          marginLeft: 10,
          marginRight: 10,
          marginTop: -30,
          textAlign: 'center',
        }}
        title={item.name}
        subtitle={`${moment(item.date).format(defaultDateTimeFormatClean)} - ${
          item.duration
        } min`}
        subtitleStyle={{
          fontFamily: 'regular',
          marginBottom: 0,
          textAlign: 'center',
        }}
        leftStyle={{ marginTop: -40, marginLeft: -10 }}
        left={() => (
          <View
            style={{
              flex: 1,
              minWidth: 80,
              flexDirection: 'column',
              alignItems: 'center',
            }}>
            {showAvatar(item.userId, loggedIn, 50, showUserProfile)}
            <Text style={{ fontSize: 12, color: colors.black }}>
              {userNickname}
            </Text>
          </View>
        )}
        right={() => {
          return (
            <View style={{ alignItems: 'center', textAlign: 'center' }}>
              <IconButton
                onPress={showEventDetail}
                size={24}
                color={colors.primary}
                icon={({ size, color }) => (
                  <Icon.MaterialCommunityIcons
                    name="eye"
                    size={size}
                    color={color}
                  />
                )}
              />
            </View>
          );
        }}
      />
      <View
        style={{
          // backgroundColor: colors.blue,
          // flex: 1,
          marginTop: -40,
          marginBottom: 0,
          flexDirection: 'row',
          alignItems: 'center',
          // alignContent: 'center',
          justifyContent: 'center',
        }}>
        <Text style={{ color: colors.black }}>{item.pricepp} </Text>
        <Icon.MaterialIcons size={15} name="euro-symbol" />
        {item.distance ? (
          <Text
            style={{
              color: getDistanceColor(item.distance),
            }}>{`     ${getDistanceString(item.distance)}`}</Text>
        ) : (
          <></>
        )}
      </View>
      <Card.Content
        style={{
          // flexGrow: 1,
          flexShrink: 0,
          flex: 1,
          marginBottom: 10,
          justifyContent: 'center',
        }}>
        <EventCardItem item={item} />
      </Card.Content>
      <Card.Actions
        style={{
          justifyContent: 'space-around',
          marginTop: 0,
          padding: 0,
          marginBottom: 5,
        }}>
        <Button
          icon="arrow-left"
          mode="outlined"
          style={{ marginRight: 5, padding: 0 }}
          onPress={goPrevious}>
          {i18n.t('__previous')}
        </Button>
        <Text
          style={{
            color: colors.black,
          }}>{`${currentIndex} / ${totalCount}`}</Text>
        <Button icon="arrow-right" mode="outlined" onPress={goNext}>
          {i18n.t('__next')}
        </Button>
      </Card.Actions>
    </Card>
  );
};

export default EventMapCard;
