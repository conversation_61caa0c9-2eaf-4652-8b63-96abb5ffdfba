import React from 'react';
import { Divider, Surface, Chip } from 'react-native-paper';
import { Image, ScrollView } from 'react-native';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';
import {
  getIconFromArray,
  globalStyles,
  typeBeverage,
  typeEvents,
  typeKitchen,
} from '@constants';
import i18n from '@translations/index';

const EventCardItem = ({ item }) => {
  return (
    <Surface
      style={{
        flex: 1,
        maxHeight: hp('22%'),
        marginTop: 5,
        elevation: 1,
        paddingLeft: 20,
        paddingRight: 20,
        paddingTop: 5,
        borderRadius: 20,
        position: 'relative',
      }}>
      <ScrollView
        horizontal
        contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
        style={{
          marginLeft: -10,
          marginRight: -10,
        }}>
        {item.typeKs?.map((prop, key) => (
          <Chip style={{ marginRight: 10 }} key={Math.random()}>
            <Image
              style={globalStyles.chipImage}
              source={getIconFromArray(typeKitchen, prop)}
            />
            {i18n.t(prop)}
          </Chip>
        ))}
      </ScrollView>
      <Divider style={globalStyles.marginDivider3} />
      <ScrollView
        horizontal
        contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
        style={{ marginLeft: -10, marginRight: -10 }}>
        {item.typeBs?.map((prop, key) => (
          <Chip style={{ marginRight: 10 }} key={Math.random()}>
            <Image
              style={globalStyles.chipImage}
              source={getIconFromArray(typeBeverage, prop)}
            />{' '}
            {i18n.t(prop)}
          </Chip>
        ))}
      </ScrollView>
      <Divider style={globalStyles.marginDivider3} />
      <ScrollView
        horizontal
        contentContainerStyle={{ flexGrow: 1, justifyContent: 'center' }}
        style={{ marginLeft: -10, marginRight: -10, marginBottom: 5 }}>
        {item.typeEs?.map((prop, key) => (
          <Chip style={{ marginRight: 10 }} key={Math.random()}>
            <Image
              style={globalStyles.chipImage}
              source={getIconFromArray(typeEvents, prop)}
            />{' '}
            {i18n.t(prop)}
          </Chip>
        ))}
      </ScrollView>
      <Divider style={globalStyles.marginDivider3} />
    </Surface>
  );
};

export default EventCardItem;
