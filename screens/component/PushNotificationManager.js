import { useEffect, useState } from 'react';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getAvatar, parsePushNotification } from '@constants/functions';
import { MESSAGES_ROUTE } from '@routes/route_constants';
import { appConfig } from '@config/app-config';
import { EventRegister } from 'react-native-event-listeners';
import MessageActions from '@reducers/message.reducer';
import { PermissionsAndroid, Platform } from 'react-native';
import { getParamsObject } from '@constants/functions';
import { useDispatch } from 'react-redux';
import notifee, {
  EventType,
  TimestampTrigger,
  TriggerType,
} from '@notifee/react-native';
import { colors } from '@constants/colors';
import Config from 'react-native-config';
import i18n from '@translations/index';

/**
 * refers to https://www.codementor.io/@uokesita/react-native-push-notifications-with-firebase-2019-10on0z19t6
 */
const PushNotificationManager = ({ navigation }) => {
  const dispatch = useDispatch();
  const [channelId, setChannelId] = useState(null);
  const [notificationId, setNotificationId] = useState('1');

  const checkPermission = async () => {
    if (Platform.OS === 'android') {
      //https://rnfirebase.io/messaging/usage#android---requesting-permissions
      PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      );
    }
    const apns = await messaging().getAPNSToken();
    console.log('PushNotificationManager checkPermission apns', apns);
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;
    console.log('PushNotificationManager checkPermission enabled', enabled);
    if (enabled) {
      getFcmToken();
    } else {
      requestPermission();
    }

    const topicRegistration = await messaging().subscribeToTopic(
      appConfig.notificationDefaultChannelId,
    );
    console.log(
      'PushNotificationManager checkPermission topicRegistration',
      topicRegistration,
    );
    await messaging().onTokenRefresh(async fcmToken => {
      console.log(
        'PushNotificationManager onTokenRefresh fcmToken>>',
        fcmToken,
      );
      await AsyncStorage.setItem('deviceToken', fcmToken);
    });
  };

  let RETRY_COUNT = 0; // outside of this function
  const getFcmToken = async () => {
    try {
      const fcmToken = await messaging().getToken();
      console.log('PushNotificationManager fcmToken>>', fcmToken);
      if (fcmToken) {
        await AsyncStorage.setItem('deviceToken', fcmToken);
        // console.log('PushNotification props', props)
        // props.notificationService.localNotification(null)
      } else {
        console.log('PushNotificationManager Failed', 'No token received');
      }
    } catch (error) {
      console.log('r🔥  --- requestUserPermission --- error', error);
      RETRY_COUNT = RETRY_COUNT + 1;
      if (RETRY_COUNT < 3) {
        await requestPermission();
      }
      return;
    }
  };

  const requestPermission = async () => {
    try {
      // https://rnfirebase.io/messaging/ios-permissions
      const authorizationStatus = await messaging().requestPermission({
        alert: true,
        announcement: true,
        badge: true,
        carPlay: true,
        provisional: true,
        sound: true,
      });
      // User has authorised
      if (authorizationStatus === messaging.AuthorizationStatus.AUTHORIZED) {
        console.log('User has notification permissions enabled.');
      } else if (
        authorizationStatus === messaging.AuthorizationStatus.PROVISIONAL
      ) {
        console.log('User has provisional notification permissions.');
      } else {
        console.log('User has notification permissions disabled');
      }
      await notifee.requestPermission();
    } catch (error) {
      // User has rejected permissions
      console.log('requestPermission rejected', error);
    }
  };

  const manageCloudMessage = async remoteMessage => {
    console.log(
      `PushNotification [${remoteMessage.messageId}] => `,
      remoteMessage,
    );
    const data = await parsePushNotification(remoteMessage);
    console.log('>>>>path data', data);
    console.log('>>>>path data.path', data.path);
    if (data.path.includes(MESSAGES_ROUTE)) {
      //NO need to show notification
      console.log(
        `PushNotification [${remoteMessage.messageId}] MESSAGES_ROUTE message:`,
        data,
      );
      EventRegister.emit('messageReceived', data);
      dispatch(MessageActions.appendUnreadMessageDrawer(data));
      return;
    }

    // switch (currentScreen) {
    //   case null:
    //   case 'Chat':
    //   case 'EMPTY_ROUTE':
    //     return
    //   default: //chat message
    // props.notificationService.localNotification(data, path);
    console.log('before onDisplayNotification data', data.data);
    console.log('before onDisplayNotification title', data.data.title);
    console.log('before onDisplayNotification path', data.path);
    console.log('before onDisplayNotification message', data.data.message);
    console.log(
      'before onDisplayNotification userRxAvatar',
      data.data.userRxAvatar,
    );
    console.log(
      'before onDisplayNotification userTxAvatar',
      data.data.userTxAvatar,
    );
    onDisplayNotification({
      title: data.data.title,
      body: data.data.message,
      path: data.path,
      ...data,
    });
  };

  useEffect(() => {
    console.log('PushNotificationManager useEffect checkPermission');
    checkPermission();
    const unsubscribe = messaging().onMessage(manageCloudMessage);

    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const createDefaultChannels = async () => {
    console.log('NotifeeManager createDefaultChannels');
    setChannelId(
      await notifee.createChannel({
        id: appConfig.notificationDefaultChannelId,
        name: appConfig.notificationDefaultChannelName,
        vibration: true,
        importance: 4,
        description: 'Selfinvite notification channel',
        sound: 'default',
      }),
    );
  };

  useEffect(() => {
    createDefaultChannels();
    requestPermission();
    const unsubscribe = notifee.onForegroundEvent(({ type, detail }) => {
      console.log('onForegroundEvent', type, detail);
      switch (type) {
        case EventType.DISMISSED:
          console.log('User dismissed notification', detail.notification);
          break;
        case EventType.PRESS:
          console.log('User pressed notification', detail.notification);
          // Navigation
          if (!detail.notification.data.path) {
            return;
          }
          const { basePath, params } = getParamsObject(
            detail.notification.data.path,
          );

          console.log('notification path', basePath, params);
          const { basePath: newBasePath, params: newParams } =
            translateNestedPath({ basePath, params });
          console.log(
            'translateNestedPath notification path',
            newBasePath,
            newParams,
          );
          navigation.navigate(newBasePath, newParams);
          break;
        case EventType.ACTION_PRESS:
          console.log('ACTION_PRESS', detail.notification);
          break;
        case EventType.DELIVERED:
          console.log('DELIVERED', detail.notification);
          break;
      }
    });
    return unsubscribe;
  }, []);

  const translateNestedPath = ({ basePath, params }) => {
    if (basePath === 'reviewsliststack') {
      return {
        basePath: 'feedbackreviewtab',
        params: {
          screen: 'reviewsliststack',
          params: params,
        },
      };
    }
    if (basePath === 'feedbacksliststack') {
      return {
        basePath: 'feedbackreviewtab',
        params: {
          screen: 'feedbacksliststack',
          params: params,
        },
      };
    }
    return { basePath, params };
  };

  const cancel = async notificationToBeDeleted => {
    await notifee.cancelNotification(notificationToBeDeleted);
  };

  // Scheduled notification
  const onCreateTriggerNotification = async () => {
    const date = new Date(Date.now());
    date.setHours(11);
    date.setMinutes(10);

    // Create a time-based trigger
    const trigger = {
      type: TriggerType.TIMESTAMP,
      timestamp: date.getTime(), // fire at 11:10am (10 minutes before meeting)
    };

    // Create a trigger notification
    await notifee.createTriggerNotification(
      {
        title: 'Meeting with Jane',
        body: 'Today at 11:20am',
        android: {
          channelId: 'your-channel-id',
        },
      },
      trigger,
    );
  };

  const onDisplayNotification = async ({ title, body, data, path }) => {
    console.log('NotifeeManager onDisplayNotification 1', title);
    console.log('NotifeeManager onDisplayNotification 2', body);
    console.log('NotifeeManager onDisplayNotification 3', data);
    console.log('NotifeeManager onDisplayNotification 4', data.userRxAvatar);
    console.log('NotifeeManager onDisplayNotification 5', data.userTxAvatar);
    console.log('NotifeeManager onDisplayNotification 6', path);

    let largeIconPath = Config.ASSETS_BASE_PROFILE + data.userRxAvatar;
    if (
      path.includes('incomingeventrequestsstack') ||
      path.includes('feedbacksliststack')
    ) {
      largeIconPath = Config.ASSETS_BASE_PROFILE + data.userTxAvatar;
    }

    console.log('NotifeeManager largeIconPath 3', largeIconPath);
    await notifee.displayNotification({
      id: notificationId,
      title,
      body: body.replace(/\n/g, '<br />'),
      data,
      android: {
        color: colors.primary,
        channelId: appConfig.notificationDefaultChannelId,
        smallIcon: 'ic_launcher',
        largeIcon: largeIconPath,
        // pressAction is needed if you want the notification to open the app when pressed
        // circularLargeIcon: true,
        pressAction: {
          id: 'default',
        },
        // style: {
        //   type: notifee.AndroidStyle.BIGTEXT,
        //   // type: notifee.AndroidStyle.BIGPICURE,
        //   // picture: largeIconPath,
        //   text: body.replace(/\n/g, '<br />'),
        // },
        // category: 'msg',
        visibility: 1,
        vibration: true,
        vibrationPattern: [300, 500],
      },
      ios: {
        sound: 'default',
      },
    });
    setNotificationId((Number(notificationId) + 1).toString());
  };

  return null;
};

export default PushNotificationManager;
