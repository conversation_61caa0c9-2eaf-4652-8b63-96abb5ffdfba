import React from 'react';
import { Button } from 'react-native-paper'; // replace with your actual imports
import i18n from '@translations/index';
import { REVIEW_HOST_LIST_ROUTE } from '@routes/route_constants';
import { useNavigation } from '@react-navigation/native';

const ReviewSummaryButton = ({ reviews }) => {
  const navigation = useNavigation();

  if (reviews.length > 0) {
    return (
      <Button
        uppercase={false}
        onPress={() => navigation.navigate(REVIEW_HOST_LIST_ROUTE)}>
        {i18n.t('__see_reviews')}
      </Button>
    );
  }

  return null;
};

export default ReviewSummaryButton;
