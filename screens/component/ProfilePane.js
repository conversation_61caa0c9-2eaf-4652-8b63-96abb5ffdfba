import React, { Component } from 'react';
import Field from './Field';
import i18n from '@translations/index';
import {
  defaultDateFormat,
  images,
  PARAMETER_PREFERENCE_USER_GENDER,
  intollerances as intollerantsArray,
  checkAddress,
  PARAMETER_PREFERENCE_INTOLLERANCE,
  PARAMETER_PREFERENCE_USER_DESCRIPTION,
  globalStyles,
  getIconFromArray,
  checkPhoneNumber,
  colors,
  PARAMETER_PREFERENCE_BADGE,
  icon_size_xsmall,
  icon_size_chip,
} from '@constants';
import { View, Image, Text } from 'react-native';
import moment from 'moment';
import { Chip, IconButton, Surface } from 'react-native-paper';
import { ADD_PHONE_ROUTE, EDIT_PROFILE_ROUTE } from '@routes/route_constants';
import { badgesArray } from '@constants/badgesArray';

import * as Icon from '@expo/vector-icons';
import AlertDialog from '@components/AlertDialog';
import { _WARNING } from '@constants/constants';

export class ProfilePane extends Component {
  constructor(props) {
    super(props);
    this.state = {
      profileButtonColor: colors.warning,
      phoneButtonColor: colors.warning,
      showAlertModal: false,
      message: '',
      messageType: _WARNING,
    };
  }

  checkPhoneNumberState = (showData = false) => {
    const { account } = this.props.account;
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <Image
          style={{
            height: icon_size_xsmall,
            width: icon_size_xsmall,
            marginRight: 10,
          }}
          source={checkPhoneNumber(account) ? images._success : images._warning}
        />
        {showData && checkPhoneNumber(account) ? (
          <Text style={{ color: colors.black }}>{account.phoneNumber}</Text>
        ) : (
          <></>
        )}
      </View>
    );
  };

  verifyPhoneUpdatable = () => {
    console.log('verifyPhoneUpdatable', this.props.account.account);
    const { account } = this.props.account;
    if (
      account.postcode === null ||
      account.city === null ||
      account.address === null ||
      account.country === null
    ) {
      this.setState({
        showAlertModal: true,
        message: i18n.t('__verify_address_first'),
        messageType: _WARNING,
      });
    } else {
      this.props.navigation.navigate(ADD_PHONE_ROUTE);
    }
  };

  checkAddressState = (showData = false) => {
    const { account } = this.props.account;
    return (
      <View
        style={{
          flex: 1,
          flexDirection: 'row',
          alignItems: 'center',
        }}>
        <Image
          style={{
            height: icon_size_xsmall,
            width: icon_size_xsmall,
            marginRight: 10,
          }}
          source={checkAddress(account) ? images._success : images._warning}
        />
        {showData && checkAddress(account) ? (
          <Text
            style={{
              color: colors.black,
            }}>{`${account.address} ${account.city} ${account.postcode} ${account.country}`}</Text>
        ) : (
          <></>
        )}
      </View>
    );
  };

  render() {
    const { account, preferences } = this.props.account;
    const { showAlertModal, message, messageType } = this.state;
    let intollerances = [];
    let user_gender = [];
    let user_badges = [];
    let user_description = [];
    let chips = [];
    let badges = [];

    if (
      preferences !== undefined &&
      preferences !== null &&
      preferences.status === undefined &&
      preferences.error === undefined
    ) {
      intollerances = preferences.filter(item =>
        item.k.includes(PARAMETER_PREFERENCE_INTOLLERANCE),
      );
      user_badges = preferences.filter(item =>
        item.k.includes(PARAMETER_PREFERENCE_BADGE),
      );
      user_description = preferences.filter(
        item => item.k === PARAMETER_PREFERENCE_USER_DESCRIPTION,
      );

      user_gender = preferences.filter(
        item => item.k === PARAMETER_PREFERENCE_USER_GENDER,
      );

      chips = intollerances.map((prop, key) => {
        return (
          <Chip style={{ marginRight: 10, marginTop: 5 }} key={key}>
            <Image
              style={{ width: icon_size_chip, height: icon_size_chip }}
              source={getIconFromArray(intollerantsArray, prop.v)}
            />{' '}
            {i18n.t(prop.v)}
          </Chip>
        );
      });

      badges = user_badges.map((badge, key) => (
        <Chip style={globalStyles.badge} key={key}>
          <Image
            style={{ width: icon_size_chip, height: icon_size_chip }}
            source={getIconFromArray(badgesArray, badge.v)}
          />{' '}
          {i18n.t(badge.v)}
        </Chip>
      ));
    }

    return (
      <View>
        <View style={globalStyles.contentCard}>
          <Surface
            style={{
              position: 'absolute',
              top: -15,
              width: 35,
              height: 35,
              borderWidth: 2,
              borderColor: colors.black,
              borderRadius: 25,
              elevation: 10,
              right: 10,
            }}>
            <IconButton
              icon={({ size, color }) => (
                <Icon.Feather size={size} color={color} name="edit-3" />
              )}
              size={25}
              style={{ marginTop: -5, marginLeft: -5, padding: 0 }}
              onPress={() => this.props.navigation.navigate(EDIT_PROFILE_ROUTE)}
            />
          </Surface>
          <Field
            label={i18n.t('__nickname')}
            value={account?.nickname || account?.email}
          />
          <Field
            label={i18n.t('__name')}
            value={
              account !== null &&
              account.firstName !== null &&
              account.lastName !== null
                ? account.firstName + ' ' + account.lastName
                : ''
            }
          />
          <Field label={i18n.t('__email')} value={account?.email} />
          <Field
            label={i18n.t('__dob')}
            value={
              account !== null && account?.dob
                ? moment(account.dob).format(defaultDateFormat)
                : ''
            }
          />
          <Field
            label={i18n.t('__gender')}
            value={
              user_gender !== undefined && user_gender.length > 0
                ? i18n.t(user_gender[0].v)
                : ''
            }
          />
          <Field
            label={i18n.t('__address')}
            content={this.checkAddressState(true)}
          />
          <Field
            label={i18n.t('__nationality')}
            content={<Text>{account?.country ?? ''}</Text>}
          />
          <Field
            label={i18n.t('__description')}
            collapsableOver={100}
            value={
              user_description !== undefined && user_description.length > 0
                ? user_description[0].v
                : ''
            }
          />
          <Field
            label={i18n.t('__food_intol')}
            content={
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  marginBottom: 40,
                }}>
                {chips}
              </View>
            }
          />
          <Field
            label={i18n.t('__badges')}
            content={
              <View
                style={{
                  flexDirection: 'row',
                  flexWrap: 'wrap',
                  marginBottom: 40,
                }}>
                {badges}
              </View>
            }
          />
        </View>
        <View
          style={{
            ...globalStyles.contentCard,
            marginTop: 30,
            marginBottom: 30,
            opacity: this.props.disablePhoneView ? 0.4 : 1,
          }}>
          <Surface
            style={{
              position: 'absolute',
              top: -15,
              width: 35,
              height: 35,
              borderWidth: 2,
              borderColor: colors.black,
              borderRadius: 25,
              elevation: 10,
              right: 10,
            }}>
            <IconButton
              icon={({ size, color }) => (
                <Icon.Feather size={size} color={color} name="edit-3" />
              )}
              size={25}
              disabled={this.props.disablePhoneView}
              style={{ marginTop: -5, marginLeft: -5, padding: 0 }}
              onPress={() => this.verifyPhoneUpdatable()}
            />
          </Surface>
          <Field
            label={i18n.t('__phone_number')}
            content={this.checkPhoneNumberState(true)}
          />
        </View>

        <AlertDialog
          onClose={() => this.setState({ showAlertModal: false })}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
      </View>
    );
  }
}

export default ProfilePane;
