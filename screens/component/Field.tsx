import React, { FC, useState } from 'react';
import {
  View,
  Image,
  ImageSourcePropType,
  StyleProp,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import { Caption, Subheading } from 'react-native-paper';
import { colors, icon_size_xsmall, images } from '@constants/index';
import * as Icon from '@expo/vector-icons';
import { heightPercentageToDP as hp } from 'react-native-responsive-screen';

interface FieldProps {
  label: string;
  content?: JSX.Element;
  value?: string;
  icon?: ImageSourcePropType;
  collapsableOver?: number;
}

const Field: FC<FieldProps> = ({
  label,
  content,
  value,
  icon,
  collapsableOver,
}) => {
  const [collapsed, setCollapsed] = useState(true);
  return (
    <View
      style={
        {
          borderBottomColor: '#f5f5f5',
          borderBottomWidth: 1,
          paddingBottom: 10,
          marginBottom: 10,
        } as StyleProp<ViewStyle>
      }>
      <View
        style={
          {
            flex: 1,
            flexDirection: 'row',
            alignItems: 'center',
          } as StyleProp<ViewStyle>
        }>
        {collapsableOver && value && value?.length > collapsableOver ? (
          <TouchableOpacity onPress={() => setCollapsed(!collapsed)}>
            <Icon.Entypo
              name={collapsed ? 'triangle-down' : 'triangle-up'}
              color={colors.gray}
              size={30}
            />
          </TouchableOpacity>
        ) : (
          <></>
        )}
        <Caption
          style={
            { fontFamily: 'bold', color: colors.text } as StyleProp<TextStyle>
          }>
          {label}{' '}
        </Caption>
        {icon ? (
          <Image
            style={
              {
                height: icon_size_xsmall,
                width: icon_size_xsmall,
                marginRight: 10,
              } as StyleProp<ViewStyle>
            }
            source={images._success}
          />
        ) : (
          <></>
        )}
      </View>
      {(value?.length ?? 0) > (collapsableOver || 0) ? (
        <TouchableOpacity onPress={() => setCollapsed(!collapsed)}>
          <Subheading
            numberOfLines={collapsed ? 3 : undefined}
            ellipsizeMode="tail"
            style={collapsed && { height: hp('3%') }}>
            {value}
          </Subheading>
        </TouchableOpacity>
      ) : (
        content || <Subheading>{value}</Subheading>
      )}
    </View>
  );
};

export default Field;
