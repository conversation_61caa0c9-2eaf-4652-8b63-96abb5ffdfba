import { View, FlatList, StyleSheet, ScrollView, Text } from 'react-native';

import {
  Button,
  Portal,
  Dialog,
  Paragraph,
  TextInput,
  RadioButton,
} from 'react-native-paper';
import React from 'react';
import i18n from '@translations/index';
import { colors } from '@constants/colors';

const FindAddress = ({ item, navigate, deleteEvent, reloadHandle }) => {
  return (
    <Portal>
      <Dialog
        visible={this.state.showAddress}
        onDismiss={this.hideAddressHelper}>
        <Dialog.Title>{i18n.t('__your_address')}</Dialog.Title>
        <Dialog.Content>
          <Paragraph>{i18n.t('__your_address_tip')}</Paragraph>
          <TextInput
            label={i18n.t('__address')}
            value={searchAddress}
            onChangeText={address => this.setState({ searchAddress: address })}
            onSubmitEditing={event => this.getNominatum(event.nativeEvent.text)}
          />
          <View style={{ maxHeight: '50%' }}>
            <ScrollView style={{ flexGrow: 0, padding: 0 }}>
              <FlatList
                data={searchResult}
                keyExtractor={(item, index) => index.toString()}
                renderItem={({ item }) => (
                  <RadioButton.Group
                    onValueChange={value => this.setState({ value })}
                    value={this.state.value}>
                    <View>
                      <RadioButton
                        value={item.display_name}
                        onPress={() => {
                          this.selectAddress(item);
                        }}
                      />
                      <Text style={{ color: colors.black }}>
                        {item.display_name}
                      </Text>
                    </View>
                  </RadioButton.Group>
                )}
              />
            </ScrollView>
          </View>
        </Dialog.Content>
        <Dialog.Actions>
          <Button onPress={this.hideAddressHelper}>{i18n.t('__cancel')}</Button>
          <Button
            disabled={this.state.enableConfirmAddress}
            onPress={this.hideAddressHelper}>
            {confirmAddressText}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
};

const styles = StyleSheet.create({});
