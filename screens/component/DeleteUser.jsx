import React from 'react';
import i18n from '@translations/index';
import { View } from 'react-native';
import { globalStyles } from '@constants';
import { Button } from 'react-native-paper';
import Field from './Field';
import { useNavigation } from '@react-navigation/native';
import { DRAWER_DELETE_PROFILE_ROUTE } from '@routes/route_constants';
import { getIcon } from '@constants/functions';

const DeleteUser = () => {
  const navigation = useNavigation();

  return (
    <View
      style={{
        ...globalStyles.contentCard,
        marginBottom: 30,
      }}>
      <Field
        label={i18n.t('__risky_delete')}
        content={
          <Button
            onPress={() => navigation.navigate(DRAWER_DELETE_PROFILE_ROUTE)}
            uppercase={false}
            mode="contained"
            style={{ marginTop: 10, elevation: 0 }}
            icon={getIcon('delete_profile')}>
            {i18n.t('__delete_profile')}
          </Button>
        }
      />
    </View>
  );
};

export default DeleteUser;
