import React, { PureComponent } from 'react';

import { ImageBackground, View, Text, StyleSheet, Image } from 'react-native';
import PropTypes from 'prop-types';
import { Surface, Card, IconButton } from 'react-native-paper';
import {
  colors,
  defaultDateTimeFormatClean,
  EVENT_STATE_CLOSING,
  EVENT_STATE_OPEN,
  getDistanceColor,
  getDistanceString,
  getFirstImage,
  icon_size_small,
  images,
  showAvatar,
} from '@constants';
import * as Icon from '@expo/vector-icons';
import {
  widthPercentageToDP as wp,
  heightPercentageToDP as hp,
} from 'react-native-responsive-screen';
import i18n from '@translations/index';
import moment from 'moment';
import { connect } from 'react-redux';
import { isLoggedIn } from '@reducers/account.reducer';
import { EVENT_DETAILS_ROUTE } from '@routes/route_constants';
import TagText from '@components/TagText';
import { globalStyles } from '@constants/styles';

const propTypes = {
  item: PropTypes.object,
  // navigate: PropTypes.func,
};

const EventStateLabel = ({ state }) => {
  return (
    <View
      style={{
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 0,
      }}>
      {state === EVENT_STATE_OPEN ? (
        <Text
          style={{
            ...globalStyles.eventStateLabel,
            borderColor: colors.success,
            color: colors.success,
          }}>
          {i18n.t('__open')}
        </Text>
      ) : state === EVENT_STATE_CLOSING ? (
        <Text
          style={{
            ...globalStyles.eventStateLabel,
            borderColor: colors.warning,
            color: colors.warning,
          }}>
          {i18n.t('__closing')}
        </Text>
      ) : (
        <Text
          style={{
            ...globalStyles.eventStateLabel,
            borderColor: colors.error,
            color: colors.error,
          }}>
          {i18n.t('__closed')}
        </Text>
      )}
    </View>
  );
};
class EventCard extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { name, objectID, date, userId, state, properties, distance } =
      this.props.item;
    const isNsfw = properties && properties?.nsfw === 'true' ? true : false;

    return (
      <Surface
        style={{
          marginHorizontal: 20,
          marginVertical: 10,
          borderRadius: 30,
          borderBottomLeftRadius: 30,
          elevation: 15,
        }}>
        <ImageBackground
          source={getFirstImage(this.props.item)}
          blurRadius={isNsfw ? 40 : 1}
          style={{
            width: '100%',
            height: hp('25%'),
            borderRadius: 30,
            borderBottomLeftRadius: 30,
            overflow: 'hidden',
            justifyContent: 'flex-end',
          }}>
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              margin: 10,
              flexDirection: 'row',
            }}>
            <Surface style={styles.surface}>
              <IconButton
                onPress={() => {
                  this.props.push(EVENT_DETAILS_ROUTE, {
                    id: objectID,
                    userId,
                  });
                }}
                color={colors.declined}
                size={16}
                icon={({ size, color }) => (
                  <Icon.MaterialCommunityIcons
                    name="eye"
                    color={color}
                    size={size}
                  />
                )}
              />
            </Surface>
          </View>
          {isNsfw ? (
            <View
              style={{
                position: 'absolute',
                top: 0,
                right: 0,
                marginTop: 10,
                flexDirection: 'row',
              }}>
              <Surface style={styles.surface}>
                <Image
                  style={{
                    height: icon_size_small,
                    width: icon_size_small,
                  }}
                  source={images._gte18}
                />
              </Surface>
            </View>
          ) : null}

          <Card.Title
            style={{
              backgroundColor: colors.whiteBackground,
              margin: 10,
              padding: 0,
              borderRadius: 20,
              borderBottomLeftRadius: 20,
              elevation: 10,
            }}
            left={() => (
              <Surface
                style={{
                  elevation: 3,
                  borderRadius: 25,
                  height: 50,
                  width: 50,
                  paddingLeft: 0,
                  margin: -5,
                }}>
                {showAvatar(userId, this.props.loggedIn, 50)}
              </Surface>
            )}
            right={() => <EventStateLabel state={state} />}
            rightStyle={{
              marginBottom: -45,
              marginRight: 10,
              paddingBottom: 0,
            }}
            titleStyle={{ fontFamily: 'medium', fontSize: 14 }}
            title={
              distance ? (
                <View>
                  <TagText value={name} />
                  <Text
                    style={{
                      ...styles.distance_text,
                      color: getDistanceColor(distance),
                    }}>
                    {getDistanceString(distance)}
                  </Text>
                </View>
              ) : (
                <TagText value={name} />
              )
            }
            subtitle={`${moment(date).format(defaultDateTimeFormatClean)}`}
          />
        </ImageBackground>
      </Surface>
    );
  }
}

EventCard.propTypes = propTypes;

const mapStateToProps = appState => {
  return {
    loggedIn: isLoggedIn(appState.account),
  };
};
function mapDispatchToProps(dispatch) {
  return {};
}

export default connect(mapStateToProps, mapDispatchToProps)(EventCard);

const styles = StyleSheet.create({
  surface: {
    backgroundColor: 'white',
    elevation: 10,
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  distance_text: {
    fontSize: 12,
  },
});
