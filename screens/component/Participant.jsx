import React from 'react';
import { View } from 'react-native';
import { colors } from '@constants/colors';
import { ellipsis } from '@constants/functions';
import useUserNickname from '@hooks/useUserNickname';
import { Avatar, Caption } from 'react-native-paper';
import useUserAvatar from '@hooks/useUserAvatar';
import i18n from '@translations/index';

const Participant = ({ item }) => {
  const userNickname = useUserNickname(item.userId);
  const userAvatar = useUserAvatar(item.userId);

  return (
    <View>
      <Avatar.Image
        icon="home"
        source={{ uri: userAvatar }}
        style={{ margin: 10, backgroundColor: colors.warning }}
      />
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <Caption
          style={{
            fontFamily: 'medium',
            color: userNickname ? colors.black : colors.red,
            lineHeight: 14,
            fontSize: 12,
            margin: 0,
          }}>
          {userNickname ? ellipsis(userNickname) : i18n.t('__user_deleted')}
        </Caption>
      </View>
    </View>
  );
};

export default Participant;
