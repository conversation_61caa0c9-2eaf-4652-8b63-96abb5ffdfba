// create a FC component called KycManagedFlow which has a button that call function uploadKyc, the return of this function is a url that must be opened in a browser
//
// Path: screens/component/KycManagedFlow.jsx
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import PaymentActions from '@reducers/payment.reducer';
import i18n from '@translations/index';
import { AppState, Image, Linking, View } from 'react-native';
import { Button, Text } from 'react-native-paper';
import Field from './Field';
import { colors, _ERROR, _SUCCESS, globalStyles } from '@constants';
import { checkKycValidatedStatus, getIcon } from '@constants/functions';
import { images } from '@constants/images';
import {
  KYC_VALIDATED,
  KYC_VALIDATION_ASKED,
  KYC_VALIDATION_CREATED,
  KYC_VALIDATION_FAILED,
  icon_size_xsmall,
} from '@constants/constants';
import { useFocusEffect } from '@react-navigation/native';

const KycManagedFlow = ({ disableKyc, disableKycView }) => {
  const dispatch = useDispatch();
  const payment = useSelector(state => state.payment);

  const [kycMessage, setKycMessage] = useState('__kyc_onboard');
  const [kycButtonColor, setKycButtonColor] = useState(colors.blue);
  const [kycDetails, setKycDetails] = useState(null);

  const startKycManagedFlow = () => {
    dispatch(PaymentActions.paymentKycManagedFlowRequest());
  };
  useEffect(() => {
    const kycManagedFlowLinkClean = () =>
      dispatch(PaymentActions.paymentKycManagedFlowCleanup());

    console.log(
      'payment.kycManagedFlowFetching',
      payment.kycManagedFlowFetching,
    );
    console.log('payment.kycManagedFlowLink', payment.kycManagedFlowLink);
    if (
      payment.kycManagedFlowFetching === false &&
      payment.kycManagedFlowLink !== null &&
      payment.kycManagedFlowError === null
    ) {
      Linking.openURL(payment.kycManagedFlowLink);
      // kycManagedFlowLinkClean();
    }
  }, [
    payment.kycManagedFlowFetching,
    payment.kycManagedFlowLink,
    payment.kycManagedFlowError,
    dispatch,
  ]);

  const [appState, setAppState] = useState(AppState.currentState);

  useEffect(() => {
    AppState.addEventListener('change', handleAppStateChange);

    return () => {
      AppState.removeEventListener('change', handleAppStateChange);
    };
  }, []);

  const handleAppStateChange = nextAppState => {
    console.log('handleAppStateChange', appState, nextAppState);
    if (nextAppState === 'active') {
      dispatch(PaymentActions.paymentKycStatusRequest());
    }
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      console.log('App has come to the foreground!', nextAppState);
      // Your code here
    }
    setAppState(nextAppState);
  };

  useEffect(() => {
    if (payment.updatingKyc === false) {
      let _kycMessage = '__kyc_onboard';
      let _kycButtonColor = colors.blue;
      let _kycDetails = null;
      switch (payment.userWallet?.kycStatus) {
        case KYC_VALIDATION_CREATED:
        case KYC_VALIDATION_ASKED:
          _kycMessage = '__kyc_waiting_response';
          _kycButtonColor = colors.orange;
          break;
        case KYC_VALIDATED:
          _kycMessage = '__kyc_verified';
          _kycButtonColor = colors.success;
          break;
        case KYC_VALIDATION_FAILED:
          _kycMessage = '__kyc_rejected';
          _kycButtonColor = colors.error;
          _kycDetails = payment.userWallet.kycMessage;
          break;
        default:
          _kycMessage = '__kyc_onboard';
          _kycButtonColor = colors.blue;
      }
      setKycButtonColor(_kycButtonColor);
      setKycMessage(_kycMessage);
      setKycDetails(_kycDetails);
    }
  }, [payment.updatingKyc, payment.errorKyc, payment.userWallet]);

  useFocusEffect(
    React.useCallback(() => {
      dispatch(PaymentActions.paymentKycStatusRequest());
    }, [dispatch]),
  );

  return (
    <View
      style={{
        ...globalStyles.contentCard,
        marginBottom: 30,
        opacity: disableKycView ? 0.5 : 1,
      }}>
      <Field
        label={i18n.t('__kyc_onboard')}
        content={
          <View
            style={{
              flex: 1,
              flexDirection: 'column',
            }}>
            <View
              style={{
                flex: 1,
                flexDirection: 'row',
                alignItems: 'center',
              }}>
              <Image
                style={{
                  height: icon_size_xsmall,
                  width: icon_size_xsmall,
                  marginRight: 10,
                }}
                source={
                  checkKycValidatedStatus(payment?.userWallet)
                    ? images._success
                    : images._warning
                }
              />
              <Button
                onPress={startKycManagedFlow}
                disabled={disableKyc}
                uppercase={false}
                mode="contained"
                style={{
                  minWidth: 230,
                  marginLeft: 50,
                  marginRight: 50,
                  marginBottom: -10,
                  elevation: 0,
                }}
                color={kycButtonColor}
                icon={getIcon('idcard')}>
                {i18n.t(kycMessage)}
              </Button>
            </View>
            {kycDetails !== null ? (
              <View
                style={{
                  flex: 1,
                  flexDirection: 'row',
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text
                  style={{
                    color: colors.black,
                    fontWeight: 'bold',
                    marginTop: 20,
                  }}>
                  {i18n.t(kycDetails)}
                </Text>
              </View>
            ) : (
              <></>
            )}
          </View>
        }
      />
    </View>
  );
};

export default KycManagedFlow;

// Path: screens/component/KycManagedFlow.test.js
// import React from 'react';
// import { render, fireEvent } from '@testing-library/react';
// import { Provider } from 'react-redux';
// import configureStore from 'redux-mock-store';
// import KycManagedFlow from './KycManagedFlow';

// const mockStore = configureStore();

// describe('KycManagedFlow', () => {
//   let store;
//   let component;

//   beforeEach(() => {
//     store = mockStore({
//       payment: {
//         fetchingToken: null,
//         fetchingOne: null,
//         fetchingAll: null,
//         updatingPayment: null,
//         updatingBankAccount: null,
//         updatePaymentStatus: null,
//         deleting: null,
//         paymentIntent: null,
//         eventOfferKvId: null,
//         payment: null,
//         payments: [],
//         updatingKyc: null,
//         kycDocument: null,
//         userWallet: null,
//         bankAccount: null,
//         paymentStatus: null,
//       },
//     });

//     component = render(
//       <Provider store={store}>
//         <KycManagedFlow />
//       </Provider>,
//     );
//   });

//   it('should render the component', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should call uploadKyc', () => {
//     const button = component.getByText('Upload KYC');
//     fireEvent.click(button);
//     expect(store.getActions()).toEqual([
//       { type: 'payment/uploadKyc' },
//     ]);
//   });
// });
