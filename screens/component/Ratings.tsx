import React, { Component } from 'react';
import { View, TouchableOpacity } from 'react-native';
import StarRating from 'react-native-star-rating';
import { Subheading } from 'react-native-paper';
import { colors } from '@constants/index';
import i18n from '@translations/index';
import { connect } from 'react-redux';
import { FEEDBACK_REVIEWS_ROUTE } from '@routes/route_constants';
import { FEEDBACK_STATE_TYPE } from '@reducers/feedbacks.reducer';

// const propTypes = {
//   userId: PropTypes.string,
//   navigation: PropTypes.any,
//   direction: PropTypes.string,
// };

interface RatingsProps {
  userId?: string;
  navigation: any;
  direction: string;
  feedbacks: FEEDBACK_STATE_TYPE;
}

class Ratings extends Component<RatingsProps> {
  constructor(props: RatingsProps) {
    super(props);
  }

  customStyle = () => {
    return this.props.direction === 'row'
      ? { padding: 5, paddingLeft: 20, paddingRight: 20, marginRight: 20 }
      : { padding: 5, paddingLeft: 20, marginRight: 20 };
  };

  render() {
    const { feedbackStats, reviewStats } = this.props.feedbacks;
    const avgReviewRx = reviewStats?.avgReviewRx ?? 0;
    const reviewRxTotal = reviewStats?.reviewRxTotal ?? 0;
    const avgFeedbackRx = feedbackStats?.avgFeedbackRx ?? 0;
    const feedbackRxTotal = feedbackStats?.feedbackRxTotal ?? 0;

    return (
      <TouchableOpacity
        onPress={() => this.props.navigation.navigate(FEEDBACK_REVIEWS_ROUTE)}>
        <View
          style={{
            flexDirection: this.props.direction,
          }}>
          <View style={this.customStyle()}>
            <Subheading>{`${i18n.t(
              '__reviews',
            )}: ${reviewRxTotal}`}</Subheading>
            <StarRating
              starSize={14}
              disabled
              maxStars={5}
              rating={avgReviewRx / 100}
              fullStarColor={colors.fullStar}
            />
          </View>
          <View style={{ padding: 5, paddingLeft: 20, paddingRight: 20 }}>
            <Subheading>{`${i18n.t(
              '__feedbacks',
            )}: ${feedbackRxTotal}`}</Subheading>
            <StarRating
              starSize={14}
              disabled
              maxStars={5}
              rating={avgFeedbackRx / 100}
              fullStarColor={colors.fullStar}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  }
}

const mapStateToProps = appState => {
  return {
    common: appState.common,
    feedbacks: appState.feedbacks,
  };
};
export default connect(mapStateToProps)(Ratings);
