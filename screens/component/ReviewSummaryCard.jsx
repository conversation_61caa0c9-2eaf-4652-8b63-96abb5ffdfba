import React from 'react';
import {
  Avatar,
  Caption,
  Card,
  Headline,
  Subheading,
  Surface,
} from 'react-native-paper';
import i18n from '@translations/index';
import { View, Text, StyleSheet } from 'react-native';
import ReviewSummaryButton from './ReviewSummaryButton';
import { globalStyles } from '@constants/styles';
import { ellipsis, getAvatar } from '@constants/functions';
import StarRating from 'react-native-star-rating';
import { colors } from '@constants/colors';

const ReviewSummaryCard = ({ reviews, reviewStats }) => {
  const calcTotalRating = () => {
    if (!reviewStats) {
      return <></>;
    }

    let { avgReviewRx = 0, reviewRxTotal = 0 } = reviewStats;
    if (reviewRxTotal > 0) {
      return (
        <View style={{ width: 200, ...styles.centerView }}>
          <Headline>{avgReviewRx / 100}</Headline>
          <View style={{ width: 200 }}>
            <StarRating
              disabled
              rating={avgReviewRx / 100}
              starSize={30}
              fullStarColor={colors.fullStar}
            />
          </View>
          <Caption>{`${reviewRxTotal} ${i18n.t('__reviews')}`}</Caption>
        </View>
      );
    }
  };

  return (
    <Surface style={globalStyles.elementCard}>
      <Subheading>{i18n.t('__reviews')}</Subheading>
      <View style={{ alignItems: 'center', paddingVertical: 10 }}>
        {calcTotalRating()}
      </View>
      <Card
        style={{
          elevation: 0,
          borderColor: '#ddd',
          borderWidth: 1,
          marginBottom: 10,
        }}>
        <Card.Content>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            {reviews.length > 0 ? (
              reviews.slice(0, 1).map((prop, key) => {
                return (
                  <View
                    key={key}
                    style={{ flexDirection: 'row', marginBottom: 5 }}>
                    <Avatar.Image
                      source={{ uri: getAvatar(prop.senderId) }}
                      size={50}
                    />
                    <View style={{ flex: 1, marginLeft: 10 }}>
                      <Caption
                        style={{
                          fontFamily: 'medium',
                          color: 'black',
                          lineHeight: 16,
                          fontSize: 14,
                          margin: 0,
                        }}>
                        {ellipsis(prop.description, 80)}
                      </Caption>
                      <View style={{ width: 90, paddingVertical: 5 }}>
                        <StarRating
                          starSize={14}
                          disabled
                          maxStars={5}
                          rating={prop.point / 100}
                          fullStarColor={colors.fullStar}
                        />
                      </View>
                    </View>
                  </View>
                );
              })
            ) : (
              <View
                style={{
                  flex: 1,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}>
                <Text style={{ color: colors.black }}>
                  {i18n.t('__no_reviews')}
                </Text>
              </View>
            )}
          </View>
        </Card.Content>
      </Card>
      <ReviewSummaryButton reviews={reviews} />
    </Surface>
  );
};

export default ReviewSummaryCard;

const styles = StyleSheet.create({
  centerView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
