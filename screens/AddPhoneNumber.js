import React, { Component } from 'react';
import { ScrollView, View } from 'react-native';
import { Button } from 'react-native-paper';
import {
  colors,
  _ERROR,
  _SUCCESS,
  OTP_LENGTH,
  addPhoneFormSchema,
  otpValidationFormSchema,
} from '@constants';
import { Input } from '@components';
import validate from '@helpers/validation';
import AlertDialog from '@components/AlertDialog';
import { connect } from 'react-redux';
import i18n from '@translations/index';
import AccountActions from '@reducers/account.reducer';
import { PROFILE_ROUTE } from '@routes/route_constants';

const constraints = {
  number: {
    presence: { allowEmpty: false, message: '__is_missing' },
    numericality: true,
  },
  nationality: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
};
const constraintsOtp = {
  otp: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
};
class PhoneScreen extends Component {
  constructor(props) {
    super(props);
    this.state = {
      phoneNumberButtonDisabled: true,
      otpVerifyButtonDisabled: true,
      otpHidden: true,
      navigateToProfile: false,
      form: addPhoneFormSchema,
      formValidation: otpValidationFormSchema,
      showAlertModal: false,
      waitTime: false,
      buttonText: '__send',
      country: this.props.account.account.country || '',
      prefix: '',
    };
  }

  componentDidUpdate(prevProps) {
    if (prevProps.account.fetchingPhone && !this.props.account.fetchingPhone) {
      if (this.props.account.error !== null) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__phone_save_error',
        });
      } else {
        this.setState({ otpHidden: false });
      }
    }
    if (prevProps.account.otpFetching && !this.props.account.otpFetching) {
      if (this.props.account.error !== null) {
        this.setState({
          showAlertModal: true,
          messageType: _ERROR,
          message: '__phone_verified_error',
        });
      } else {
        this.setState({
          showAlertModal: true,
          messageType: _SUCCESS,
          message: '__phone_verified',
          navigateToProfile: true,
        });
      }
    }
  }

  generateForm = (structure, change, state) => {
    const fields = Object.keys(structure).map(key => ({
      ...structure[key],
      key,
    }));

    return fields.map(field => {
      let value = field.value;
      if (field.key === 'nationality') {
        value = this.state.country;
      }
      return (
        <Input {...field} _key={field.key} value={value} change={change} />
      );
    });
  };

  serverRequest = async () => {
    let obj = {
      country: this.state.country,
      nationality: this.state.form.nationality.value,
      prefix: this.state.prefix,
      number: this.state.form.number.value,
    };

    const errors = Object.values(validate(obj, constraints) || {});

    if (errors.length > 0) {
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: errors[0][0],
      });
      return;
    }

    await this.props.savePhoneNumber({
      prefix: obj.prefix,
      number: obj.number,
      nationality: obj.nationality,
      country: obj.country,
    });
    this.setState({ waitTime: true, buttonText: '__sending_30s' });
    setTimeout(
      () => this.setState({ waitTime: false, buttonText: '__send' }),
      30000,
    );
  };

  phoneVerification = () => {
    let obj = {
      otp: this.state.formValidation.otp.value,
    };

    const errors = Object.values(validate(obj, constraintsOtp) || {});
    if (errors.length > 0) {
      console.log('err...', errors[0][0]);
      this.setState({
        showAlertModal: true,
        messageType: _ERROR,
        message: errors[0][0],
      });
      return;
    }

    this.props.verifyPhoneNumber(obj);
  };

  changeHandler = (text, field) => {
    const { form, country, prefix } = this.state;
    let nextCountry = country;
    let nextPrefix = prefix;
    if (field === 'nationality') {
      form[field].value = `${text.cca2} ${text.callingCode[0]}`;
      nextCountry = text.cca2;
      nextPrefix = `+${text.callingCode[0]}`;
    } else {
      form[field].value = text;
    }

    const isDisabled =
      text && text !== null && text.length > OTP_LENGTH ? false : true;

    this.setState({
      form,
      phoneNumberButtonDisabled: isDisabled,
      country: nextCountry,
      prefix: nextPrefix,
    });
  };

  changeHandlerOtp = (text, field) => {
    const { formValidation } = this.state;
    formValidation[field].value = text;
    const isDisabled =
      text && text !== null && text.length >= OTP_LENGTH ? false : true;

    this.setState({ formValidation, otpVerifyButtonDisabled: isDisabled });
  };

  render() {
    const {
      form,
      showAlertModal,
      phoneNumberButtonDisabled,
      message,
      messageType,
      formValidation,
      otpVerifyButtonDisabled,
      waitTime,
      buttonText,
      navigateToProfile,
    } = this.state;

    const { fetchingPhone, otpFetching } = this.props.account;

    return (
      <ScrollView style={{ padding: 20, backgroundColor: colors.background }}>
        {this.generateForm(form, this.changeHandler)}
        <Button
          mode="contained"
          style={{ marginBottom: 40 }}
          contentStyle={{ padding: 5 }}
          loading={fetchingPhone}
          disabled={phoneNumberButtonDisabled || waitTime || fetchingPhone}
          onPress={this.serverRequest}>
          {i18n.t(buttonText)}
        </Button>
        <AlertDialog
          onClose={() => {
            this.setState({ showAlertModal: false, navigateToProfile: false });
            if (navigateToProfile) {
              this.props.navigation.navigate(PROFILE_ROUTE);
            }
          }}
          alert={{
            visible: showAlertModal,
            message: message,
            messageType: messageType,
          }}
        />
        <View>
          {this.generateForm(formValidation, this.changeHandlerOtp)}
          <Button
            mode="contained"
            style={{ marginBottom: 40 }}
            contentStyle={{ padding: 5 }}
            loading={otpFetching}
            disabled={otpVerifyButtonDisabled || otpFetching}
            onPress={this.phoneVerification}>
            {otpFetching ? i18n.t('__verifying') : i18n.t('__verify')}
          </Button>
        </View>
      </ScrollView>
    );
  }
}
const mapStateToProps = appState => {
  return {
    account: appState.account,
  };
};

function mapDispatchToProps(dispatch) {
  return {
    savePhoneNumber: phone => dispatch(AccountActions.phoneRequest(phone)),
    verifyPhoneNumber: otpCode => dispatch(AccountActions.otpRequest(otpCode)),
  };
}
export const AddPhoneNumber = connect(
  mapStateToProps,
  mapDispatchToProps,
)(PhoneScreen);
