{"bankAccount": null, "cardId": null, "deleting": null, "errorAll": null, "errorDeleting": null, "errorKyc": null, "errorOne": null, "errorPaymentStatus": null, "errorSecure": null, "errorToken": {"detail": "400 BAD_REQUEST, ProblemDetailWithCause[type='https://www.jhipster.tech/problem/problem-with-message', title='__state_invalid', status=400, detail='null', instance='null', properties='{message=error.__state_invalid, params=entity_name}']", "instance": "/api/payments/intent/ac6b8aa6-3fd0-447a-9d32-61b6e625489c", "message": "error.__state_invalid", "params": "entity_name", "path": "/api/payments/intent/ac6b8aa6-3fd0-447a-9d32-61b6e625489c", "status": 400, "title": "__state_invalid", "type": "https://www.jhipster.tech/problem/problem-with-message"}, "errorUpdating": null, "errorUpdatingBankAccount": null, "eventOfferKvId": "c7de73d0-8be5-45e1-9854-3088139f227f", "fetchingAll": null, "fetchingOne": null, "fetchingToken": false, "kycDocument": null, "payment": null, "paymentIntent": {"customerId": "cus_P1WfKZqepCjOid", "ephemeralKey": "ek_test_YWNjdF8xRFowMm1LMEd0Z3ViQ245LHF2Y0V3SWVYQXFDQ3lKOW42T1ExajlDTE5RUE90RXA_007dQ8XYrQ", "eventId": "e837cd48-caa1-4aec-8390-b3a9c8aefcce", "eventOfferKvId": "c7de73d0-8be5-45e1-9854-3088139f227f", "paymentIntent": "pi_3P3hthK0GtgubCn910rsp509_secret_vQ6MbTrcvxFIapO9D48BXLyjg", "pubKey": "pk_test_pA0GVzkinxHbxZtSr7LgwxcC"}, "paymentStatus": null, "payments": [], "secureRequest": false, "secureStatus": null, "updatePaymentStatus": null, "updatingBankAccount": null, "updatingKyc": false, "updatingPayment": null, "userWallet": {"cardId": null, "iban": null, "ibanId": null, "kyc": null, "kycMessage": null, "kycStatus": null, "userId": "auth0|652eb51e0d34d1e5c05ab53d", "userPaymentId": null, "walletId": null}}