import { createStore, applyMiddleware, combineReducers } from 'redux';
import promiseMiddleware from 'redux-promise';
import createSagaMiddleware from 'redux-saga';
import rootSaga from '@sagas/index';

import immutablePersistenceTransform from './immutable-persistence-transform';
import { composeWithDevTools } from 'redux-devtools-extension';

import AsyncStorage from '@react-native-async-storage/async-storage';
import { persistStore, persistReducer } from 'redux-persist';
import hardSet from 'redux-persist/lib/stateReconciler/hardSet';

const persistConfig = {
  key: 'primary',
  version: 0,
  storage: AsyncStorage,
  transforms: [immutablePersistenceTransform],
  stateReconciler: hardSet,
  // debug: true
};

import {reducer as SearchReducer} from '@reducers/search.reducer'
import {reducer as RegisterReducer} from '@reducers/register.reducer'
import {reducer as AccountReducer} from '@reducers/account.reducer'

const rootReducer = combineReducers(
  {
    searchedEvents: SearchReducer,
    events: require('@reducers/events.reducer').reducer,
    messages: require('@reducers/message.reducer').reducer,
    feedbacks: require('@reducers/feedbacks.reducer').reducer,
    common: require('@reducers/common.reducer').reducer,
    account: AccountReducer,
    authorization: require('@reducers/login.reducer').reducer,
    register: RegisterReducer,
    eventkv: require('@reducers/eventKv.reducer').reducer,
    forgotPassword: require('@reducers/forgot-password.reducer').reducer,
    payment: require('@reducers/payment.reducer').reducer,
  },
  //TODO to debug redux
  window.__REDUX_DEVTOOLS_EXTENSION__ && window.__REDUX_DEVTOOLS_EXTENSION__(),
);

const persistedReducer = persistReducer(persistConfig, rootReducer);

const sagaMiddleware = createSagaMiddleware();
const debugger1 = createDebugger();

const configureStore = () => {
  if (__DEV__) {
    console.log('I am in debug');
  }

  const store = createStore(
    persistedReducer,
    __DEV__
      ? composeWithDevTools(
          applyMiddleware(promiseMiddleware, sagaMiddleware, debugger1),
        )
      : applyMiddleware(promiseMiddleware, sagaMiddleware),
  );
  sagaMiddleware.run(rootSaga);
  const persistor = persistStore(store);
  return { store, persistor };
};

const { store, persistor } = configureStore();

export { store, persistor };
