import { appConfig, BASE_AI_URL } from '@config/app-config';
import apisauce from 'apisauce';
import Axios from 'axios';
import { setupCache } from 'axios-cache-interceptor';

const DEFAULT_TIMEOUT = 10000; //10 sec
const LONG_TIMEOUT = 60000; //10 sec
const urlUploadEvent = `${appConfig.apiUrl}${BASE_AI_URL}api/uploadFileAzure`;
const FIRST_PAGE = 0;
const PAGE_SIZE = 20;

const create = () => {
  const instance = Axios.create();
  const axios = setupCache(instance, {
    headerInterpreter: (headers, type) => {
      return {
        ...headers,
        'Cache-Control': 'no-cache',
      };
    },
  });

  const algoliaClient = apisauce.create({
    baseURL: `https://${appConfig.algoliaAppId}-dsn.algolia.net/1/indexes/${appConfig.algoliaSearchIndex}`,
    headers: {
      'X-Algolia-API-Key': appConfig.algoliaSearchKey,
      'X-Algolia-Application-Id': appConfig.algoliaAppId,
    },
    timeout: LONG_TIMEOUT,
  });

  const api = apisauce.create({
    // base URL is read from the "constructor"
    baseURL: appConfig.apiUrl,
    // here are some default headers
    headers: {
      'Cache-Control': 'no-cache',
    },
    timeout: DEFAULT_TIMEOUT,
  });

  const getNickname = login =>
    axios.get(`${appConfig.apiUrl}api/user/nickname/${login}`);

  const logout = () => api.post('auth/logout');

  const registerDeviceToken = deviceToken =>
    api.post('api/account/device-id', deviceToken, {
      headers: {
        'Content-Type': 'text/plain',
        Accept: 'application/json, text/plain, */*',
      },
    });
  const registerSocialLogin = data => {
    return api.post('api/register-social', data, {
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });
  };
  const isAccountExist = ({ email, token }) => {
    api.setHeader('Authorization', `Bearer ${token}`);
    return api.get('api/user-exist?email=' + email);
  };
  const getAccount = () => api.get('api/account');
  const updateAccount = account =>
    api.post('api/account', account, {
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });
  const getAccountPreferences = () => api.get('api/user-preferences');
  const updateAccountPreferences = preferences =>
    api.put('api/user-preferences', preferences);
  const addAccountPreferences = preferences =>
    api.post('api/user-preferences', preferences);
  const searchEventOffersLegacy = (
    query,
    page = FIRST_PAGE,
    size = PAGE_SIZE,
  ) =>
    api.post(
      BASE_AI_URL + `api/_search/event-offers/legacy?page=${page}&size=${size}`,
      query,
    );
  // const searchAlgoliaEventOffer = query => algoliaEventOfferIndex.search(query);
  const searchAlgoliaEventOfferNative = data =>
    algoliaClient.post('/query', JSON.stringify(data));

  const searchEventOffers = (query, page = FIRST_PAGE, size = PAGE_SIZE) =>
    api.get(
      BASE_AI_URL +
        `api/_search/event-offers?query=${query}&page=${page}&size=${size}`,
    );

  const savePhoneNumber = phoneNationObject =>
    api.post('api/account/phone', phoneNationObject);
  const verifyOtp = otp => api.post('api/account/verify-phone', otp);
  const userInfo = userInfoId => api.get('api/user-info/' + userInfoId);

  const getEventOffer = eventId =>
    api.get(BASE_AI_URL + 'api/event-offers/' + eventId);
  const getEventOffers = options =>
    api.get(BASE_AI_URL + 'api/event-offers' + options);
  const createEventOffer = eventOffer =>
    api.post(BASE_AI_URL + 'api/event-offers', eventOffer);
  const updateEventOffer = eventOffer =>
    api.put(BASE_AI_URL + `api/event-offers/${eventOffer.id}`, eventOffer);
  const deleteEventOffer = eventId =>
    api.delete(BASE_AI_URL + 'api/event-offers/' + eventId);

  const getEventKVSRxRequest = options =>
    api.get(BASE_AI_URL + 'api/all-requests-to-your-event', options);
  const getEventKVSTxRequest = ({ page = FIRST_PAGE, ...rest }) =>
    api.get(BASE_AI_URL + `api/all-offer-requests?page=${page}`, rest);
  const getEventKVSById = eventId =>
    api.get(BASE_AI_URL + 'api/event-offer-request/' + eventId);
  const getEventEditById = eventId =>
    api.get(BASE_AI_URL + 'api/event-offer-edit/' + eventId);
  const createEventKV = eventKv =>
    api.post(BASE_AI_URL + 'api/event-offer-participate', eventKv);
  const updateEventKV = eventKv =>
    api.put(BASE_AI_URL + 'api/event-offer-participate-change-state', eventKv);
  const updateEventRefund = eventKv =>
    api.post(BASE_AI_URL + 'api/event-offer-refund-change-state', eventKv);
  const deleteEventKV = eventKvId =>
    api.delete(BASE_AI_URL + 'api/event-offer-kv/' + eventKvId);

  const getMessage = messageId =>
    api.get(BASE_AI_URL + 'api/messages/' + messageId);
  const createMessage = message =>
    api.post(BASE_AI_URL + 'api/messages', message);
  const updateMessage = message =>
    api.put(BASE_AI_URL + 'api/messages', message);
  const deleteMessage = messageId =>
    api.delete(BASE_AI_URL + 'api/messages/' + messageId);
  const getMessagesByThread = threadId =>
    api.get(BASE_AI_URL + 'api/messages-thread/' + threadId);
  const markMessageRead = threadId =>
    api.put(BASE_AI_URL + 'api/message/' + threadId);

  const getConfiguration = () => api.get(BASE_AI_URL + 'api/configuration');

  const getUserFeedbacks = userId =>
    api.get(BASE_AI_URL + 'api/feedbacks-user/' + userId);
  const getUserReviews = userId =>
    api.get(BASE_AI_URL + 'api/reviews-user/' + userId);
  const createFeedback = feedback =>
    api.post(BASE_AI_URL + 'api/feedbacks', feedback);
  const createReview = review => api.post(BASE_AI_URL + 'api/reviews', review);
  const updateFeedback = feedback =>
    api.put(BASE_AI_URL + 'api/feedbacks', feedback);
  const updateReview = review => api.put(BASE_AI_URL + 'api/reviews', review);
  const deleteFeedback = feedbackId =>
    api.delete(BASE_AI_URL + 'api/feedbacks/' + feedbackId);

  const getEventsByTag = tagId => api.get(BASE_AI_URL + 'api/tags/' + tagId);
  const getFeedbacksStas = userId =>
    api.get(BASE_AI_URL + 'api/feedback-stats/' + userId);
  const getReviewsStas = userId =>
    api.get(BASE_AI_URL + 'api/review-stats/' + userId);

  const getMessages = options =>
    api.get(BASE_AI_URL + 'api/messages-threads', options);

  const uploadImageEvent = (formData, onUploadProgress) => {
    console.log('uploadImageEvent payload', formData);
    console.log('url is ', urlUploadEvent);
    console.log('url formData ', formData);
    console.log('url api.headers ', api.headers);

    return fetch(urlUploadEvent, {
      body: formData,
      method: 'POST',
      headers: {
        Accept: 'application/json',
        'Content-Type': 'multipart/form-data',
        Authorization: api.headers.Authorization,
      },
    });
  };
  const uploadImageGeneric = (image, onUploadProgress) =>
    api.post(BASE_AI_URL + 'api/uploadFileAzure', image, {
      headers: { 'Content-Type': 'multipart/form-data' },
      onUploadProgress,
    });
  const uploadImageProfile = image =>
    api.post('api/uploadFileAzure', image, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });

  const getStripePaymentIntent = eventofferkvid =>
    api.get(BASE_AI_URL + `api/payments/intent/${eventofferkvid}`);

  const getUserWallet = () => api.get(BASE_AI_URL + 'api/user-wallet');
  const uploadKycDocument = data =>
    api.post(BASE_AI_URL + 'api/kyc-documents', data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
  const initKycManagedFlow = () => api.get(BASE_AI_URL + 'api/kyc-init');

  const updateBankAccount = ibanData => {
    const bankAccountData = new FormData();
    bankAccountData.append('iban', ibanData.iban);
    bankAccountData.append('country', ibanData.country);
    return api.post(BASE_AI_URL + 'api/bankaccount', bankAccountData);
  };
  const getPaymentStatus = paymentId =>
    api.get(BASE_AI_URL + 'api/payments/' + paymentId);

  const deleteAccount = () => api.delete(appConfig.uaaBaseUrl + 'api/user');
  const updateUsersBlock = users => api.put('api/users-blocked', users);

  const reportEventOffer = data =>
    api.post(BASE_AI_URL + 'api/event/report', data);

  const setAuthToken = authToken => {
    api.setHeader('Authorization', `Bearer ${authToken}`);
  };

  return {
    logout,
    searchEventOffers,
    searchEventOffersLegacy,

    // Algolia
    searchAlgoliaEventOfferNative,
    createEventOffer,
    updateEventOffer,
    getAccount,
    uploadImageEvent,
    updateAccount,
    getEventOffer,
    getEventOffers,
    deleteEventOffer,
    registerDeviceToken,
    getAccountPreferences,
    updateAccountPreferences,
    addAccountPreferences,
    registerSocialLogin,
    savePhoneNumber,
    verifyOtp,
    userInfo,
    uploadImageProfile,
    isAccountExist,
    getConfiguration,

    getEventKVSRxRequest,
    getEventKVSTxRequest,
    getEventKVSById,
    getEventEditById,
    updateEventKV,
    updateEventRefund,
    createEventKV,
    deleteEventKV,

    // createPayment,
    // updatePayment,
    // getClientToken,
    // createPaypalPayment,

    getMessage,
    getMessages,
    createMessage,
    updateMessage,
    deleteMessage,
    // searchMessages,
    getMessagesByThread,
    markMessageRead,

    getUserFeedbacks,
    getUserReviews,
    updateFeedback,
    createFeedback,
    createReview,
    updateReview,
    getFeedbacksStas,
    getReviewsStas,
    getEventsByTag,

    // Stripe
    getStripePaymentIntent,
    initKycManagedFlow,

    getUserWallet,
    uploadKycDocument,
    updateBankAccount,
    getPaymentStatus,

    uploadImageGeneric,
    deleteAccount,
    updateUsersBlock,
    reportEventOffer,

    getNickname,

    setAuthToken,
  };
};

export default {
  create,
};
