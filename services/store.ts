import { combineReducers, Store, Reducer, CombinedState, Action } from 'redux';
import { configureStore } from '@reduxjs/toolkit';
import promiseMiddleware from 'redux-promise';
import createSagaMiddleware from 'redux-saga';
import rootSaga from '@sagas/index';

import immutablePersistenceTransform from './immutable-persistence-transform';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { persistStore, persistReducer, Persistor } from 'redux-persist';
import hardSet from 'redux-persist/lib/stateReconciler/hardSet';

import {
  reducer as searchedEventsReducer,
  SEARCH_STATE_TYPE,
} from '@reducers/search.reducer';
import {
  reducer as eventsReducer,
  EVENT_STATE_TYPE,
} from '@reducers/events.reducer';
import {
  reducer as messageReducer,
  INITIAL_STATE as MESSAGE_INITIAL_STATE,
} from '@reducers/message.reducer';
import {
  reducer as feedbacksReducer,
  INITIAL_STATE as FEEDBACK_INITIAL_STATE,
} from '@reducers/feedbacks.reducer';
import {
  reducer as commonReducer,
  INITIAL_STATE as COMMON_INITIAL_STATE,
} from '@reducers/common.reducer';
import {
  reducer as accountReducer,
  ACCOUNT_STATE_TYPE,
} from '@reducers/account.reducer';
import {
  reducer as authorizationReducer,
  INITIAL_STATE as AUTH_INITIAL_STATE,
} from '@reducers/login.reducer';
import {
  reducer as eventkvReducer,
  INITIAL_STATE as EVENTKS_INITIAL_STATE,
} from '@reducers/eventKv.reducer';
import {
  reducer as paymentReducer,
  INITIAL_STATE as PAYMENT_INITIAL_STATE,
} from '@reducers/payment.reducer';

const persistConfig = {
  key: 'primary',
  version: 0,
  storage: AsyncStorage,
  transforms: [immutablePersistenceTransform],
  stateReconciler: hardSet,
  // debug: true
};

export interface RootState {
  searchedEvents: SEARCH_STATE_TYPE;
  events: EVENT_STATE_TYPE;
  messages: typeof MESSAGE_INITIAL_STATE;
  feedbacks: typeof FEEDBACK_INITIAL_STATE;
  common: typeof COMMON_INITIAL_STATE;
  account: ACCOUNT_STATE_TYPE;
  authorization: typeof AUTH_INITIAL_STATE;
  eventkv: typeof EVENTKS_INITIAL_STATE;
  payment: typeof PAYMENT_INITIAL_STATE;
}

const rootReducer: Reducer<
  CombinedState<RootState>,
  Action<any>
> = combineReducers<RootState>({
  searchedEvents: searchedEventsReducer,
  events: eventsReducer,
  messages: messageReducer,
  feedbacks: feedbacksReducer,
  common: commonReducer,
  account: accountReducer,
  authorization: authorizationReducer,
  eventkv: eventkvReducer,
  payment: paymentReducer,
});

const sagaMiddleware = createSagaMiddleware();

interface ConfigureStoreResult {
  store: Store;
  persistor: Persistor;
}
const middlewares = [sagaMiddleware, promiseMiddleware];

const configureCustomStore = (): ConfigureStoreResult => {
  if (__DEV__) {
    console.log('I am in debug');
    const createDebugger = require('redux-flipper').default;
    middlewares.push(createDebugger());
  }

  const store = configureStore({
    reducer: persistReducer(persistConfig, rootReducer),
    middleware: getDefaultMiddleware =>
      getDefaultMiddleware({ thunk: false }).concat(middlewares),
    devTools: __DEV__,
  });

  sagaMiddleware.run(rootSaga);
  const persistor = persistStore(store);
  return { store, persistor };
};

const { store, persistor } = configureCustomStore();

export { store, persistor };
