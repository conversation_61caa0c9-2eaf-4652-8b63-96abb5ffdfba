export interface IUserBlock {
  login: string;
  time: Date;
}
export interface IAccount {
  id: number;
  login: string;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl: string;
  activated: boolean;
  verified: string;
  langKey: string;
  // createdBy: 'system',
  // createdDate: null,
  // lastModifiedBy: string
  // lastModifiedDate: '2022-01-11T12:39:38Z',
  // authorities: [
  //   'ROLE_USER',
  //   'ROLE_USER_SOCIAL_FB'
  // ],
  phoneNumber: string;
  paymentUserId: string;
  deviceId: string | null;
  dob: Date;
  country: string | null;
  city: string | null;
  address: string | null;
  postcode: string | null;
  region: string | null;
  userBlocked: IUserBlock[];
}
