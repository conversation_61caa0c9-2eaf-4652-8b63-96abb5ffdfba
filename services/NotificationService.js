import React, { useState, useEffect, useRef } from 'react';
import PushNotification from 'react-native-push-notification';
import { appConfig } from '@config/app-config';
import i18n from '@translations/index';
import Config from 'react-native-config';

// import PushNotificationIOS from '@react-native-community/push-notification-ios'

const useNotificationService = ({ onRegister, onNotification }) => {
  const onRegisterRef = useRef(null);
  const onNotificationRef = useRef(null);

  const attachRegister = handler => {
    onRegisterRef.current = handler;
  };

  const attachNotification = handler => {
    onNotificationRef.current = handler;
  };

  const onNotificationAction = notification => {
    console.log('onNotification:', notification);
    if (typeof onNotificationRef.current === 'function') {
      onNotificationRef.current(notification);
    } else {
      console.warn(
        'current Handler this. POSTPONED 1 second',
        onNotificationRef.current,
      );
      setTimeout(() => onNotification(notification), 1000);
    }
  };

  const onRegisterAction = token => {
    if (typeof onRegisterRef.current === 'function') {
      onRegisterRef.current(token);
    }
  };

  const onAction = notification => {
    console.log('ACTION:', notification.action);
    console.log('NOTIFICATION:', notification);

    // TODO: it must match all the __show translations
    if (notification.action === 'Show' || notification.action === 'Mostra') {
      PushNotification.invokeApp(notification);
    }
  };

  const [lastId, setLastId] = useState(0);
  useEffect(() => {
    createDefaultChannels();

    requestPermissions();
    checkPermission(p => {
      console.log('checkPermission callback', p);
      if (p && !p.alert) {
        requestPermissions();
      }
    });
    attachRegister(onRegister);
    attachNotification(onNotification);

    PushNotification.configure({
      onRegister: onRegisterAction,
      onNotification: onNotificationAction,
      onAction: onAction,
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },
      popInitialNotification: true,
      requestPermissions: true,
    });
  }, []);

  // Clear badge number at start
  PushNotification.getApplicationIconBadgeNumber(number => {
    if (number > 0) {
      PushNotification.setApplicationIconBadgeNumber(0);
    }
  });

  PushNotification.getChannels(channel_ids => {
    console.log('channel:', channel_ids); // ['channel_id_1']
  });

  PushNotification.channelBlocked(
    appConfig.notificationDefaultChannelId,
    blocked => {
      console.log('channel blocked', blocked); // true/false
    },
  );

  PushNotification.popInitialNotification(notification => {
    console.log('Initial Notification', notification);
  });

  // PushNotification.cancelAllLocalNotifications()
  // PushNotification.removeAllDeliveredNotifications()

  const createDefaultChannels = () => {
    PushNotification.createChannel(
      {
        channelId: appConfig.notificationDefaultChannelId, // (required)
        channelName: appConfig.notificationDefaultChannelName, // (required)
        channelDescription: 'A channel to categorise your notifications', // (optional) default: undefined.
        soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
        importance: 4, // (optional) default: 4. Int value of the Android notification importance
        vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
      },
      created => console.log(`createChannel returned '${created}'`), // (optional) callback returns whether the channel was created, false means it already existed.
    );
  };

  const localNotification = (data, path) => {
    setLastId(lastId + 1);
    const picUri = Config.ASSETS_BASE_PROFILE + data.userTxAvatar;
    const title = data.title || 'No Title';

    const message = data.data.message || 'No Message';

    console.log('localNotif title ', title, ' message ', message);
    console.log('localNotif picUri ', picUri);

    // if (Platform.OS === "android") {
    PushNotification.localNotification({
      /* Android Only Properties */
      channelId: appConfig.notificationDefaultChannelId, // (required) channelId, if the channel doesn't exist, it will be created with options passed above (importance, vibration, sound). Once the channel is created, the channel will not be update. Make sure your channelId is different if you change these options. If you have created a custom channel, it will apply options of the channel.
      ticker: 'My Notification Ticker', // (optional)
      showWhen: true, // (optional) default: true
      autoCancel: true, // (optional) default: true
      largeIcon: 'ic_launcher', // (optional) default: "ic_launcher". Use "" for no large icon.
      largeIconUrl: picUri, //"https://www.example.tld/picture.jpg", // (optional) default: undefined
      smallIcon: 'ic_launcher', // (optional) default: "ic_notification" with fallback for "ic_launcher". Use "" for default small icon.
      bigText: message, // "My big text that will be shown when notification is expanded", // (optional) default: "message" prop
      subText: title, //"This is a subText", //data.data.title, // "This is a subText", // (optional) default: none
      // bigPictureUrl: bigPic, //"https://www.example.tld/picture.jpg", // (optional) default: undefined
      color: 'red', // (optional) default: system default
      vibrate: true, // (optional) default: true
      vibration: 300, // vibration length in milliseconds, ignored if vibrate=false, default: 1000
      tag: 'some_tag', // (optional) add tag to message
      group: 'group', // (optional) add group to message
      groupSummary: false, // (optional) set this notification to be the group summary for a group of notifications, default: false
      ongoing: false, // (optional) set whether this is an "ongoing" notification
      priority: 'high', // (optional) set notification priority, default: high
      visibility: 'private', // (optional) set notification visibility, default: private
      ignoreInForeground: false, // (optional) if true, the notification will not be visible when the app is in the foreground (useful for parity with how iOS notifications appear)
      shortcutId: 'shortcut-id', // (optional) If this notification is duplicative of a Launcher shortcut, sets the id of the shortcut, in case the Launcher wants to hide the shortcut, default undefined
      onlyAlertOnce: false, // (optional) alert will open only once with sound and notify, default: false

      when: null, // (optional) Add a timestamp pertaining to the notification (usually the time the event occurred). For apps targeting Build.VERSION_CODES.N and above, this time is not shown anymore by default and must be opted into by using `showWhen`, default: null.
      usesChronometer: false, // (optional) Show the `when` field as a stopwatch. Instead of presenting `when` as a timestamp, the notification will show an automatically updating display of the minutes and seconds since when. Useful when showing an elapsed time (like an ongoing phone call), default: false.
      timeoutAfter: null, // (optional) Specifies a duration in milliseconds after which this notification should be canceled, if it is not already canceled, default: null

      messageId: 'google:message_id', // (optional) added as `message_id` to intent extras so opening push notification can find data stored by @react-native-firebase/messaging module.

      actions: [i18n.t('__show'), i18n.t('__close')], //["Show", "Close"], // (Android only) See the doc for notification actions to know more
      invokeApp: false, // (optional) This enable click on actions to bring back the application to foreground or stay in background, default: true

      /* iOS only properties */
      alertAction: 'view', // (optional) default: view
      category: '', // (optional) default: empty string

      /* iOS and Android properties */
      id: this.lastId, // (optional) Valid unique 32 bit integer specified as string. default: Autogenerated Unique ID
      title: title, //"My Notification Title", //data.data.title, //"My Notification Title", // (optional)
      message: message, //"My Notification Message", //data.data.message, // "My Notification Message", // (required)
      userInfo: {
        data, // (optional) default: {} (using null throws a JSON value '<null>' error)
      },
      playSound: false, // (optional) default: true
      soundName: 'default', // (optional) Sound to play when the notification is shown. Value of 'default' plays the default sound. It can be set to a custom sound such as 'android.resource://com.xyz/raw/my_sound'. It will look for the 'my_sound' audio file in 'res/raw' directory and play it. default: 'default' (default sound is played)
      number: 10, // (optional) Valid 32 bit integer specified as string. default: none (Cannot be zero)
      // repeatType: "day", // (optional) Repeating interval. Check 'Repeating Notifications' section for more info.
    });
  };

  const scheduleNotification = (soundName = true) => {
    setLastId(lastId + 1);
    PushNotification.localNotificationSchedule({
      date: new Date(Date.now() + 30 * 1000), // in 30 secs

      /* Android Only Properties */
      id: this.lastId, // (optional) Valid unique 32 bit integer specified as string. default: Autogenerated Unique ID
      ticker: 'My Notification Ticker', // (optional)
      autoCancel: true, // (optional) default: true
      largeIcon: 'ic_launcher', // (optional) default: "ic_launcher"
      smallIcon: 'ic_notification', // (optional) default: "ic_notification" with fallback for "ic_launcher"
      bigText: 'My big text that will be shown when notification is expanded', // (optional) default: "message" prop
      subText: 'This is a subText', // (optional) default: none
      color: 'blue', // (optional) default: system default
      vibrate: true, // (optional) default: true
      vibration: 300, // vibration length in milliseconds, ignored if vibrate=false, default: 1000
      tag: 'some_tag', // (optional) add tag to message
      group: 'group', // (optional) add group to message
      ongoing: false, // (optional) set whether this is an "ongoing" notification

      /* iOS only properties */
      alertAction: 'view', // (optional) default: view
      category: '', // (optional) default: empty string
      userInfo: {}, // (optional) default: {} (using null throws a JSON value '<null>' error)

      /* iOS and Android properties */
      title: 'Scheduled Notification', // (optional)
      message: 'My Notification Message', // (required)
      playSound: !!soundName, // (optional) default: true
      number: 10, // (optional) Valid 32 bit integer specified as string. default: none (Cannot be zero)
      soundName: soundName ? soundName : 'default', // (optional) Sound to play when the notification is shown. Value of 'default' plays the default sound. It can be set to a custom sound such as 'android.resource://com.xyz/raw/my_sound'. It will look for the 'my_sound' audio file in 'res/raw' directory and play it. default: 'default' (default sound is played)
    });
  };

  const checkPermission = cbk => {
    return PushNotification.checkPermissions(cbk);
  };

  const requestPermissions = () => {
    return PushNotification.requestPermissions();
  };

  const cancelNotif = () => {
    PushNotification.cancelLocalNotification({ id: '' + this.lastId });
  };

  const cancelAll = () => {
    PushNotification.cancelAllLocalNotifications();
  };

  const abandonPermissions = () => {
    PushNotification.abandonPermissions();
  };

  return {
    localNotification,
    scheduleNotification,
    requestPermissions,
  };
};

export default useNotificationService;
