// import { useEffect, useRef } from 'react';
// import PushNotification from 'react-native-push-notification';

// const useNotificationHandler = () => {
//   const onRegisterRef = useRef(null);
//   const onNotificationRef = useRef(null);

//   const onNotification = notification => {
//     console.log('onNotification:', notification);
//     if (typeof onNotificationRef.current === 'function') {
//       onNotificationRef.current(notification);
//     } else {
//       console.warn(
//         'current Handler this. POSTPONED 1 second',
//         onNotificationRef.current,
//       );
//       setTimeout(() => onNotification(notification), 1000);
//     }
//   };

//   const onRegister = token => {
//     if (typeof onRegisterRef.current === 'function') {
//       onRegisterRef.current(token);
//     }
//   };

//   const attachRegister = handler => {
//     onRegisterRef.current = handler;
//   };

//   const attachNotification = handler => {
//     onNotificationRef.current = handler;
//   };

//   const onAction = notification => {
//     console.log('ACTION:', notification.action);
//     console.log('NOTIFICATION:', notification);

//     // TODO: it must match all the __show translations
//     if (notification.action === 'Show' || notification.action === 'Mostra') {
//       PushNotification.invokeApp(notification);
//     }
//   };

//   useEffect(() => {
//     PushNotification.configure({
//       onRegister: onRegister,
//       onNotification: onNotification,
//       onAction: onAction,
//       permissions: {
//         alert: true,
//         badge: true,
//         sound: true,
//       },
//       popInitialNotification: true,
//       requestPermissions: true,
//     });
//   }, []);

//   return { attachRegister, attachNotification };
// };

// export default useNotificationHandler;
