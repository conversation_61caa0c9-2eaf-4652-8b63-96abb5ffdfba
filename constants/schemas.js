import {
  DEFAULT_LAT_LON,
  INPUT_TYPE_DATE,
  INPUT_TYPE_IMAGE,
  INPUT_TYPE_MULTIIMAGE,
  INPUT_TYPE_MULTIPLE,
  INPUT_TYPE_PICKER,
  INPUT_TYPE_PICKER_COUNTRY,
  INPUT_TYPE_SWITCHBISTATE,
  INPUT_TYPE_TAGTEXT,
  INPUT_TYPE_TAGTEXTMULTILINE,
  INPUT_TYPE_TEXTAREA,
  INPUT_TYPE_UISTEPPER,
  MAX_DURATION_MIN,
  MAX_PEOPLE,
  MAX_PRICE,
  MIN_PRICE,
  PARAMETER_PREFERENCE_USER_GENDER,
} from './constants';
import { locations } from './locations';
import { typeBeverage } from './typeBeverage';
import { typeEvents } from './typeE';
import { typeKitchen } from './typeKitchen';
import moment from 'moment';
import { intollerances } from './intollerances';
import { images } from './images';
import Config from 'react-native-config';

const gender = [
  {
    label: '__male',
    value: '__male',
  },
  {
    label: '__female',
    value: '__female',
  },
  {
    label: '__prefer_not_to_say',
    value: '__prefer_not_to_say',
  },
];

export const editProfileFormSchema = {
  nickname: {
    label: '__nickname',
  },
  firstName: {
    label: '__first_name',
  },
  lastName: {
    label: '__last_name',
  },
  dob: {
    label: '__dob',
    type: INPUT_TYPE_DATE,
    optional: false,
    format: 'DD MMMM YYYY',
    value: moment().subtract(18, 'years'),
  },
  address: {
    label: '__address',
    maxLength: 255,
    value: '',
    default: '',
  },
  city: {
    label: '__city',
    maxLength: 255,
    value: '',
    default: '',
  },
  country: {
    label: '__country',
    value: '',
    type: INPUT_TYPE_PICKER_COUNTRY,
  },
  postcode: {
    label: '__postal_code',
    maxLength: 255,
    value: '',
    default: '',
  },
  gender: {
    label: '__gender',
    type: INPUT_TYPE_PICKER,
    items: gender,
  },
  description: {
    label: '__description',
    textarea: true,
    type: INPUT_TYPE_TEXTAREA,
    optional: true,
  },
  intollerances: {
    label: '__food_intol',
    type: INPUT_TYPE_MULTIPLE,
    hideSearch: false,
    showCancelButton: false,
    items: intollerances,
    singleChoice: false,
    optional: true,
  },
};

export const otpValidationFormSchema = {
  otp: {
    label: '__otp',
    value: '',
  },
};

export const addPhoneFormSchema = {
  nationality: {
    label: '__nationality',
    type: INPUT_TYPE_PICKER_COUNTRY,
    value: '',
  },
  number: {
    label: '__phone_number',
    value: '',
  },
};

export const editAvatarFormSchema = {
  avatar: {
    label: '__avatar',
    type: INPUT_TYPE_IMAGE,
    value: '',
    replace: true,
    imageNumber: 1,
    pickImageHandlerError: null,
    maxWidth: 800,
    maxHeight: 800,
  },
};

export const feedbackSchema = {
  pics: {
    label: '__pic',
    type: INPUT_TYPE_MULTIIMAGE,
    imageNumber: 5,
    maxWidth: 1200,
    maxHeight: 800,
  },
};

export const createEventFormSchema = {
  name: {
    label: '__theme',
    maxLength: 255,
    type: INPUT_TYPE_TAGTEXT,
    value: Config.TEST ? 'Event ' + Math.random() : '',
  },
  maxPartecipants: {
    label: '__max_partic',
    value: Config.TEST ? 4 : 2,
    type: INPUT_TYPE_UISTEPPER,
    minStep: 1,
    subHead: '__number_people',
    step: 1,
    maxStep: MAX_PEOPLE,
    default: 2,
    setValue: nextValue => {
      this.value = nextValue;
    },
  },
  typeKs: {
    label: '__kitchen',
    type: INPUT_TYPE_MULTIPLE,
    singleChoice: false,
    items: typeKitchen,
    optional: false,
    value: Config.TEST ? ['__VEGAN'] : '',
    default: '',
  },
  typeBs: {
    label: '__drink',
    type: INPUT_TYPE_MULTIPLE,
    singleChoice: false,
    items: typeBeverage,
    optional: false,
    value: Config.TEST ? ['__ALCOHOLIC'] : [],
    default: '',
  },
  typeEs: {
    label: '__event',
    type: INPUT_TYPE_MULTIPLE,
    hideSearch: false,
    showCancelButton: false,
    items: typeEvents,
    singleChoice: false,
    optional: false,
    value: Config.TEST ? ['__movie_lovers'] : [],
    default: '',
  },
  intolerances: {
    label: '__allergens',
    type: INPUT_TYPE_MULTIPLE,
    hideSearch: false,
    showCancelButton: false,
    items: intollerances,
    singleChoice: false,
    optional: true,
    value: Config.TEST ? ['__shellfish'] : [],
    default: '',
  },
  typeLs: {
    label: '__location',
    type: INPUT_TYPE_MULTIPLE,
    hideSearch: false,
    singleChoice: false,
    items: locations,
    optional: false,
    value: Config.TEST ? ['__home'] : '',
    default: '',
  },
  date: {
    label: '__date',
    type: INPUT_TYPE_DATE,
    optional: false,
    format: 'DD/MM/YY, h:mm a',
    value: moment().add(1, 'days'),
    default: '',
  },
  pics: {
    label: '__pic',
    type: INPUT_TYPE_MULTIIMAGE,
    imageNumber: 5,
    maxWidth: 1200,
    maxHeight: 800,
  },
  presentation: {
    label: '__presentation',
    optional: true,
    type: INPUT_TYPE_TAGTEXTMULTILINE,
    value: Config.TEST ? 'Testing' : '',
    default: '',
  },
  duration: {
    label: '__duration_short',
    value: 60,
    type: INPUT_TYPE_UISTEPPER,
    minStep: 15,
    step: 15,
    subHead: '__duration_unit',
    maxStep: MAX_DURATION_MIN,
    default: 60,
  },
  pricepp: {
    label: '__pricepp',
    value: 10,
    type: INPUT_TYPE_UISTEPPER,
    minStep: MIN_PRICE,
    step: 1,
    subHead: '__euro_sign',
    maxStep: MAX_PRICE,
    default: 10,
  },
  type: {
    label: '__typeEvent',
    subHead: '__event_private',
    value: false,
    default: false,
    type: INPUT_TYPE_SWITCHBISTATE,
    explanationText: '__event_private_explanation',
    explanationIcon: images._private,
  },
};

export const createEventFormLocationSchema = {
  street: { label: '__street', maxLength: 255, value: '' },
  city: { label: '__city', maxLength: 255, value: '' },
  postalcode: {
    label: '__postal_code',
    maxLength: 255,
    value: '',
    // TODO: remove it
    number: true,
  },
  country: { label: '__country', maxLength: 255, value: '' },
  lat: DEFAULT_LAT_LON,
  lon: DEFAULT_LAT_LON,
};

export const createEventFormSchemaConstraints = {
  name: {
    presence: { allowEmpty: false, message: '__is_missing' },
    length: {
      minimum: 4,
      maximum: 50,
      message: '__error_length_gt_4_lt_50',
    },
  },
  maxPartecipants: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  date: {
    datetime: {
      earliest: moment.utc().add('12', 'hours'),
      message: '__event_alert_1',
    },
  },
  typeKs: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  typeEs: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  typeBs: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  typeLs: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  pics: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  duration: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  pricepp: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  street: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  city: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  postalcode: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  country: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
};

// Edit Profile
export const userDataConstraints = {
  firstName: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  lastName: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  country: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  city: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  address: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
  postcode: {
    presence: { allowEmpty: false, message: '__is_missing' },
  },
};

let userPreferencesConstraints = {};
userPreferencesConstraints[PARAMETER_PREFERENCE_USER_GENDER] = {
  presence: { allowEmpty: false, message: '__is_missing' },
};
export default userPreferencesConstraints;
