import { ITypeProp } from '@services/model';
import { images } from './images';

export const intollerances: Array<ITypeProp> = [
  { id: '__eggs', label: '__eggs', value: '__eggs', icon: images._eggs },
  { id: '__soy', label: '__soy', value: '__soy', icon: images._soy },
  {
    id: '__celery',
    label: '__celery',
    value: '__celery',
    icon: images._celery,
  },
  {
    id: '__mustard',
    label: '__mustard',
    value: '__mustard',
    icon: images._mustard,
  },
  {
    id: '__sesame',
    label: '__sesame',
    value: '__sesame',
    icon: images._sesame,
  },
  {
    id: '__sulphites',
    label: '__sulphites',
    value: '__sulphites',
    icon: images._sulphite,
  },
  {
    id: '__lupins',
    label: '__lupins',
    value: '__lupins',
    icon: images._lupins,
  },
  { id: '__clams', label: '__clams', value: '__clams', icon: images._shell },
  { id: '__dairy', label: '__dairy', value: '__dairy', icon: images._cheese },
  {
    id: '__gluten',
    label: '__gluten',
    value: '__gluten',
    icon: images._gluten,
  },
  { id: '__nuts', label: '__nuts', value: '__nuts', icon: images._peanut },
  {
    id: '__legumes',
    label: '__legumes',
    value: '__legumes',
    icon: images._beans,
  },
  { id: '__fish', label: '__fish', value: '__fish', icon: images._fish },
  {
    id: '__shellfish',
    label: '__shellfish',
    value: '__shellfish',
    icon: images._shrimp,
  },
];
