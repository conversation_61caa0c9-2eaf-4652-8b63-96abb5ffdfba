import { appConfig } from '@config/app-config';
import * as FileSystem from 'expo-file-system';
import shorthash from 'shorthash';
import * as Localization from 'expo-localization';
import moment from 'moment';
import i18n from '@translations/index';
import { images } from './images';
import {
  TouchableOpacity,
  Platform,
  NativeModules,
  Text,
  View,
} from 'react-native';
import { Avatar } from 'react-native-paper';
import React, { useReducer } from 'react';
import { colors } from './colors';
import { globalStyles } from './styles';
import {
  LATITUDE_DELT,
  LONGITUDE_DELT,
  defaultDateTimeFormatClean,
  ACCOUNT_VERIFIED,
  EVENT_OFFER_TYPE_OFFER_PRIVATE,
  ID_SEPARATOR,
  SPACE_SEPARATOR,
  KYC_VALIDATED,
  KYC_VALIDATION_FAILED,
  USER_DOMAIN,
  NAME_LOCATION,
  EVENT_OFFER_KV_STATE_GUEST_PAID,
} from './constants';
import * as Location from 'expo-location';
import Config from 'react-native-config';
import { IEvent, IProperty, ITypeProp } from '../services/model/IEvent';
import { IAccount, IUserWallet } from '@services/model';
import { EVENT_STATE_CLOSING, EVENT_STATE_OPEN } from '@constants/constants';
import { Buffer } from 'buffer';
import { agent } from '@services/index';
import { MESSAGES_ROUTE } from '@routes/route_constants';

export function getReadableDuration(duration: number): string {
  return duration % 60 > 1
    ? Math.floor(duration / 60).toFixed(0) === '0'
      ? (duration % 60) + ' min'
      : Math.floor(duration / 60).toFixed(0) +
        ` ${i18n.t('__hour_sign')} ` +
        (duration % 60) +
        ' min'
    : duration % 60 === 0
    ? `1 ${i18n.t('__hour_sign')}`
    : '-';
}

export function getDistanceUnit(decimal = false) {
  return Localization.isMetric
    ? decimal
      ? i18n.t('__m')
      : i18n.t('__km')
    : decimal
    ? i18n.t('__ft')
    : i18n.t('__miles');
}

export function getIconFromArray(
  itemArray: Array<ITypeProp>,
  type: string,
  defaultIcon = images._event,
) {
  let res = itemArray.filter((e: ITypeProp) => e.id === type);
  if (res.length !== 0) return res[0].icon;

  res = res
    .map((e: any) => e.children)
    .flat()
    .filter((g: any) => g !== undefined)
    .filter((e2: any) => e2.id === type);
  if (res.length !== 0) return res[0].icon;

  return defaultIcon;
}

export function getIcon(type: string) {
  switch (type) {
    // Main menu icons
    case 'edit':
      return 'draw';
    case '__authentication':
      return 'login';
    case '__register':
      return 'account-plus';
    case '__logout':
      return 'logout';
    case '__home':
      return 'home';
    case '__incoming_requests':
      return 'inbox-arrow-down';
    case '__outgoing_requests':
      return 'inbox-arrow-up';
    case '__my_events':
      return 'calendar';
    case '__my_messages':
      return 'message-text-outline';

    // Your request icons
    case '__all':
      return 'notification-clear-all';
    case '__pending':
      return 'clock';
    case '__accept':
    case '__accepted':
    case '__waitingForPayment':
    case '__paid':
      return 'check';
    case '__cancel':
    case '__cancelled':
    case '__host_cancelled':
      return 'cancel';
    case '__completed':
    case '__finished':
      return 'check-all';

    case '__feedback':
      return 'rate-review';
    case '__unknown':
      return 'crosshairs-question';

    // types icons
    case 'kitchen':
    case '__kitchen':
      return 'food';
    case 'drink':
    case '__drink':
      return 'cup-water';
    case 'location':
    case '__location':
      return 'home-city-outline';
    case 'price':
    case '__pricepp':
      return 'cash';
    case 'duration':
    case '__duration_short':
      return 'av-timer';
    case 'adults':
    case '__max_partic':
      return 'face-man';
    case 'eventTypes':
    case '__event':
      return 'format-list-bulleted-type';
    case 'dates':
    case '__date':
      return 'update';
    case 'eye':
      return 'eye';
    case 'add':
      return 'calendar-plus';
    case 'close':
      return 'close';
    case 'more':
      return 'more';
    case 'mobile':
      return 'cellphone';
    case 'profile':
      return 'face-profile';
    case 'avatar':
      return 'face-recognition';
    case '__expired':
      return 'clock-alert-outline';
    case 'camera':
      return 'camera';
    case 'paypal':
      return 'paypal';
    case 'idcard':
      return 'id-card';
    case 'bank':
      return 'bank-plus';
    case '__refund_accepted':
      return 'cash';
    case '__refund_done':
      return 'cash-check';
    case '__refund_denied':
      return 'cash-remove';
    case '__refund':
      return 'cash-refund';
    case '__refund_verify':
      return 'directions-fork';
    case 'delete_profile':
      return 'delete';

    case 'nsfw':
    case '__nsfw':
      return 'eye';
  }
}

export function getFirstImage(
  event: IEvent | undefined,
  dummyImage = images._dummy_event,
) {
  if (event?.pics) {
    if (event.pics.pic_0) {
      return { uri: event.pics.pic_0 };
    }
    if (event.pics.pic_1) {
      return { uri: event.pics.pic_1 };
    }
  }
  return dummyImage;
}

export function getBic(iban: string) {
  return fetch(`https://openiban.com/validate/${iban}?getBIC=true`).then(
    response => response.json(),
  );
}

export function socialIdGenerator(id: string, name: string) {
  // (Math.random() + 1).toString(36).substring(3);
  return (
    name.toLowerCase().split(' ').join(SPACE_SEPARATOR) + ID_SEPARATOR + id
  );
}

export function getBoundedRegionForCoords(latitude: number, longitude: number) {
  const deltaLat = LATITUDE_DELT;
  const deltaLon = LONGITUDE_DELT;

  return {
    latitude,
    longitude,
    latitudeDelta: deltaLat,
    longitudeDelta: deltaLon,
  };
}
export function getBoundedRegion(events: IEvent[]) {
  console.log('getBoundedRegion events', events);

  if (events === undefined) {
    return null;
  }

  const filterSet = events.filter(x => x.lat !== 0 && x.lon !== 0);
  if (filterSet === undefined || filterSet === null || filterSet.length === 0) {
    return null;
  }

  const minLat = filterSet.reduce(
    (min, p) => (p.lat < min ? p.lat : min),
    filterSet[0].lat,
  );
  const minLon = filterSet.reduce(
    (min, p) => (p.lon < min ? p.lon : min),
    filterSet[0].lon,
  );

  const maxLat = filterSet.reduce(
    (max, p) => (p.lat > max ? p.lat : max),
    filterSet[0].lat,
  );
  const maxLon = filterSet.reduce(
    (max, p) => (p.lon > max ? p.lon : max),
    filterSet[0].lon,
  );

  const deltaLat = (maxLat - minLat) / 4;
  const deltaLon = (maxLon - minLon) / 4;

  return {
    latitude: minLat + deltaLat,
    longitude: minLon + deltaLon,
    latitudeDelta: deltaLat + LATITUDE_DELT,
    longitudeDelta: deltaLon + LONGITUDE_DELT,
  };
}

export function getPropertiesFromList(list: Array<IProperty>) {
  // console.log('list is', list);
  let result = '';
  list.forEach(e => (result += i18n.t(e.name) + ' '));
  return result;
}

export function getShareableObject(item: IEvent, isGuest = false) {
  // console.log('item is', item);
  let details = `${i18n.t('__details')}:`;
  if (item.type === EVENT_OFFER_TYPE_OFFER_PRIVATE) {
    details += `\n\t\t- ${i18n.t('__event_private')}`;
  }

  if (isGuest) {
    return {
      message: `${i18n.t('__share_intro_participation')}
      ${appConfig.shareBaseUrl}${item.id}\n${i18n.t('__event')}: 
      ${item.name} ${i18n.t('__kitchen')}: ${getPropertiesFromList(item.typeKs)}
      ${i18n.t('__drink')}: ${getPropertiesFromList(item.typeBs)}
      ${i18n.t('__when')}: ${getDateTime(item.date)}
      ${i18n.t('__place_available')}: ${item.maxPartecipants}
      ${details}`,
      url: `${appConfig.shareBaseUrl}${item.id}`,
    };
  }

  return {
    message: `${i18n.t('__share_intro')} ${appConfig.shareBaseUrl}${item.id}
    ${i18n.t('__event')}: ${item.name}
    ${i18n.t('__kitchen')}: ${getPropertiesFromList(item.typeKs)}
    ${i18n.t('__drink')}: ${getPropertiesFromList(item.typeBs)}
    ${i18n.t('__when')}: ${getDateTime(item.date)}
    ${i18n.t('__place_available')}: ${item.maxPartecipants}
    ${details}`,
    url: `${appConfig.shareBaseUrl}${item.id}`,
  };
}

export async function getCachedLogin(login?: string): Promise<string | null> {
  if (login === undefined || login === null || login === '') {
    return null;
  }
  try {
    return (await agent.getNickname(login)).data;
  } catch (e) {
    console.log('user is probably deleted error', e);
  }
  return null;
}

export function getCleanLoginFunction(userLogin: string, removeSpace = false) {
  if (
    userLogin !== undefined &&
    typeof userLogin === 'string' &&
    userLogin.includes(ID_SEPARATOR)
  ) {
    let words = userLogin
      .split(ID_SEPARATOR)
      [NAME_LOCATION].split(SPACE_SEPARATOR);
    words.forEach(
      (e, i) => (words[i] = e.charAt(0).toUpperCase() + e.substring(1)),
    );
    if (removeSpace) {
      return words.join('_');
    } else {
      return words.join(' ');
    }
  }
  return userLogin;
}

export function ellipsis(textString: string, size = 8) {
  // return string.length <= size ? string : string.substring(0, size);
  return textString.length <= size
    ? textString
    : `${textString.substring(0, size)}...`;
}

export function cleanText(text: string) {
  let words = text.split(' ');

  words.forEach((e, i, a) => {
    if (e.includes(ID_SEPARATOR)) {
      a[i] = getCleanLoginFunction(e);
    }
  });
  return words.join(' ');
}

export function getUsernameOrPlaceholder(
  userLogin: string,
  isLoggedIn: boolean,
  ellipsisValue: number = 50,
) {
  return isLoggedIn && userLogin
    ? ellipsis(getCleanLoginFunction(userLogin), ellipsisValue)
    : i18n.t('__hidden_user');
}

// export const GetIsLoggedIn = () => {
//   const isLoggedIn2 = useSelector((state: any) => {
//     // console.log('useSelector state', state.account?.account);
//     // console.log('useSelector state', state.account !== null);

//     return state.account !== null;
//   });
//   return isLoggedIn2;
// };

export function getAvatarOrDummy(isLoggedIn: boolean, userLogin: string) {
  // console.log('>>getAvatarOrDummy isLoggedIn', isLoggedIn);

  if (isLoggedIn && userLogin) {
    try {
      const basePath = Config.ASSETS_BASE_PROFILE;
      const uri = basePath + Buffer.from(userLogin, 'utf-8').toString('base64');
      return { uri };
    } catch (e) {
      console.log('getAvatarAsync error', e);
    }
    return images._no_profile;
  }
  return images._no_profile;
}

export function getAvatar(userLogin: string) {
  // console.log('getAVatar>>', Config.ASSETS_BASE_PROFILE, userLogin);
  // return images._no_profile;
  if (userLogin === undefined || userLogin === null || userLogin === '') {
    return images._no_profile;
  }

  const basePath = Config.ASSETS_BASE_PROFILE;
  const uri = basePath + Buffer.from(userLogin, 'utf-8').toString('base64');
  return uri;
}

/**
 * parse the push notification path to return the base path and the params object
 * using the pattern basePath/ParamName1/ParamValue1/ParamName2/ParamValue2 ....
 * @param {path} path
 */
export function getParamsObject(path: string): {
  basePath: string;
  params: any | undefined;
} {
  const pathSplit = path.split('/');
  const basePath = pathSplit[0];
  let params: any = {};
  if (pathSplit.length > 1) {
    let paramsArray = pathSplit.splice(1);
    for (let i = 0; i < paramsArray.length; i += 2) {
      params[paramsArray[i]] = paramsArray[i + 1];
    }
  } else {
    params = undefined;
  }

  return {
    basePath,
    params,
  };
}

export async function getAvatar2(userLogin: string) {
  const userUrl =
    Config.ASSETS_BASE_PROFILE +
    Buffer.from(userLogin, 'utf-8').toString('base64');
  console.log('getAvatar init', userUrl);
  let image = await fetch(userUrl, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Accept: '*/*',
    },
    credentials: 'include',
  })
    .then(r => {
      return { uri: r };
    })
    .catch(e => {
      console.log('catch image getAvatar', image, e);
      return images._no_profile;
    });
  console.log('image getAvatar', image);
  return image;
}

export function getDateTime(change_time: string, space = true) {
  if (change_time) {
    return (
      (space ? ' ' : '') +
      moment(change_time).format(defaultDateTimeFormatClean)
    );
  }
  return '';
}

export const getCachedAvatar = async (userLogin: string) => {
  const imagePath = await (Config.ASSETS_BASE_PROFILE +
    Buffer.from(userLogin, 'utf-8').toString('base64'));
  const name = shorthash.unique(imagePath);

  const path = `${FileSystem.cacheDirectory}${name}`;
  const image = await FileSystem.getInfoAsync(path);
  if (image.exists) {
    return image.uri;
  }

  const newImage = await FileSystem.downloadAsync(imagePath, path);
  return newImage.uri;
};

export const parsePushNotification = async (remoteMessage: any) => {
  const payload = remoteMessage.data;
  const userTxNickname = await getCachedLogin(payload.userTx);
  const userRxNickname = await getCachedLogin(payload.userRx);

  const composeObject: {
    path: any;
    data: any;
    notification?: {
      title: string;
      body: string;
      image: string;
    };
  } = {
    path: payload.path,
    data: payload,
  };
  if (
    remoteMessage.notification &&
    remoteMessage.notification.title &&
    remoteMessage.notification.body
  ) {
    console.log('remoteMessage.notification', remoteMessage.notification);
    composeObject.notification = {
      title: remoteMessage.notification.title,
      body: remoteMessage.notification.body
        .replace(payload.userTx, userTxNickname)
        .replace(payload.userRx, userRxNickname),
      image: remoteMessage.notification.image || 'default',
    };
  }

  if (payload.path.includes(MESSAGES_ROUTE)) {
    console.log('parsePushNotification MESSAGES_ROUTE', payload);
    return {
      ...composeObject,
      // message: composeObject,
      data: {
        ...composeObject.data,
        body: {
          text: payload.text
            .replace(payload.userTx, userTxNickname)
            .replace(payload.userRx, userRxNickname),
        },
      },
    };
  }
  console.log('parsePushNotification', payload);
  return {
    ...composeObject,
    data: {
      ...composeObject.data,
      message: payload.message
        .replace(payload.userTx, userTxNickname)
        .replace(payload.userRx, userRxNickname),
    },
  };
};

export function isJSON(str: string) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}

export function parseMessage(message: string | undefined): string {
  // console.log('parseMessage is', message);
  if (message !== undefined && message !== '' && typeof message !== 'object') {
    let result = '';
    if (message.split(' ').length > 1) {
      message.split(' ').forEach(e => {
        result += translateOrNot(e) + ' ';
      });
    } else {
      result += translateOrNot(message);
    }
    return result;
  }
  return '';
}

export function checkKycValidatedStatus(userWallet: IUserWallet) {
  return userWallet && userWallet.kycStatus === KYC_VALIDATED;
}

export function maskValue(value: string, pos = 4, mask = '#') {
  return (
    ('' + value).slice(0, pos) +
    ('' + value).slice(pos, -pos).replace(/./g, '#') +
    ('' + value).slice(-pos)
  );
}

export function checkBankAccount(userWallet: IUserWallet) {
  return !!userWallet?.iban;
}

export const checkPhoneNumber = (account: IAccount) =>
  account?.phoneNumber && account.phoneNumber !== '-1';

export function checkAccountAvatar(account: IAccount) {
  return !!account?.imageUrl;
}

export function checkAccountVerified(account: IAccount) {
  return account.verified === ACCOUNT_VERIFIED;
}

export function checkAddress(account: IAccount) {
  return !!(
    account?.address &&
    account?.address !== 'null' &&
    account?.postcode &&
    account?.postcode !== 'null' &&
    account?.city &&
    account?.city !== 'null' &&
    account?.country &&
    account?.country !== 'null'
  );
}

function translateOrNot(element: string) {
  let result = '';
  if (element.startsWith('__')) {
    result += i18n.t(element);
  } else {
    result += element;
  }
  return result;
}

export function parseUrl(url: string): any {
  console.log('parseUrl', url);
  let regex = /[?&]([^=#]+)=([^&#]*)/g,
    params: any = {},
    match;
  while ((match = regex.exec(url))) {
    params[match[1]] = match[2];
    console.log(match[1], match[2]);
  }
  return params;
}

export async function getLocationPermission() {
  const { status } = await Location.requestForegroundPermissionsAsync();
  // const { status } = await Permissions.askAsync(Permissions.LOCATION);
  console.log('getLocationPermission >>', status);

  if (status === 'granted') {
    // try {
    //   const location = await Location.getCurrentPositionAsync({});
    //   this.setState({ hasPermission: true, userLocation: location }, async () =>
    //     console.log('MAP RECEIVES setState'),
    //   );
    // } catch (e) {
    //   console.error('getCurrentPositionAsync error', e);
    // }
    return true;
  }
  return false;
  // else {
  //   Alert.alert(
  //     i18n.t('__user_not_position'),
  //     i18n.t('__user_location_permission_denied'),
  //     [{ text: 'OK', onPress: () => console.log('OK Pressed') }],
  //   );
  // }
}

export function calculateFlyDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number,
  unit = 'K',
) {
  if (lat1 === lat2 && lon1 === lon2) {
    return 0;
  } else {
    const radlat1 = (Math.PI * lat1) / 180;
    const radlat2 = (Math.PI * lat2) / 180;
    const theta = lon1 - lon2;
    const radtheta = (Math.PI * theta) / 180;
    let dist =
      Math.sin(radlat1) * Math.sin(radlat2) +
      Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
    if (dist > 1) {
      dist = 1;
    }
    dist = Math.acos(dist);
    dist = (dist * 180) / Math.PI;
    dist = dist * 60 * 1.1515;
    if (unit === 'K') {
      dist = dist * 1.609344;
    }
    if (unit === 'N') {
      dist = dist * 0.8684;
    }
    return dist;
  }
}

export function getDistanceString(distance: number) {
  if (!distance) {
    return 'N/A';
  }
  if (distance < 1) {
    return `${distance.toFixed(1)} ${getDistanceUnit(true)}`;
  }
  return `${distance.toFixed(0)} ${getDistanceUnit()}`;
}

export function getDistanceColor(distance: number) {
  if (!distance) {
    return colors.declined;
  }
  if (distance < 50) {
    return colors.success;
  }
  if (distance < 100) {
    return colors.warning;
  }
  return colors.declined;
}

export function showAvatar(
  userLogin: string,
  loggedIn: boolean,
  size: number,
  action: Function | null,
) {
  const avatar = getAvatarOrDummy(loggedIn, userLogin);

  return (
    <TouchableOpacity
      onPress={() =>
        typeof action === 'function' ? action(userLogin, avatar) : null
      }>
      <Avatar.Image
        source={avatar}
        size={size}
        style={{ backgroundColor: colors.whiteBackground }}
      />
    </TouchableOpacity>
  );
}

export const getDeviceLanguage = () => {
  const deviceLanguage =
    Platform.OS === 'ios'
      ? NativeModules.SettingsManager.settings.AppleLocale ||
        NativeModules.SettingsManager.settings.AppleLanguages[0] //iOS 13
      : NativeModules.I18nManager.localeIdentifier;

  console.log('getDeviceLanguage', deviceLanguage);
  return deviceLanguage;
};

export const getUserNameWithDomain = (
  username: string,
  domain = USER_DOMAIN,
  email: String | undefined,
) => `${username}|${domain}${email ? `|${email}` : ''}`;

export const getEventStatus = (eventOfferState: number) => {
  return eventOfferState === EVENT_STATE_OPEN ? (
    <View style={{ alignSelf: 'flex-start' }}>
      <Text
        style={{
          ...globalStyles.eventStateLabel,
          borderColor: colors.success,
          color: colors.success,
        }}>
        {i18n.t('__open')}
      </Text>
    </View>
  ) : eventOfferState === EVENT_STATE_CLOSING ? (
    <View style={{ alignSelf: 'flex-start' }}>
      <Text
        style={{
          ...globalStyles.eventStateLabel,
          borderColor: colors.warning,
          color: colors.warning,
        }}>
        {i18n.t('__closing')}
      </Text>
    </View>
  ) : (
    <View style={{ alignSelf: 'flex-start' }}>
      <Text
        style={{
          ...globalStyles.eventStateLabel,
          borderColor: colors.error,
          color: colors.error,
        }}>
        {i18n.t('__closed')}
      </Text>
    </View>
  );
};

export const getMapLink = (params: {
  name: string;
  lat: Number;
  lon: Number;
}) => {
  const scheme = Platform.select({
    ios: 'maps:0,0?q=',
    android: 'geo:0,0?q=',
  });
  const latLng = `${params.lat},${params.lon}`;
  return Platform.select({
    ios: `${scheme}${params.name}@${latLng}`,
    android: `${scheme}${latLng}(${params.name})`,
  });
};

export const isEventUnlocked = (event: any) => {
  if (event && event.state >= EVENT_OFFER_KV_STATE_GUEST_PAID) return true;
  return false;
};

export function detectPhoneOrEmail(text: string) {
  var PhoneRegex = /\b[\+]?[(]?[0-9]{2,6}[)]?[-\s\.]?[-\s\/\.0-9]{3,15}\b/m;
  var EmailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9_-]+)/gi;
  return PhoneRegex.test(text) || EmailRegex.test(text);
}

/**
 * it returns the image size in bytes
 * @param uri string
 * @returns Promise<number>
 */
export const getImageDimesion = async (uri: string) => {
  return await fetch(uri)
    .then(response => response.blob())
    .then(blob => blob.size);
};
