import { ITypeProp } from '@services/model';
import { images } from './images';

export const typeKitchen: Array<ITypeProp> = [
  {
    id: '__pizza',
    label: '__pizza',
    value: '__pizza',
    icon: images._pizza,
  },
  {
    id: '__ethnic',
    label: '__ethnic',
    value: '__ethnic',
    icon: images._ethnic,
  },
  {
    id: '__fusion',
    label: '__fusion',
    value: '__fusion',
    icon: images._fusion,
  },
  {
    id: '__mediterrean',
    label: '__mediterrean',
    value: '__mediterrean',
    icon: images._bruschetta,
  },
  {
    id: '__asian',
    label: '__asian',
    value: '__asian',
    icon: images._ramen,
  },
  {
    id: '__chinese',
    label: '__chinese',
    value: '__chinese',
    icon: images._rice,
  },
  {
    id: '__japanese',
    label: '__japanese',
    value: '__japanese',
    icon: images._sushi,
  },
  {
    id: '__turkish',
    label: '__turkish',
    value: '__turkish',
    icon: images._turkish,
  },
  {
    id: '__burgers',
    label: '__burgers',
    value: '__burgers',
    icon: images._burger,
  },
  {
    id: '__greek',
    label: '__greek',
    value: '__greek',
    icon: images._greek,
  },
  {
    id: '__italian',
    label: '__italian',
    value: '__italian',
    icon: images._italian,
  },
  {
    id: '__pasta_risotto',
    label: '__pasta_risotto',
    value: '__pasta_risotto',
    icon: images._pasta_risotto,
  },
  {
    id: '__baking',
    label: '__baking',
    value: '__baking',
    icon: images._baking,
  },
  {
    id: '__pastry',
    label: '__pastry',
    value: '__pastry',
    icon: images._pastry,
  },
  {
    id: '__rice',
    label: '__rice',
    value: '__rice',
    icon: images._rice_generic,
  },
  {
    id: '__cereals',
    label: '__cereals',
    value: '__cereals',
    icon: images._cereal,
  },
  {
    id: '__pate',
    label: '__pate',
    value: '__pate',
    icon: images._meat_pie,
  },
  {
    id: '__seafood',
    label: '__seafood',
    value: '__seafood',
    icon: images._seafood,
  },
  {
    id: '__mushrooms',
    label: '__mushrooms',
    value: '__mushrooms',
    icon: images._mushrooms,
  },
  {
    id: '__raw',
    label: '__raw',
    value: '__raw',
    icon: images._raw,
  },
  {
    id: '__bio',
    label: '__bio',
    value: '__bio',
    icon: images._bio,
  },
  {
    id: '__frozen',
    label: '__frozen',
    value: '__frozen',
    icon: images._frozen,
  },
  {
    id: '__canned',
    label: '__canned',
    value: '__canned',
    icon: images._canned_food,
  },
  {
    id: '__homemade',
    label: '__homemade',
    value: '__homemade',
    icon: images._homemade,
  },
  {
    id: '__african',
    label: '__african',
    value: '__african',
    icon: images._african,
  },
  {
    id: '__vegeterian',
    label: '__vegeterian',
    value: '__vegeterian',
    icon: images._vegeterian,
  },
  {
    id: '__vegan',
    label: '__vegan',
    value: '__vegan',
    icon: images._vegan,
  },
  {
    id: '__street_food',
    label: '__street_food',
    value: '__street_food',
    icon: images._street_food,
  },
  {
    id: '__fish',
    label: '__fish',
    value: '__fish',
    icon: images._fish,
  },
  {
    id: '__meat',
    label: '__meat',
    value: '__meat',
    icon: images._meat2,
  },
  {
    id: '__junk_food',
    label: '__junk_food',
    value: '__junk_food',
    icon: images._burger,
  },
  {
    id: '__thai',
    label: '__thai',
    value: '__thai',
    icon: images._thai,
  },
  {
    id: '__mexican',
    label: '__mexican',
    value: '__mexican',
    icon: images._mexican,
  },
  {
    id: '__argentina',
    label: '__argentina',
    value: '__argentina',
    icon: images._argentina,
  },
  {
    id: '__brazilian',
    label: '__brazilian',
    value: '__brazilian',
    icon: images._brazilian,
  },
  {
    id: '__gourmet_deli',
    label: '__gourmet_deli',
    value: '__gourmet_deli',
    icon: images._chef,
  },
  {
    id: '__chips_popcorn',
    label: '__chips_popcorn',
    value: '__chips_popcorn',
    icon: images._popcorn,
  },
  {
    id: '__bacon',
    label: '__bacon',
    value: '__bacon',
    icon: images._bacon,
  },
  {
    id: '__sweets_candies',
    label: '__sweets_candies',
    value: '__sweets_candies',
    icon: images._candies,
  },
  {
    id: '__icecream',
    label: '__icecream',
    value: '__icecream',
    icon: images._ice_cream,
  },
  {
    id: '__cake',
    label: '__cake',
    value: '__cake',
    icon: images._cake,
  },
  {
    id: '__poultry_meat',
    label: '__poultry_meat',
    value: '__poultry_meat',
    icon: images._poultry,
  },
  {
    id: '__pork',
    label: '__pork',
    value: '__pork',
    icon: images._pork,
  },
  {
    id: '__duck',
    label: '__duck',
    value: '__duck',
    icon: images._duck,
  },
  {
    id: '__chicken',
    label: '__chicken',
    value: '__chicken',
    icon: images._chicken,
  },
  {
    id: '__salad',
    label: '__salad',
    value: '__salad',
    icon: images._salad,
  },
  {
    id: '__season_fruit',
    label: '__season_fruit',
    value: '__season_fruit',
    icon: images._seasonable_fruit,
  },
  {
    id: '__season_veg',
    label: '__season_veg',
    value: '__season_veg',
    icon: images._seasonable_vegetables,
  },
  {
    id: '__breads',
    label: '__breads',
    value: '__breads',
    icon: images._bakery,
  },
  {
    id: '__jam',
    label: '__jam',
    value: '__jam',
    icon: images._jam,
  },
  {
    id: '__butter',
    label: '__butter',
    value: '__butter',
    icon: images._toast,
  },
  {
    id: '__evo',
    label: '__evo',
    value: '__evo',
    icon: images._olive_oil,
  },
  {
    id: '__oil',
    label: '__oil',
    value: '__oil',
    icon: images._seed_oil,
  },
  {
    id: '__eggs',
    label: '__eggs',
    value: '__eggs',
    icon: images._eggs,
  },
  {
    id: '__cheese',
    label: '__cheese',
    value: '__cheese',
    icon: images._cheese,
  },
  {
    id: '__km0',
    label: '__km0',
    value: '__km0',
    icon: images._km0,
  },
];
