import { StyleSheet } from 'react-native';
import { icon_size_chip, icon_size } from './constants';
import { colors } from './colors';

export const globalStyles = StyleSheet.create({
  elementCard: {
    marginHorizontal: 20,
    marginVertical: 10,
    elevation: 10,
    padding: 20,
    paddingTop: 5,
    borderRadius: 20,
  },
  container: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chipContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  flatlistEntry: {
    padding: 5,
    borderRadius: 15,
    marginBottom: 5,
    paddingVertical: 10,
    borderColor: colors.gray,
    borderWidth: 1,
    backgroundColor: colors.whiteBackground,
  },
  flatlistElement: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  flatlistColumnElement: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-around',
  },
  blueChip: {
    backgroundColor: colors.blue,
    borderRadius: 10,
    paddingLeft: 15,
    paddingRight: 15,
    overflow: 'hidden',
  },
  elementCounter: {
    marginBottom: 20,
    backgroundColor: colors.blue,
    borderRadius: 10,
    overflow: 'hidden',
    paddingLeft: 10,
    paddingRight: 10,
  },
  marginDivider: { backgroundColor: colors.primary },
  badge: { fontSize: 14, marginRight: 10, marginTop: 10 },
  rowProp: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  marginDivider3: {
    marginVertical: 5,
  },
  marginDivider2: {
    marginVertical: 10,
  },
  text: {
    color: colors.black,
    fontWeight: '700',
  },
  radio: {
    backgroundColor: '#fafafa',
    borderRadius: 50,
    borderColor: '#e9e9e9',
    borderWidth: 1,
    marginRight: 5,
  },
  contentCard: {
    flex: 1,
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 24,
    shadowColor: colors.black,
    shadowOpacity: 0.15,
    shadowOffset: {
      width: 1,
      height: 4,
    },
    shadowRadius: 12,
  },
  fab: {
    position: 'absolute',
    backgroundColor: colors.primary,
    color: colors.white,
    zIndex: 100,
    elevation: 50,
    margin: 20,
    right: 70,
    left: 70,
    bottom: 0,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchImage: {
    marginLeft: 10,
    height: icon_size,
    width: icon_size,
  },
  chipImage: { width: icon_size_chip, height: icon_size_chip },

  eventStateLabel: {
    borderRadius: 8,
    borderWidth: 1,
    paddingLeft: 5,
    paddingRight: 5,
    backgroundColor: colors.whiteBackground,
    overflow: 'hidden',
  },
});
