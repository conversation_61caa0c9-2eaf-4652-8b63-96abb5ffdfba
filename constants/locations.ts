import { ITypeProp } from '@services/model';
import { images } from './images';

export const locations: Array<ITypeProp> = [
  {
    id: '__terrace',
    label: '__terrace',
    value: '__terrace',
    icon: images._terrace,
  },
  {
    id: '__neighborhood',
    label: '__neighborhood',
    value: '__neighborhood',
    icon: images._neighborhood,
  },
  {
    id: '__garden',
    label: '__garden',
    value: '__garden',
    icon: images._garden,
  },
  {
    id: '__canteen',
    label: '__canteen',
    value: '__canteen',
    icon: images._canteen,
  },
  {
    id: '__home',
    label: '__home',
    value: '__home',
    icon: images._house,
  },
  {
    id: '__restaurant',
    label: '__restaurant',
    value: '__restaurant',
    icon: images._restaurant,
  },
  {
    id: '__hotel',
    label: '__hotel',
    value: '__hotel',
    icon: images._hotel,
  },
  {
    id: '__bnb',
    label: '__bnb',
    value: '__bnb',
    icon: images._bnb,
  },

  {
    id: '__park',
    label: '__park',
    value: '__park',
    icon: images._park,
  },
  {
    id: '__square',
    label: '__square',
    value: '__square',
    icon: images._square,
  },
  {
    id: '__club',
    label: '__club',
    value: '__club',
    icon: images._club,
  },
  {
    id: '__beach',
    label: '__beach',
    value: '__beach',
    icon: images._beach,
  },
  {
    id: '__camping',
    label: '__camping',
    value: '__camping',
    icon: images._camping,
  },
  {
    id: '__sport_facilities',
    label: '__sport_facilities',
    value: '__sport_facilities',
    icon: images._sports_facilities,
  },
  {
    id: '__office',
    label: '__office',
    value: '__office',
    icon: images._office,
  },
  {
    id: '__rooftop',
    label: '__rooftop',
    value: '__rooftop',
    icon: images._rooftop,
  },
  {
    id: '__bonfire',
    label: '__bonfire',
    value: '__bonfire',
    icon: images._bonfire,
  },
  {
    id: '__guest_bath',
    label: '__guest_bath',
    value: '__guest_bath',
    icon: images._private_toilets,
  },
  {
    id: '__pet_friendly',
    label: '__pet_friendly',
    value: '__pet_friendly',
    icon: images._pet_care,
  },
  {
    id: '__private_room',
    label: '__private_room',
    value: '__private_room',
    icon: images._private_room,
  },
  {
    id: '__wifi',
    label: '__wifi',
    value: '__wifi',
    icon: images._wifi,
  },
  {
    id: '__airco',
    label: '__airco',
    value: '__airco',
    icon: images._airco,
  },
  {
    id: '__swim_pool',
    label: '__swim_pool',
    value: '__swim_pool',
    icon: images._swimming_pool,
  },
  {
    id: '__playground',
    label: '__playground',
    value: '__playground',
    icon: images._playground,
  },
  {
    id: '__wood_oven',
    label: '__wood_oven',
    value: '__wood_oven',
    icon: images._wood_oven,
  },
  {
    id: '__indoor',
    label: '__indoor',
    value: '__indoor',
    icon: images._indoor,
  },
  {
    id: '__outdoor',
    label: '__outdoor',
    value: '__outdoor',
    icon: images._outdoor,
  },
  {
    id: '__yacht',
    label: '__yacht',
    value: '__yacht',
    icon: images._yacht,
  },
  {
    id: '__sail_boat',
    label: '__sail_boat',
    value: '__sail_boat',
    icon: images._sail_boat,
  },
  {
    id: '__wheelchair_access',
    label: '__wheelchair_access',
    value: '__wheelchair_access',
    icon: images._wheelchair,
  },
];
