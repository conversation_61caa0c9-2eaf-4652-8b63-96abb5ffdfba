export const DURATION = 60;
export const DEFAULT_LAT_LON = 0.0;
export const MIN_PRICE = 5;
export const MAX_PRICE = 100;
export const MIN_DISTANCE_FILTER = 5;
export const MAX_DISTANCE_FILTER = 501;
export const MAX_DURATION_MIN = 240;
export const MAX_DISTANCE = 10000;
export const MAX_PEOPLE = 50;
export const LATITUDE_DELT = 0.0922;
export const LONGITUDE_DELT = 0.0421;

export const EVENT_OFFER_TYPE_OFFER_PUBLIC = 1;
export const EVENT_OFFER_TYPE_REQUEST_PUBLIC = 2;
export const EVENT_OFFER_TYPE_OFFER_PRIVATE = 3;
export const EVENT_OFFER_TYPE_REQUEST_PRIVATE = 4;

//feedback type

export const REVIEW_HOST_2_GUEST = 2;
export const FEEDBACK_GUEST_2_HOST = 1;

// event state
export const EVENT_STATE_FEEDBACK_CLOSE = 7;
export const EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_CLOSE = 6;
export const EVENT_STATE_FEEDBACK_OPEN_AND_MANAGE_DISPUTE = 5;
export const EVENT_STATE_FEEDBACK_OPEN_AND_DISPUTE_OPEN = 4;
export const EVENT_STATE_CLOSE = 1;
export const EVENT_STATE_CLOSING = 2;
export const EVENT_STATE_OPEN = 3;
export const EVENT_STATE_DELETED = -1;

// event kv state
export const EVENT_OFFER_KV_STATE = 0;
export const EVENT_OFFER_KV_STATE_GUEST_REQUEST = 1;
export const EVENT_OFFER_KV_STATE_HOST_CONFIRM = 2;
export const EVENT_OFFER_KV_STATE_HOST_CANCEL = 3;
export const EVENT_OFFER_KV_STATE_GUEST_CANCEL = 4;
export const EVENT_OFFER_KV_STATE_GUEST_PAID = 5;
export const EVENT_OFFER_KV_STATE_GUEST_PAID_CANCEL = 6;
export const EVENT_OFFER_KV_STATE_HOST_PAID_CANCEL = 7;
export const EVENT_OFFER_KV_STATE_HOST_FEEDBACK_LEFT = 8;
export const EVENT_OFFER_KV_STATE_GUEST_FEEDBACK_LEFT = 9;
export const EVENT_OFFER_KV_STATE_ALL_FEEDBACK_LEFT = 17;

export const EVENT_OFFER_KV_STATE_DELETED = -1;

export const EVENT_OFFER_KV_STATE_NO_REFUND = 0;
export const EVENT_OFFER_KV_STATE_GUEST_OPEN_REFUND = 21;
export const EVENT_OFFER_KV_HOST_CONFIRMS_REFUND = 22; // Host confirms to refund the guest
export const EVENT_OFFER_KV_HOST_DENIES_REFUND = 23; // Host doesn't want to refund the guest
export const EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST = 25; // Host doesn't want to refund the guest

export const EVENT_OFFER_KV_GUEST_CANCELLED_PAID_REQUEST_BEFORE_EVENT_CLOSE = 24; // Host

export const EVENT_OFFER_KV_HOST_CANCELLED_PAID_REQUEST_REFUNDED_DONE = 27; // Host

export const MESSAGE_STATE_SYSTEM = 10;
export const MESSAGE_STATE_RX_READ = 2;
export const MESSAGE_STATE_BIN = -1;
export const MESSAGE_STATE_STICKY = -2;

export const defaultDateTimeFormat = 'DD/MMM/YY - HH:mm';
export const defaultDateFormat = 'DD MMM YYYY';
export const defaultDateTimeFormatShort = 'DD MMM - HH:mm';
export const defaultDateTimeFormatClean = 'DD MMM YY, HH:mm';

export const _CANCELLED = '__cancelled';
export const _ERROR = '__error';
export const _SUCCESS = '__success';
export const _WARNING = '__warning';
export const _CONFIRM = '__confirm';
export const _DELETE = '__delete';
export const _CANCEL = '__cancel';

export const BUDDY = 'BUDDY';
export const BROTHER = 'BROTHER';

export const NAME_LOCATION = 0;
export const ID_SEPARATOR = '_1si_';
export const SPACE_SEPARATOR = '@@';

export const swipeConfig = {
  velocityThreshold: 0.3,
  directionalOffsetThreshold: 80,
};

export const KYC_VALIDATED = 'verified';
export const KYC_VALIDATION_ASKED = 'processing';
export const KYC_VALIDATION_CREATED = 'INIT';
export const KYC_VALIDATION_FAILED = 'requires_input';

export const ACCOUNT_VERIFIED = '1';
export const PHONE_VERIFIED = '1';

export const POLICY_CHECKED = 'checked';
export const PARAMETER_PREFIX = 'parameter_';
export const PARAMETER_PREFIX_POLICY = `${PARAMETER_PREFIX}policy_`;
export const PARAMETER_PREFERENCE_PREFIX = 'pref_';
export const PARAMETER_PREFERENCE_INTOLLERANCE = `${PARAMETER_PREFERENCE_PREFIX}intollerance_`;
export const PARAMETER_PREFERENCE_BADGE = `${PARAMETER_PREFERENCE_PREFIX}badge_`;
export const PARAMETER_PREFERENCE_USER_DESCRIPTION = `${PARAMETER_PREFERENCE_PREFIX}user_description`;
export const PARAMETER_PREFERENCE_USER_GENDER = `${PARAMETER_PREFERENCE_PREFIX}user_gender`;
export const PARAMETER_MAX_GUESTS = `${PARAMETER_PREFIX}max_guest_request`;

export const EOKV_V_PARTICIPANTS = 'participants';
export const EOKV_V_REPORT = 'report';

export const OTP_LENGTH = 6;

export const INPUT_TYPE_TEXT = 'text';
export const INPUT_TYPE_TAGTEXT = 'tagText';
export const INPUT_TYPE_TAGTEXTMULTILINE = 'tagTextMultiline';
export const INPUT_TYPE_UISTEPPER = 'uiStepper';
export const INPUT_TYPE_MULTIPLE = 'multiple';
export const INPUT_TYPE_TEXTAREA = 'textarea';
export const INPUT_TYPE_IMAGE = 'image';
export const INPUT_TYPE_DATE = 'date';
export const INPUT_TYPE_SWITCHBISTATE = 'switchBiState';
export const INPUT_TYPE_MULTIIMAGE = 'multiImage';
export const INPUT_TYPE_MULTIIMAGE_PICKER = 'imagepicker';
export const INPUT_TYPE_PICKER = 'picker';
export const INPUT_TYPE_SLIDER = 'slider';
export const INPUT_TYPE_PICKER_COUNTRY = 'countryPicker';
export const INPUT_TYPE_PICKER_COUNTRY_PHONE = 'countryPickerPhone';

// SOCIAL USER DOMAIN
export const USER_DOMAIN = 'selfinvite';
export const USER_FB_DOMAIN = 'facebook';
export const USER_APPLE_DOMAIN = 'apple';

export const icon_size_chip = 20;
export const icon_size_medium = 48;
export const icon_size_large = 64;
export const icon_size = 32;
export const icon_size_small = 25;
export const icon_size_xsmall = 18;

export const INF_SYMBOL = '\u221E';
