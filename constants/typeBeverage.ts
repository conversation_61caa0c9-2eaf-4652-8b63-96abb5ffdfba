import { ITypeProp } from '@services/model';
import { images } from './images';

export const typeBeverage: Array<ITypeProp> = [
  {
    id: '__water',
    label: '__water',
    value: '__water',
    icon: images._water,
  },
  {
    id: '__alcoholic',
    label: '__alcoholic',
    value: '__alcoholic',
    icon: images._alcohol,
  },
  {
    id: '__analcoholic',
    label: '__analcoholic',
    value: '__analcoholic',
    icon: images._analcohol,
  },
  {
    id: '__wine',
    label: '__wine',
    value: '__wine',
    icon: images._wine,
  },
  {
    id: '__cocktail',
    label: '__cocktail',
    value: '__cocktail',
    icon: images._cocktail,
  },
  {
    id: '__beer',
    label: '__beer',
    value: '__beer',
    icon: images._beer,
  },
  {
    id: '__liquor',
    label: '__liquor',
    value: '__liquor',
    icon: images._liquor,
  },
  {
    id: '__smoothie',
    label: '__smoothie',
    value: '__smoothie',
    icon: images._smoothie,
  },
  {
    id: '__self_drink',
    label: '__self_drink',
    value: '__self_drink',
    icon: images._selfmade_drink,
  },
  {
    id: '__infused',
    label: '__infused',
    value: '__infused',
    icon: images._infusion,
  },
  {
    id: '__veg_milk',
    label: '__veg_milk',
    value: '__veg_milk',
    icon: images._veg_milk,
  },
  {
    id: '__milk',
    label: '__milk',
    value: '__milk',
    icon: images._milk,
  },
  {
    id: '__fresh_juice',
    label: '__fresh_juice',
    value: '__fresh_juice',
    icon: images._juices,
  },
];
