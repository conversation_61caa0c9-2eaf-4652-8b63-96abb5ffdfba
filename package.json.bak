{"name": "selfinvite", "main": "node_modules/expo/AppEntry.js", "type": "commonjs", "scripts": {"postinstall": "patch-package", "clean2": "react-native-clean-project", "clean": "rm -rf $TMPDIR/react-* && watchman watch-del-all && npm cache clean", "clean-cache": "react-native start --reset-cache", "reset-cache": "expo r -c", "start": "react-native start", "why": "yarn why", "android:local": "ENVFILE=.env.local; rm android/.env* ; cp .env.local android/.env && npx react-native run-android --active-arch-only", "android:dev": "ENVFILE=.env.dev; rm android/.env* ; cp .env.dev android/.env && npx react-native run-android", "android:prod": "ENVFILE=.env.prod; rm android/.env* ; cp .env.prod android/.env && npx react-native run-android", "ios": "npx react-native run-ios", "ios:env:prod": "ENVFILE=.env.prod npx react-native run-ios", "ios:release": "react-native run-ios --configuration Release", "android:debug": "ENVFILE=.env.dev; cd android && ./gradlew assembleDebug && ./gradlew installDebug", "android:build": "ENVFILE=.env.prod; cd android && ./gradlew assembleRelease", "android:install": "ENVFILE=.env.prod; cd android && ./gradlew assembleRelease && ./gradlew installRelease", "android:offline": "./build_debug_offline.sh", "android:release": "ENVFILE=.env.prod; cp .env.prod android/ ; npx react-native run-android --variant=release", "link-node": "ln -s $(which node) /usr/local/bin/node", "test": "jest --watch --coverage=false --changedSince=origin/main", "testDebug": "jest -o --watch --coverage=false --config jest.config.js", "testFinal": "jest", "updateSnapshots": "jest -u --coverage=false", "detox:android:build": "npx detox build -c android.sim.debug", "detox:android:test": "npx detox test -c android.sim.debug", "detox:recorder:ios": "detox recorder --bundleId 'com.j.autoinvito' --simulatorId booted --outputTestFile './DetoxRecordedTest.js' --testName 'My Recorded Test' --record", "lint": "eslint . --ext .js,.ts,.jsx,.tsx", "ios:push": "xcrun simctl push 7A29DDEA-BD5E-4A4E-A533-07A5E22C846E com.j.autoinvito notification_sample/ios_2.apns", "ios:push2": "xcrun simctl push 7A29DDEA-BD5E-4A4E-A533-07A5E22C846E com.j.autoinvito notification_sample/ios_1_search.apns", "ios:publish": "fastlane ios release --env prod", "android:publish": "fastlane android release --env prod", "android": "expo run:android"}, "dependencies": {"@invertase/react-native-apple-authentication": "^2.1.5", "@notifee/react-native": "^7.8.2", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "1.21.0", "@react-native-community/datetimepicker": "7.7.0", "@react-native-community/picker": "^1.8.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-community/toolbar-android": "0.2.1", "@react-native-firebase/app": "^19.3.0", "@react-native-firebase/crashlytics": "^19.3.0", "@react-native-firebase/messaging": "^19.3.0", "@react-native-masked-view/masked-view": "0.3.0", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/drawer": "^6.6.2", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "@react-navigation/stack": "^6.3.16", "@reduxjs/toolkit": "^1.9.7", "@sanar/react-native-highlight-text": "^1.0.2", "@stripe/connect-js": "^3.3.5", "@stripe/stripe-react-native": "^0.35.0", "@types/i18n-js": "^3.8.9", "apisauce": "^2.1.5", "axios": "^1.5.0", "axios-cache-interceptor": "^1.5.1", "buffer": "^6.0.3", "deprecated-react-native-prop-types": "^5.0.0", "expo": "~50.0.17", "expo-calendar": "~12.2.1", "expo-camera": "~14.1.3", "expo-constants": "~15.4.6", "expo-linking": "~6.2.2", "expo-localization": "~14.8.4", "expo-location": "~16.5.5", "expo-media-library": "~15.9.2", "expo-secure-store": "~12.8.1", "expo-splash-screen": "~0.26.5", "expo-status-bar": "~1.11.1", "i18n-js": "^4.4.3", "install": "^0.13.0", "jwt-decode": "3.1.2", "mime": "^4.0.1", "moment": "^2.24.0", "npm": "^6.14.8", "prop-types": "^15.7.2", "react": "18.2.0", "react-dom": "18.2.0", "react-lifecycles-compat": "^3.0.4", "react-native": "0.73.6", "react-native-app-intro-slider": "^4.0.0", "react-native-auth0": "^3.0.1", "react-native-base64": "^0.2.1", "react-native-calendar-picker": "^7.1.4", "react-native-config": "^1.5.1", "react-native-country-picker-modal": "^2.0.0", "react-native-event-listeners": "^1.0.7", "react-native-gesture-handler": "~2.14.0", "react-native-gifted-chat": "2.4.0", "react-native-grid-image-viewer": "^1.3.0", "react-native-image-picker": "^4.8.3", "react-native-indicators": "^0.17.0", "react-native-maps": "1.10.0", "react-native-modal-datetime-picker": "^13.1.0", "react-native-paper": "^4.12.8", "react-native-picker-select": "^8.0.4", "react-native-push-notification": "8.1.1", "react-native-raw-bottom-sheet": "3.0.0", "react-native-reanimated": "~3.6.2", "react-native-responsive-screen": "^1.4.1", "react-native-restart": "0.0.27", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-sectioned-multi-select": "https://github.com/jonno85/react-native-sectioned-multi-select.git", "react-native-simple-radio-button": "^2.7.4", "react-native-star-rating": "^1.1.0", "react-native-swipe-gestures": "^1.0.4", "react-native-switch-selector": "^2.1.4", "react-native-twitter-textview": "^1.0.7", "react-native-ui-stepper": "^1.2.4", "react-native-vector-icons": "^9.1.0", "react-phone-number-input": "^2.5.1", "react-redux": "^8.0.5", "redux": "^4.2.1", "redux-persist": "^6.0.0", "redux-promise": "^0.6.0", "redux-saga": "^1.2.3", "reduxsauce": "^1.2.1", "seamless-immutable": "^7.1.4", "shorthash": "0.0.2", "validate.js": "^0.13.1"}, "private": true, "version": "3.0.0", "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.24.7", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^2.0.0", "@react-native/babel-preset": "^0.73.21", "@react-native/eslint-config": "^0.73.2", "@react-native/metro-config": "^0.73.5", "@react-native/typescript-config": "0.73.1", "@react-navigation/devtools": "^6.0.18", "@redux-devtools/extension": "^3.2.6", "@testing-library/react-native": "^12.5.1", "@types/google.maps": "^3.55.5", "@types/jest": "^29.5.12", "@types/markerclustererplus": "^2.1.33", "@types/react": "~18.2.6", "@types/react-dom": "~18.0.10", "@types/react-native": "^0.72.1", "@types/react-native-star-rating": "^1.1.2", "@types/react-native-vector-icons": "^6.4.18", "@types/react-redux": "^7.1.30", "@types/react-test-renderer": "^18.0.0", "@types/redux-promise": "^0.5.29", "@types/seamless-immutable": "^7.1.16", "babel-jest": "^29.6.3", "babel-plugin-module-resolver": "^4.1.0", "babel-preset-expo": "^11.0.11", "babel-preset-react-native": "^4.0.1", "bplist-creator": "^0.1.1", "detox": "^20.22.2", "detox-recorder": "^1.0.151", "enzyme": "^3.11.0", "eslint": "^8.19.0", "jest": "^29.6.3", "jest-circus": "^26.6.3", "jest-expo": "50", "jetifier": "^1.6.6", "patch-package": "^7.0.2", "prettier": "^2.8.8", "react-devtools": "5.0.0", "react-native-clean-project": "4.0.1", "react-test-renderer": "18.1.0", "reactotron-core-client": "^2.9.3", "reactotron-react-native": "^5.0.0", "reactotron-redux": "^3.1.3", "reactotron-redux-saga": "^4.2.3", "redux-flipper": "^2.0.3", "ts-jest": "^29.1.5", "typescript": "^5.1.3"}, "detox": {"configurations": {"ios.sim.debug": {"binaryPath": "ios/build/Build/Products/Debug-iphonesimulator/autoinvito.app", "build": "xcodebuild -workspace ios/selfinvite.xcworkspace -UseNewBuildSystem=NO -scheme selfinvite -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build", "type": "ios.simulator", "device": {"id": "2F4A9AA5-45C8-4DCC-B860-0CCE1E53C980", "type": "iPhone 11", "name": "iPhone 11", "os": "iOS 14.3"}}, "ios.sim.release": {"binaryPath": "ios/build/Build/Products/Release-iphonesimulator/autoinvito.app", "build": "export RCT_NO_LAUNCH_PACKAGER=true && xcodebuild -workspace ios/selfinvite.xcworkspace -UseNewBuildSystem=NO -scheme selfinvite -configuration Release -sdk iphonesimulator -derivedDataPath ios/build", "type": "ios.simulator", "name": "iPhone 11"}, "android.sim.debug": {"binaryPath": "android/app/build/outputs/apk/debug/app-debug.apk", "build": "cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug && cd ..", "type": "android.emulator", "device": {"avdName": "Pixel_3a_API_29"}}, "android.sim.release": {"binaryPath": "android/app/build/outputs/apk/release/app-release.apk", "build": "cd android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release && cd ..", "type": "android.emulator", "device": {"avdName": "Pixel_3a_API_29"}}}, "test-runner": "jest"}, "packageManager": "yarn@3.6.4", "engines": {"node": ">=18"}}