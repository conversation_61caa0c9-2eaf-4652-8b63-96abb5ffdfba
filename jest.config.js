module.exports = {
  preset: 'jest-expo', //'react-native', //
  // preset: 'jest-expo',
  // transformIgnorePatterns: [
  // 'node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg|expo-modules-core)',
  // ],
  // transformIgnorePatterns: [
  //   'node_modules/(?!(@react-native/js-polyfills|@react-native/assets-registry|react-native|react-native-config|expo-constants|@react-native-community|expo|@expo/vector-icons|@expo|expo-[^/]+|@unimodules|unimodules|@unimodules-core|expo-modules-core)/)',
  // ],
  transformIgnorePatterns: [
    'node_modules/(?!(@react-native/js-polyfills|@react-native/assets-registry|react-native|react-native-[^/]+|react-native-config|expo-constants|@react-native-community|expo|@expo/vector-icons|@expo|expo-[^/]+|@unimodules|unimodules|@unimodules-core|expo-modules-core|react-native-responsive-screen|react-native-picker-select)/)',
  ],
  // [
  //   // 'react-native',
  //   [
  //     '@babel/preset-env',
  //     {
  //       targets: {
  //         node: 'current',
  //       },
  //     },
  //   ],
  //   ['jest-expo'],
  // ],
  setupFiles: [
    '<rootDir>/jest/setup.js',
    '<rootDir>/__mocks__/expo-mocks.js',
    '<rootDir>/__mocks__/fileMock.js',
    '<rootDir>/__mocks__/react-mocks.js',
  ],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': 'babel-jest',
  },
  moduleNameMapper: {
    '^@helpers/(.*)$': '<rootDir>/helpers/$1',
    '^@config/(.*)$': '<rootDir>/config/$1',
    '^@screens/(.*)$': '<rootDir>/screens/$1',
    '^@routes/(.*)$': '<rootDir>/routes/$1',
    '^@services/(.*)$': '<rootDir>/services/$1',
    '^@components/(.*)$': '<rootDir>/components/$1',
    '^@constants/(.*)$': '<rootDir>/constants/$1',
    '^@actions/(.*)$': '<rootDir>/_actions/$1',
    '^@reducers/(.*)$': '<rootDir>/_reducers/$1',
    '^@hooks/(.*)$': '<rootDir>/hooks/$1',
    '^@sagas/(.*)$': '<rootDir>/_sagas/$1',
    '^@assets/(.*)$': '<rootDir>/assets/$1',
    '^@translations/(.*)$': '<rootDir>/translations/$1',
    '\\.(jpg|jpeg|png|gif|webp|svg|ttf)$': '<rootDir>/__mocks__/fileMock.js',
  },
  collectCoverage: true,
  collectCoverageFrom: [
    '**/*.{js,jsx}',
    '!**/coverage/**',
    '!**/node_modules/**',
    '!**/babel.config.js',
    '!**/jest.setup.js',
  ],
};
